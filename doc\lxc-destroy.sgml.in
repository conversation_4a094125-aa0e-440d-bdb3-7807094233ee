<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-destroy</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-destroy</refname>

    <refpurpose>
      destroy a container.
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-destroy</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-f</arg>
      <arg choice="opt">-s</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-destroy</command> destroys the system object
      previously created by the <command>lxc-create</command> command.
    </para>

  </refsect1>

  <refsect1>

    <title>Options</title>

    <variablelist>
      <varlistentry>
	<term>
	  <option>-f, --force</option>
	</term>
	<listitem>
	  <para>
	    If a container is running, stop it first.  If this option is
	    not specified and the container is running, then
	    <command>lxc-destroy</command> will be aborted.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-s, --snapshots</option></term>
        <listitem>
          <para>
            destroy the specified container including all its snapshots.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Diagnostic</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
	    The specified container for destruction was not found. It
	    is probable it does not exists and was already
	    destroyed.You can use the <command>lxc-ls</command>
	    command to list the available containers on the system.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
