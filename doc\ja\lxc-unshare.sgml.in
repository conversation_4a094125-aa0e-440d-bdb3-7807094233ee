<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-unshare</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-unshare</refname>

    <refpurpose>
      <!--
      Run a task in a new set of namespaces.
      -->
      タスクの新しい名前空間の組の中ので実行
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-unshare</command>
      <arg choice="req">-s, --namespaces <replaceable>namespaces</replaceable></arg>
      <arg choice="opt">-u, --user <replaceable>user</replaceable></arg>
      <arg choice="opt">-H, --hostname <replaceable>hostname</replaceable></arg>
      <arg choice="opt">-i, --ifname <replaceable>ifname</replaceable></arg>
      <arg choice="opt">-d, --daemon</arg>
      <arg choice="opt">-M, --remount</arg>
      <arg choice="req">command</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      <command>lxc-unshare</command> can be used to run a task in a cloned set
      of namespaces.  This command is mainly provided for testing purposes.
      Despite its name, it always uses clone rather than unshare to create
      the new task with fresh namespaces.  Apart from testing kernel
      regressions this should make no difference.
      -->
      <command>lxc-unshare</command> はクローンされた名前空間の組の中でタスクを実行するのに使います。
      このコマンドは主にテスト目的で使います。
      このような名前であるにもかかわらず、このコマンドは常に、新しい名前空間で新しいタスクを作成するために unshare ではなく clone を使います。
      テスト中のカーネルの退行は別として、これで違いは生じないはずです。
    </para>

  </refsect1>

  <refsect1>

    <title><!-- Options -->オプション</title>

    <variablelist>

      <varlistentry>
	<term>
	  <option>-s, --namespaces <replaceable>namespaces</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the namespaces to attach to, as a pipe-separated list,
	    e.g. <replaceable>NETWORK|IPC</replaceable>. Allowed values are
	    <replaceable>MOUNT</replaceable>, <replaceable>PID</replaceable>,
	    <replaceable>UTSNAME</replaceable>, <replaceable>IPC</replaceable>,
	    <replaceable>USER </replaceable> and
	    <replaceable>NETWORK</replaceable>. This allows one to change
	    the context of the process to e.g. the network namespace of the
	    container while retaining the other namespaces as those of the
            host. (The pipe symbol needs to be escaped, e.g.
            <replaceable>MOUNT\|PID</replaceable> or quoted, e.g.
            <replaceable>"MOUNT|PID"</replaceable>.)
            -->
            アタッチする名前空間を、パイプでつなげたリストで指定します。
            例えば <replaceable>NETWORK|IPC</replaceable> のようにです。
            指定できる値は <replaceable>MOUNT</replaceable>、<replaceable>PID</replaceable>、<replaceable>UTSNAME</replaceable>、<replaceable>IPC</replaceable>、<replaceable>USER </replaceable>、<replaceable>NETWORK</replaceable> です。
            これにより、プロセスのコンテキストを変更することができます。
            例えば、コンテナのネットワーク名前空間だけを変更し、他の名前空間をホストのものと同じものに保ったままにするというようなことです。
            (パイプ記号を <replaceable>MOUNT\|PID</replaceable> のようにエスケー
プするか、<replaceable>"MOUNT|PID"</replaceable> のように引用符号を付ける必要が>あります。)
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-u, --user <replaceable>user</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify a user which the new task should become.
            -->
            新しいタスクを実行するユーザを指定します。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-H, --hostname <replaceable>hostname</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Set the hostname in the new container.  Only allowed if
	    the UTSNAME namespace is set.
            -->
            新しいコンテナ内でのホスト名を設定します。UTSNAME 名前空間を指定している時のみ有効です。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-i, --ifname <replaceable>interfacename</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Move the named interface into the container.  Only allowed
	    if the NETWORK namespace is set.  You may specify this
	    argument multiple times to move multiple interfaces into
	    container.
            -->
            指定したインターフェースをコンテナ内に移動させます。ネットワーク (NETWORK) 名前空間を指定している時のみ有効です。複数のインターフェースをコンテナに移動させるために複数回指定することも可能です。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-d, --daemon</option>
	</term>
	<listitem>
	  <para>
            <!--
	    Daemonize (do not wait for the container to exit before exiting)
            -->
            デーモンにします (コマンドはコンテナの終了を待ちません)。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-M, --remount</option>
	</term>
	<listitem>
	  <para>
            <!--
	    Mount default filesystems (/proc /dev/shm and /dev/mqueue)
	    in the container.  Only allowed if MOUNT namespace is set.
            -->
            コンテナ内でデフォルトのファイルシステム (/proc, /dev/shm, /dev/mqueue) をマウントします。マウント (MOUNT) 名前空間を指定している時のみ有効です。
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  <refsect1>
    <title><!-- Examples -->例</title>
      <para>
        <!--
        To spawn a new shell with its own UTS (hostname) namespace,
        <programlisting>
          lxc-unshare -s UTSNAME /bin/bash
        </programlisting>
	If the hostname is changed in that shell, the change will not be
	reflected on the host.
        -->
        自身の UTS(hostname)名前空間でシェルを起動するには以下のように実行します。
        <programlisting>
          lxc-unshare -s UTSNAME /bin/bash
        </programlisting>
        もし、そのシェル上でホスト名を変更しても、その変更はホストには反映されません。
      </para>
      <para>
        <!--
        To spawn a shell in a new network, pid, and mount namespace,
        <programlisting>
          lxc-unshare -s "NETWORK|PID|MOUNT" /bin/bash
        </programlisting>
	The resulting shell will have pid 1 and will see no network interfaces.
	After re-mounting /proc in that shell,
        <programlisting>
          mount -t proc proc /proc
        </programlisting>
	ps output will show there are no other processes in the namespace.
        -->
        新しいネットワーク、pid、マウント名前空間でシェルを起動するには以下のように実行します。
        <programlisting>
          lxc-unshare -s "NETWORK|PID|MOUNT" /bin/bash
        </programlisting>
        その結果起動するシェルは pid が 1 となり、ネットワークインターフェースがないでしょう。
        そのシェル上で /proc を再マウントした後
        <programlisting>
          mount -t proc proc /proc
        </programlisting>
        ps の出力は、その名前空間内には他のプロセスが存在しない事を表示するでしょう。
      </para>
      <para>
        <!--
        To spawn a shell in a new network, pid, mount, and hostname
        namespace.
        <programlisting>
          lxc-unshare -s "NETWORK|PID|MOUNT|UTSNAME" -M -H myhostname -i veth1 /bin/bash
        </programlisting>
        -->
        新しいネットワーク、PID、マウント、ホスト名 (UTS) 名前空間でシェルを起動するには、
        <programlisting>
          lxc-unshare -s "NETWORK|PID|MOUNT|UTSNAME" -M -H myhostname -i veth1 /bin/bash
        </programlisting>

        <!--
	The resulting shell will have pid 1 and will see two network
	interfaces (lo and veth1).  The hostname will be "myhostname" and
	/proc will have been remounted.  ps output will show there are
	no other processes in the namespace.
        -->
        起動したシェルは PID 1 を持ち、2 つのネットワークインターフェース (lo と veth1) を持ちます。
        ホスト名は "myhostname" となり、/proc は再マウントされます。ps コマンドは、名前空間内には他のプロセスがない状態を表示するでしょう。
      </para>
  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
