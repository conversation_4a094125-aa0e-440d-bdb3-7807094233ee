<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-info</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-info</refname>

    <refpurpose>
      query information about a container
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-info</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-c <replaceable>KEY</replaceable></arg>
      <arg choice="opt">-s</arg>
      <arg choice="opt">-p</arg>
      <arg choice="opt">-i</arg>
      <arg choice="opt">-S</arg>
      <arg choice="opt">-H</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>
    <para>
      <command>lxc-info</command> queries and shows information about a
      container.
    </para>
  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>

      <varlistentry>
        <term>
          <option>-c, --config <replaceable>KEY</replaceable></option>
        </term>
        <listitem>
          <para>
            Print a configuration key from the container. This option
            may be given multiple times to print out multiple key = value pairs.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-s, --state</option>
        </term>
        <listitem>
          <para>
            Just print the container's state.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-p, --pid</option>
        </term>
        <listitem>
          <para>
            Just print the container's pid.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-i, --ips</option>
        </term>
        <listitem>
          <para>
            Just print the container's IP addresses.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-S, --stats</option>
        </term>
        <listitem>
          <para>
            Just print the container's statistics.
            Note that for performance reasons the kernel does not account
            kernel memory use unless a kernel memory limit is set. If a limit
            is not set, <command>lxc-info</command> will display kernel memory
            use as 0. A limit can be set by specifying
            <programlisting>
            lxc.cgroup.memory.kmem.limit_in_bytes = <replaceable>number</replaceable>
            </programlisting>
            in your container configuration file, see
            <citerefentry>
              <refentrytitle>lxc.conf</refentrytitle>
              <manvolnum>5</manvolnum>
            </citerefentry>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-H, --no-humanize</option>
        </term>
        <listitem>
          <para>
            Print the container's statistics in raw, non-humanized form. The
            default is to print statistics in humanized form.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Examples</title>
    <variablelist>
      <varlistentry>
        <term>lxc-info -n foo</term>
        <listitem>
          <para>
            Show information for foo.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>lxc-info -n 'ubuntu.*'</term>
        <listitem>
          <para>
            Show information for all containers whose name starts with ubuntu.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>lxc-info -n foo -c lxc.net.0.veth.pair</term>
        <listitem>
          <para>
            prints the veth pair name of foo.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
