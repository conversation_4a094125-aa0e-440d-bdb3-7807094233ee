<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-execute</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-execute</refname>

    <refpurpose>
      run an application inside a container.
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-execute</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-d</arg>
      <arg choice="opt">-f <replaceable>config_file</replaceable></arg>
      <arg choice="opt">-s KEY=VAL</arg>
      <arg choice="opt">-u, --uid <replaceable>uid</replaceable></arg>
      <arg choice="opt">-g, --gid <replaceable>gid</replaceable></arg>
      <arg choice="opt">-- <replaceable>command</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-execute</command> runs the specified
      <replaceable>command</replaceable> inside the container
      specified by <replaceable>name</replaceable>.
    </para>
    <para>
      It will setup the container
      according to the configuration previously defined with the
      lxc-create command or with the configuration file parameter.
      If no configuration is
      defined, the default isolation is used.
    </para>
    <para>
      This command is mainly used when you want to quickly launch an
      application in an isolated environment.
    </para>
    <para>
      <command>lxc-execute</command> command will run the
      specified command into the container via an intermediate
      process, <command>lxc-init</command>.
      This lxc-init after launching  the specified command,
      will wait for its end and all other reparented processes.
      (to support daemons in the container).
      In other words, in the
      container, <command>lxc-init</command> has the pid 1 and the
      first process of the application has the pid 2.
     </para>
     <para>
      The above <command>lxc-init</command> is designed to forward received
      signals to the started command.
     </para>
  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>

      <varlistentry>
	<term>
	  <option>-f, --rcfile <replaceable>config_file</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify the configuration file to configure the virtualization
	    and isolation functionalities for the container.
	  </para>
	  <para>
	   This configuration file if present will be used even if there is
	   already a configuration file present in the previously created
	   container (via lxc-create).
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term>
	  <option>-s, --define <replaceable>KEY=VAL</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Assign value <replaceable>VAL</replaceable> to configuration
	    variable <replaceable>KEY</replaceable>. This overrides any
	    assignment done in <replaceable>config_file</replaceable>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-d, --daemon</option>
	</term>
	<listitem>
	  <para>
	    Run the container as a daemon. As the container has no
	    more tty, if an error occurs nothing will be displayed,
	    the log file can be used to check the error.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-u, --uid <replaceable>uid</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Executes the <replaceable>command</replaceable> with user ID (use numerical value)
	    <replaceable>uid</replaceable> inside the container.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--g, --gid <replaceable>gid</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Executes the <replaceable>command</replaceable> with group ID (use numerical value)
	    <replaceable>gid</replaceable> inside the container.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term><option>--</option></term>
	<listitem>
	  <para>
	    Signal the end of options and disables further option
	    processing. Any arguments after the -- are treated as
	    arguments to <replaceable>command</replaceable>.
	  </para>
	  <para>
	    This option is useful when you want specify options
	    to <replaceable>command</replaceable> and don't want
	    <command>lxc-execute</command> to interpret them.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Diagnostic</title>

    <variablelist>

      <varlistentry>
        <term>The container is busy</term>
        <listitem>
          <para>
	    The specified container is already running an
	    application. You should stop it before reuse this
	    container or create a new one.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
