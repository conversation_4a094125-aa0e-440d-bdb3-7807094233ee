<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-checkpoint</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-checkpoint</refname>

    <refpurpose>
      checkpoint a container
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-checkpoint</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="req">-D <replaceable>PATH</replaceable></arg>
      <arg choice="opt">-r</arg>
      <arg choice="opt">-s</arg>
      <arg choice="opt">-v</arg>
      <arg choice="opt">-d</arg>
      <arg choice="opt">-F</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>
    <para>
      <command>lxc-checkpoint</command> checkpoints and restores containers.
    </para>
  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>

      <varlistentry>
        <term>
          <option>-r, --restore</option>
        </term>
        <listitem>
          <para>
            Restore the checkpoint for the container, instead of dumping it.
            This option is incompatible with <option>-s</option>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-D <replaceable>PATH</replaceable>, --checkpoint-dir=<replaceable>PATH</replaceable></option>
        </term>
        <listitem>
          <para>
            The directory to dump the checkpoint metadata.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-s, --stop</option>
        </term>
        <listitem>
          <para>
            Optionally stop the container after dumping. This option is
            incompatible with <option>-r</option>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-v, --verbose</option>
        </term>
        <listitem>
          <para>
            Enable verbose criu logging.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-d, --daemon</option>
        </term>
        <listitem>
          <para>
            Restore the container in the background (this is the default).
            Only available when providing <option>-r</option>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-F, --foreground</option>
        </term>
        <listitem>
          <para>
            Restore the container in the foreground. Only available when
            providing <option>-r</option>.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Examples</title>
    <variablelist>

      <varlistentry>
        <term>lxc-checkpoint -n foo -D /tmp/checkpoint</term>
        <listitem>
          <para>
            Checkpoint the container foo into the directory /tmp/checkpoint.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>lxc-checkpoint -r -n foo -D /tmp/checkpoint</term>
        <listitem>
          <para>
            Restore the checkpoint from the directory /tmp/checkpoint.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
