#!/bin/bash

# SPDX-License-Identifier: LGPL-2.1+

# lxc: linux Container library
#
# This is a test for dependency between snapshots
#
# When container c2 is created as an overlayfs clone of c1, then
# we record it as such, because c1 cannot be deleted until c2 is
# deleted.  Once c2 is deleted, c1 should be delete-able.

# This test assumes an Ubuntu host

set -e

modprobe -v overlay || true
if ! grep -q overlay /proc/filesystems; then
	echo "Not running this test as overlay is not available"
	exit 0
fi

cleanup() {
	for i in $(seq 1 20); do
		lxc-destroy -n snapdeptest$i > /dev/null 2>&1 || true
	done
	lxc-destroy -n snapdeptest > /dev/null 2>&1 || true
}

verify_deps() {
	n=$1
	m=$(wc -l /var/lib/lxc/snapdeptest/lxc_snapshots | awk '{ print $1 }')
	[ $((n*2)) -eq $m ]
}

cleanup

trap cleanup EXIT SIGHUP SIGINT SIGTERM

lxc-create -t busybox -n snapdeptest
lxc-copy -s -n snapdeptest -N snapdeptest1
fail=0
lxc-destroy -n snapdeptest || fail=1
if [ $fail -eq 0 ]; then
	echo "FAIL: clone did not prevent deletion"
	false
fi

for i in $(seq 2 20); do
	lxc-copy -s -n snapdeptest -N snapdeptest$i
done

verify_deps 20

lxc-destroy -n snapdeptest1

verify_deps 19

lxc-destroy -n snapdeptest20

verify_deps 18

for i in $(seq 2 19); do
	lxc-destroy -n snapdeptest$i
done

lxc-destroy -n snapdeptest

echo "Snapshot clone dependency test passed"
exit 0
