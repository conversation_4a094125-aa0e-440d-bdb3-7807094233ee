<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>pam_cgfs</refentrytitle>
    <manvolnum>8</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>pam_cgfs</refname>

    <refpurpose>
      <!--
      cgroup management for unprivileged LXC containers.
        -->
      非特権の LXC コンテナのための cgroup を管理する
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>pam_cgfs.so</command>
      <arg choice="req">-c <replaceable>kernel_controller,name=named_controller</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      LXC has supported fully unprivileged containers since LXC 1.0.
      Fully unprivileged containers are the safest containers and are run by
      normal (non-root) users.  This is achieved by using user namespaces by
      mapping between a range of UIDs and GIDs on the host to a different
      (unprivileged) range of UIDs and GIDs in the container. That means the uid
      0 (root) in the container is mapped to an unprivileged user id (something
      like 1000000) outside of the container and only has rights on resources
      that it owns itself.
      -->
      LXC は LXC 1.0 以降、完全な非特権コンテナをサポートしてきました。完全な非特権コンテナは安全なコンテナであり、一般ユーザ（非 root）で実行できます。これはホスト上の特権のない UID と GID の範囲を、それとは異なるコンテナ内の UID と GID の範囲にマッピングするユーザ名前空間を使うことで実現しています。これは、コンテナ内の UID 0 (root) が、コンテナの外では 1000000 のような特権のないユーザ ID にマッピングされ、自身の所有するリソースに対してのみ権限を有することを意味します。
    </para>
    <para>
      <!--
      Cgroup management of fully unprivileged containers means restricting the
      resources used by these containers like limiting the CPU usage of a
      container, or the number of processes it is allowed to spawn, or the
      memory it is allowed to consume. It is clear that the fully
      unprivileged containers are run by normal users and there is a need to
      limit and manage resource consumption among the containers.
      But unprivileged cgroup management is not easy with most init systems.
      So, the pam_cgfs.so came into existence.
      -->
      完全な非特権コンテナの cgroup 管理は、このようなコンテナが使用するリソースを制限するということです。例えば、コンテナの CPU 使用量を制限したり、コンテナ内で起動するプロセスの数を制限したり、コンテナが消費できるメモリを制限したりということです。
      完全な非特権コンテナは一般ユーザによって実行され、コンテナ間のリソース消費を制限、管理する必要があるということは明らかです。
      しかし、非特権での cgroup 管理はほとんどの init システムでは簡単ではありません。
      そこで、pam_cgfs.so が誕生したのです。
    </para>

    <para>
      <!--
      The <command>pam_cgfs.so</command> module can handle pure cgroupfs v1
      (<filename>/sys/fs/cgroup/$controller</filename>) and mixed mounts,
      where some controllers are mounted in a standard cgroupfs v1 hierarchy
      (<filename>/sys/fs/cgroup/$controller</filename>) and others in
      cgroupfs v2 hierarchy (<filename>/sys/fs/cgroup/unified</filename>).
      Writeable cgroups are either created for all controllers or, if specified,
      for only controllers listed as arguments on the command line. 
      Pure cgroup v2 mount is not covered by the pam_cgfs.so module.
	-->
      <command>pam_cgfs.so</command> モジュールは、純粋な cgroupfs v1 (<filename>/sys/fs/cgroup/$controller</filename>) ツリーと、コントローラのいくつかが cgroupfs v1 ツリー (<filename>/sys/fs/cgroup/$controller</filename>) で、それ以外が cgroupfs v2 (<filename>/sys/fs/cgroup/unified</filename>) ツリーと言ったようなミックスマウントを扱えます。
      書き込み可能な cgroup がすべてのコントローラ用に作られます。また、引数で指定すれば、指定したコントローラのみ書き込み可能な cgroup が作られます。
      純粋な cgroup v2 のみのマウントは pam_cgfs.so モジュールでは対象外です。
    </para>

    <para>
      <!--
      The cgroup created <filename>user/$user/n</filename> will be for the nth
      session under cgroup kernel controller hierarchy.
      -->
      作成された cgroup <filename>user/$user/n</filename> は cgroup カーネルコントローラ階層配下の n 番目のセッション用です。
    </para>

    <para>
      <!--
      Systems with a systemd init system are treated specifically, both with
      respect to cgroupfs v1 and cgroupfs v2. For  both, cgroupfs v1 and
      cgroupfs v2, the module checks whether systemd already placed the user in
      a cgroup it created <filename>user.slice/user-$uid/session-n.scope
      </filename> by checking whether $uid == login uid. If so, the login
      user chown the <filename>session-n.scope</filename>, else a cgroup is
      created as outlined above (<filename>user/$user/n</filename>) and chown it
      to login uid. If the init system has already placed the login user inside
      a session specific group, the <command>pam_cgfs.so</command> module is
      smart enough to detect it and re-use the cgroup.
      -->
      init システムが systemd であるシステムは、cgroupfs v1 と v2 の両方が特別に扱われます。cgroupfs v1 と v2 の両方に対して、このモジュールは systemd が既に、<filename>user.slice/user-$uid/session-n.scope</filename> を cgroup 内に作成しているかどうか、$uid == login uid であるかどうかをチェックします。もし $uid == login uid であるなら、<filename>session-n.scope</filename> をユーザに chown します。そうでなければ、前述のような cgroup (<filename>user/$user/n</filename>) が作成され、ログイン uid で chown されます。
      もし、init システムがすでにセッション特有のグループ内にログインユーザーの cgroup を配置しているなら、<command>pam_cgfs.so</command> モジュールはそれを検出して再利用する機能を持っています。
    </para>

    <para>
      <!--
      In essence,  the <command>pam_cgfs.so</command> module takes care of
      placing unprivileged (non-root) users into writable cgroups at login
      and also cleaning up these cgroup hierarchies on logout, so they are free
      to delegate resources to containers as needed that have been provided to
      them.
        -->
      基本的には <command>pam_cgfs.so</command> モジュールは、ログイン時に非特権（非 root）ユーザが書き込みできる cgroup を配置し、ログアウト時にもその cgroup ツリーをクリーンアップする処理を行います。したがって、必要に応じて提供されているリソースをコンテナに自由に委譲できます。
    </para>

  </refsect1>

  <refsect1>

    <title><!-- Options -->オプション</title>

    <variablelist>

          <varlistentry>
            <term> <option>-c <replaceable>controller-list</replaceable></option> </term>
           <listitem>
             <para>
               <!--
        Takes a string argument which sets the list of kernel controllers and
        named controllers delimited by commas in-between “,”. Named controllers
        need to be specified in the form “name=$namedcontroller”. Can use “all”
        enable all cgroup resource controller hierarchies. Specifying “all” and
        other controllers explicitly returns PAM_SESSION_ERR.
                 -->
               カンマ（","）で区切られたカーネルコントローラと名前付きコントローラ（訳注: mount オプションとして name オプションで名前を指定してマウントした cgroup v1 ツリー）のリストを設定した文字列の引数を取ります。名前付きコントローラは "name=$namedcontroller" の形で指定する必要があります。"all" を指定して、すべての cgroup リソースコントローラーのツリーを有効にできます。"all" と他のコントローラを同時に指定すると、明確に PAM_SESSION_ERR が返ります。
             </para>
           </listitem>
          </varlistentry>

    </variablelist>

  </refsect1>

  <refsect1>
    <title>Module types provided</title>
    <para>
      <!--
      Only <option>session</option> module type is provided (and needed).
        -->
      モジュールタイプとして <option>session</option> のみが提供されます（そして必要です）。
    </para>
  </refsect1>

  <refsect1>

    <title><!-- Return Values -->返り値</title>

    <variablelist>

      <varlistentry>
        <term>PAM_SUCCESS</term>
        <listitem>
          <para>
            <!--
            Writeable cgroups have been created for the user.
              -->
            ユーザ用の書き込み可能な cgroup が作成されました。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>PAM_SESSION_ERR</term>
        <listitem>
          <para>
            <!--
            Failed to create writable cgroups for the user.
              -->
            ユーザ用の書き込み可能な cgroup の作成が失敗しました。
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  <refsect1>

    <title><!-- Files -->ファイル</title>

    <variablelist>

      <varlistentry>
        <term>/etc/pam.d/common-session{,-noninteractive}</term>
        <listitem>
          <para>
            <!--
            Default configuration is added at the end of these files.
              -->
            これらのファイルの最後にデフォルト設定が追加されます。
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  <refsect1>
    <title><!-- EXAMPLE -->例</title>
    <!--
    <programlisting>
session optional        pam_cgfs.so -c freezer,memory,named=systemd
# default configuration
# user writable cgroups are created under freezer, memory and named cgroup systemd hierarchies.
# /sys/fs/cgroup/$controller/user/$user/n for freezer,memory.
# /sys/fs/cgroup/systemd/user.slice/user-$uid/session-n.scope for systemd.

session optional        pam_cgfs.so -c all
# user writable cgroups are created under all cgroup controllers.

session optional        pam_cgfs.so -c all,memory,freezer
# invalid argument and returns PAM_SESSION_ERR
    </programlisting>
    -->
    <programlisting>
session optional        pam_cgfs.so -c freezer,memory,named=systemd
# デフォルト設定
# ユーザが書き込み可能な cgroup が freezer, memory, 名前付き cgroup "systemd" ツリー以下に作成されます。
# freezer と memory に対しては /sys/fs/cgroup/$controller/user/$user/n
# systemd に対しては /sys/fs/cgroup/systemd/user.slice/user-$uid/session-n.scope

session optional        pam_cgfs.so -c all
# すべての cgroup コントローラ以下にユーザが書き込み可能な cgroup が作成されます

session optional        pam_cgfs.so -c all,memory,freezer
# 不正な引数なので PAM_SESSION_ERR が返ります
    </programlisting>
  </refsect1>

  <refsect1>
    <title>See Also</title>

    <simpara>
      <citerefentry>
      <refentrytitle><command>lxc-cgroup</command></refentrytitle>
      <manvolnum>1</manvolnum>
      </citerefentry>,

      <citerefentry>
      <refentrytitle><command>cgroups</command></refentrytitle>
      <manvolnum>7</manvolnum>
      </citerefentry>,

      <citerefentry>
      <refentrytitle><command>user_namespaces</command></refentrytitle>
      <manvolnum>7</manvolnum>
      </citerefentry>,

      <citerefentry>
      <refentrytitle><command>namespaces</command></refentrytitle>
      <manvolnum>7</manvolnum>
      </citerefentry>,

      <citerefentry>
      <refentrytitle><command>pam</command></refentrytitle>
      <manvolnum>8</manvolnum>
      </citerefentry>
    </simpara>


  </refsect1>

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
