<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc.system.conf</refentrytitle>
    <manvolnum>5</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc.system.conf</refname>

    <refpurpose>
      <!--
      LXC system configuration file
      -->
      LXC 시스템 설정파일
    </refpurpose>
  </refnamediv>

  <refsect1>
    <title><!-- Description -->설명</title>

    <para>
      <!--
      The system configuration is located at
      <filename>@LXC_GLOBAL_CONF@</filename> or
      <filename>~/.config/lxc/lxc.conf</filename> for unprivileged
      containers.
      -->
      시스템 설정은 <filename>@LXC_GLOBAL_CONF@</filename>에 위치하고 있다. 비
특권 컨테이너의 경우는 <filename>~/.config/lxc/lxc.conf</filename>에 위치하고 있
다.
    </para>

    <para>
      <!--
      This configuration file is used to set values such as default
      lookup paths and storage backend settings for LXC.
      -->
      이 설정파일은 LXC 기본 경로 및 저장소 백엔드 설정과 같은 값들을 설정할 때 사용한다.
    </para>

    <refsect2>
      <title><!-- Configuration paths -->경로 설정</title>

      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.lxcpath</option>
          </term>
          <listitem>
            <para>
              <!--
              The location in which all containers are stored.
              -->
              모든 컨테이너들이 저장되는 장소.
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.default_config</option>
          </term>
          <listitem>
            <para>
              <!--
              The path to the default container configuration.
              -->
              컨테이너의 기본 설정파일 경로.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Control Groups -->컨트롤 그룹</title>

      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.cgroup.use</option>
          </term>
          <listitem>
            <para>
              <!--
              Comma separated list of cgroup controllers to setup.
              If none is specified, all available controllers will be used.
              -->
              사용할 cgroup 컨트롤러의 쉼표(,)로 구분된 목록.
              아무것도 지정하지 않았다면, 사용가능한 컨트롤러 전체를 사용될 것이다.
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cgroup.pattern</option>
          </term>
          <listitem>
            <para>
              <!--
              Format string used to generate the cgroup path (e.g. lxc/%n).
              -->
              컨테이너용 cgroup을 생성할 때 사용하는 포맷 문자열 (예 : lxc/%n).
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>LVM</title>

      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.bdev.lvm.vg</option>
          </term>
          <listitem>
            <para>
              <!--
              Default LVM volume group name.
              -->
              기본 LVM 볼륨 그룹 이름
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.bdev.lvm.thin_pool</option>
          </term>
          <listitem>
            <para>
              <!--
              Default LVM thin pool name.
              -->
              기본 LVM thin pool 이름
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>ZFS</title>

      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.bdev.zfs.root</option>
          </term>
          <listitem>
            <para>
              <!--
              Default ZFS root name.
              -->
              기본 ZFS root 이름.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>
  </refsect1>

  <refsect1>
    <simpara>
      <citerefentry>
        <refentrytitle><command>lxc</command></refentrytitle>
        <manvolnum>1</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle><command>lxc.container.conf</command></refentrytitle>
        <manvolnum>5</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle><command>lxc.system.conf</command></refentrytitle>
        <manvolnum>5</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle><command>lxc-usernet</command></refentrytitle>
        <manvolnum>5</manvolnum>
      </citerefentry>
    </simpara>
  </refsect1>

  &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
