2
allowlist trap
# 'allowlist' would normally mean kill a task doing any syscall which is not
# allowlisted below.  By appending 'trap' to the line, we will cause a SIGSYS
# to be sent to the task instead.  'errno 0' would  mean don't allow the system
# call but immediately return 0.  'errno 22' would mean return EINVAL immediately.
[x86_64]
open
close
read
write
mount
umount2
# Since we are listing system calls by name, we can also ask to have them resolved
# for another arch, i.e. for 32/64-bit versions.
[x86]
open
close
read
write
mount
umount2
# Do note that this policy does not allowlist enough system calls to allow a
# system container to boot.
