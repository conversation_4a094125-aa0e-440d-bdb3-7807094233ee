<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-start</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-start</refname>

    <refpurpose>
      run an application inside a container.
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-start</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-f <replaceable>config_file</replaceable></arg>
      <arg choice="opt">-c <replaceable>console_device</replaceable></arg>
      <arg choice="opt">-L <replaceable>console_logfile</replaceable></arg>
      <arg choice="opt">-d</arg>
      <arg choice="opt">-F</arg>
      <arg choice="opt">-p <replaceable>pid_file</replaceable></arg>
      <arg choice="opt">-s KEY=VAL</arg>
      <arg choice="opt">-C</arg>
      <arg choice="opt">--share-[net|ipc|uts] <replaceable>name|pid</replaceable></arg>
      <arg choice="opt">command</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-start</command> runs the specified
      <replaceable>command</replaceable> inside the container
      specified by <replaceable>name</replaceable>.
    </para>
    <para>
      It will setup the container
      according to the configuration previously defined with the
      lxc-create command or with the configuration file parameter.
      If no configuration is
      defined, the default isolation is used.
    </para>
    <para>
      If no command is specified, <command>lxc-start</command> will
      use the command defined in lxc.init.cmd or if not set, the default
      <command>"/sbin/init"</command> command to run a system
      container.
    </para>

  </refsect1>

  <refsect1>

    <title>Options</title>

    <variablelist>

      <varlistentry>
	<term>
	  <option>-d, --daemon</option>
	</term>
	<listitem>
	  <para>
	    Run the container as a daemon. As the container has no
	    more tty, if an error occurs nothing will be displayed,
	    the log file can be used to check the error. (This is the default mode)
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-F, --foreground</option>
	</term>
	<listitem>
	  <para>
	    Run the container in the foreground. In this mode, the container
	    console will be attached to the current tty and signals will be routed
	    directly to the container.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-p, --pidfile <replaceable>pid_file</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Create a file with the process id.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-f, --rcfile <replaceable>config_file</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify the configuration file to configure the virtualization
	    and isolation functionalities for the container.
	  </para>
	  <para>
	   This configuration file if present will be used even if there is
	   already a configuration file present in the previously created
	   container (via lxc-create).
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-c,
	  --console <replaceable>console_device</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify a device to use for the container's console, for example
            /dev/tty8. If this option is not specified the current terminal
            will be used unless <option>-d</option> is specified.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-L,
	  --console-log <replaceable>console_logfile</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify a file to log the container's console output to.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-s, --define <replaceable>KEY=VAL</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Assign value <replaceable>VAL</replaceable> to configuration
	    variable <replaceable>KEY</replaceable>. This overrides any
	    assignment done in <replaceable>config_file</replaceable>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-C,
	  --close-all-fds</option>
	</term>
	<listitem>
	  <para>
	  If any file descriptors are inherited, close them.  If this option
	  is not specified, then <command>lxc-start</command> will exit with
	  failure instead. Note: <replaceable>--daemon</replaceable> implies
	  <replaceable>--close-all-fds</replaceable>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--share-net <replaceable>name|pid</replaceable></option>
	</term>
	<listitem>
	  <para>
            Inherit a network namespace from
	    a <replaceable>name</replaceable> container or
	    a <replaceable>pid</replaceable>. The network namespace
	    will continue to be managed by the original owner. The
	    network configuration of the starting container is ignored
	    and the up/down scripts won't be executed.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--share-ipc <replaceable>name|pid</replaceable></option>
	</term>
	<listitem>
	  <para>
            Inherit an IPC namespace from
	    a <replaceable>name</replaceable> container or
	    a <replaceable>pid</replaceable>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--share-uts <replaceable>name|pid</replaceable></option>
	</term>
	<listitem>
	  <para>
            Inherit a UTS namespace from
	    a <replaceable>name</replaceable> container or
	    a <replaceable>pid</replaceable>. The starting LXC will
	    not set the hostname, but the container OS may do it
	    anyway.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Diagnostic</title>

    <variablelist>

      <varlistentry>
        <term>The container is busy</term>
        <listitem>
          <para>
	    The specified container is already running an
	    application. You should stop it before reuse this
	    container or create a new one.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
