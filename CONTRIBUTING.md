Contributing to this project
----------------------------

This project accepts contributions. In order to contribute, you should
pay attention to a few things:

    1 - your code must follow the coding style rules
    2 - the format of the submission must be GitHub pull requests
    3 - your work must be signed


Coding Style:
-------------

The LXC project generally follows the Linux kernel coding style.  However there
are a few differences, these are outlined it CODING_STLYE.md

The Linux kernel coding style guide can be found within the kernel tree:

	Documentation/process/coding-style.rst

It can be accessed online too:

https://www.kernel.org/doc/html/latest/process/coding-style.html

Submitting Modifications:
-------------------------

The contributions must be GitHub pull requests.

Licensing for new files:
------------------------

LXC is made of files shipped under a few different licenses.

Anything that ends up being part of the LXC library needs to be released
under LGPLv2.1+ or a license compatible with it (though the latter will
only be accepted for cases where the code originated elsewhere and was
imported into LXC).

Language bindings for the libraries need to be released under LGPLv2.1+.

Anything else (non-libraries) needs to be Free Software and needs to be
allowed to link with LGPLv2.1+ code (if needed). LXC upstream prefers
LGPLv2.1+ or GPLv2 for those.

When introducing a new file into the project, please make sure it has a
copyright header making clear under which license it's being released
and if it doesn't match the criteria described above, please explain
your decision on the lxc-devel mailing-list when submitting your patch.

Developer Certificate of Origin:
--------------------------------

To improve tracking of contributions to this project we will use a
process modeled on the modified DCO 1.1 and use a "sign-off" procedure.

The sign-off is a simple line at the end of the explanation for the
patch, which certifies that you wrote it or otherwise have the right
to pass it on as an open-source patch.  The rules are pretty simple:
if you can certify the below:

By making a contribution to this project, I certify that:

(a) The contribution was created in whole or in part by me and I have
    the right to submit it under the open source license indicated in
    the file; or

(b) The contribution is based upon previous work that, to the best of
    my knowledge, is covered under an appropriate open source License
    and I have the right under that license to submit that work with
    modifications, whether created in whole or in part by me, under
    the same open source license (unless I am permitted to submit
    under a different license), as indicated in the file; or

(c) The contribution was provided directly to me by some other person
    who certified (a), (b) or (c) and I have not modified it.

(d) The contribution is made free of any other party's intellectual
    property claims or rights.

(e) I understand and agree that this project and the contribution are
    public and that a record of the contribution (including all
    personal information I submit with it, including my sign-off) is
    maintained indefinitely and may be redistributed consistent with
    this project or the open source license(s) involved.


then you just add a line saying

    Signed-off-by: Random J Developer <<EMAIL>>

You can do it by using option -s or --signoff when you commit

    git commit --signoff ...

using your real name (sorry, no pseudonyms or anonymous contributions.)

In addition we support the following DCOs which maintainers can use to indicate
that a patch is acceptable:

    Acked-by: Random J Developer <<EMAIL>>
    Reviewed-by: Random J Developer <<EMAIL>>

If you are contributing as a group who is implementing a feature together such
that it cannot be reasonably attributed to a single developer please use:

    Co-developed-by: Random J Developer 1 <<EMAIL>>
    Co-developed-by: Random J Developer 2 <<EMAIL>>

AI Generated Code:
------------------

Substantially AI generated code is not welcome.  There are several
reasons for this.  First, it violates the "The contribution was created
in whole or in part by me" statement of DCO.  Second, the licensing
implications are not yet clear.  Thirdly, we expect anyone who submits
code to fully understand what they are submitting.  Finally, we put
a lot of time into reviewing patch submissions.  Increasing the
volume of code to be reviewed with autogenerated boilerplate drivel
will take away time from more important reviews.
