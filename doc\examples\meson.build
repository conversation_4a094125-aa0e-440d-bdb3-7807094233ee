# SPDX-License-Identifier: LGPL-2.1+

example_lxc_complex = configure_file(
    configuration: dummy_config_data,
    input: 'lxc-complex.conf.in',
    output: 'lxc-complex.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_empty_netns = configure_file(
    configuration: dummy_config_data,
    input: 'lxc-empty-netns.conf.in',
    output: 'lxc-empty-netns.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_macvlan = configure_file(
    configuration: dummy_config_data,
    input: 'lxc-macvlan.conf.in',
    output: 'lxc-macvlan.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_no_netns = configure_file(
    configuration: dummy_config_data,
    input: 'lxc-no-netns.conf.in',
    output: 'lxc-no-netns.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_phys = configure_file(
    configuration: dummy_config_data,
    input: 'lxc-phys.conf.in',
    output: 'lxc-phys.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_veth = configure_file(
    configuration: dummy_config_data,
    input: 'lxc-veth.conf.in',
    output: 'lxc-veth.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_vlan = configure_file(
    configuration: dummy_config_data,
    input: 'lxc-vlan.conf.in',
    output: 'lxc-vlan.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_seccomp_v1 = configure_file(
    configuration: dummy_config_data,
    input: 'seccomp-v1.conf',
    output: 'seccomp-v1.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_seccomp_v2 = configure_file(
    configuration: dummy_config_data,
    input: 'seccomp-v2.conf',
    output: 'seccomp-v2.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)

example_lxc_seccomp_v2 = configure_file(
    configuration: dummy_config_data,
    input: 'seccomp-v2-denylist.conf',
    output: 'seccomp-v2-denylist.conf',
    install: want_examples,
    install_dir: lxcexamplesdir)
