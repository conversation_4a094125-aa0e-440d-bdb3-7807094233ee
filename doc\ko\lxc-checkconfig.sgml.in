<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-checkconfig</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-checkconfig</refname>

    <refpurpose>
      <!--
      check the current kernel for lxc support
      -->
      현재 커널의 lxc 지원 여부 검사
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-checkconfig</command>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->설명</title>
    <para>
      <!--
      <command>lxc-checkconfig</command> check the current kernel for
      lxc support
      -->
      <command>lxc-checkconfig</command>는 현재 커널이 lxc를 지원하는지 검사한다.
    </para>
  </refsect1>

  <refsect1>
    <title><!-- Examples -->예제</title>
    <variablelist>
      <varlistentry>
        <term>lxc-checkconfig</term>
        <listitem>
        <para>
          <!--
          check the current kernel.
          CONFIG can be set in the environment to an alternate location.
          -->
          현재 커널을 검사한다.
          CONFIG 환경 변수를 이용하여 다른 위치를 설정할 수 있다.
          (역주 : 기본값은 /proc/config.gz 이다. Kernel compile option에서 Enable access to .config through /proc/config.gz를 체크하여야 한다)
        </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
