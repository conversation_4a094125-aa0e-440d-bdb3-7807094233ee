<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-copy</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-copy</refname>

    <refpurpose>
      copy an existing container.
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-copy</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="opt">-P, --lxcpath <replaceable>path</replaceable></arg>
      <arg choice="req">-N, --newname <replaceable>newname</replaceable></arg>
      <arg choice="opt">-p, --newpath <replaceable>newpath</replaceable></arg>
      <arg choice="opt">-B, --backingstorage <replaceable>backingstorage</replaceable></arg>
      <arg choice="opt">-s, --snapshot</arg>
      <arg choice="opt">-a, --allowrunning</arg>
      <arg choice="opt">-K, --keepname</arg>
      <arg choice="opt">-D, --keepdata</arg>
      <arg choice="opt">-M, --keepmac</arg>
      <arg choice="opt">-L, --fssize <replaceable>size [unit]</replaceable></arg>
      <arg choice="opt">-- hook arguments</arg>
    </cmdsynopsis>
    <cmdsynopsis>
      <command>lxc-copy</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="opt">-P, --lxcpath <replaceable>path</replaceable></arg>
      <arg choice="opt">-N, --newname <replaceable>newname</replaceable></arg>
      <arg choice="opt">-p, --newpath <replaceable>newpath</replaceable></arg>
      <arg choice="req">-e, --ephemeral</arg>
      <arg choice="opt">-B, --backingstorage <replaceable>backingstorage</replaceable></arg>
      <arg choice="opt">-s, --snapshot</arg>
      <arg choice="opt">-a, --allowrunning</arg>
      <arg choice="opt">-K, --keepname</arg>
      <arg choice="opt">-D, --keepdata</arg>
      <arg choice="opt">-M, --keepmac</arg>
      <arg choice="opt">-L, --fssize <replaceable>size [unit]</replaceable></arg>
      <arg choice="opt">-- hook arguments</arg>
    </cmdsynopsis>
    <cmdsynopsis>
      <command>lxc-copy</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="opt">-P, --lxcpath <replaceable>path</replaceable></arg>
      <arg choice="opt">-N, --newname <replaceable>newname</replaceable></arg>
      <arg choice="opt">-p, --newpath <replaceable>newpath</replaceable></arg>
      <arg choice="req">-e, --ephemeral</arg>
      <arg choice="opt">-B, --backingstorage <replaceable>backingstorage</replaceable></arg>
      <arg choice="opt">-s, --snapshot</arg>
      <arg choice="opt">-t, --tmpfs</arg>
      <arg choice="opt">-K, --keepname</arg>
      <arg choice="opt">-M, --keepmac</arg>
      <arg choice="opt">-- hook arguments</arg>
    </cmdsynopsis>
    <cmdsynopsis>
      <command>lxc-copy</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="opt">-P, --lxcpath <replaceable>path</replaceable></arg>
      <arg choice="req">-N, --newname <replaceable>newname</replaceable></arg>
      <arg choice="opt">-p, --newpath <replaceable>newpath</replaceable></arg>
      <arg choice="req">-R, --rename</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-copy</command> creates and optionally starts (ephemeral or
      non-ephemeral) copies of existing containers.
    </para>
    <para>
      <command>lxc-copy</command> creates copies of existing containers. Copies
      can be complete clones of the original container. In this case the whole
      root filesystem of the container is simply copied to the new container. Or
      they can be snapshots, i.e. small copy-on-write copies of the original
      container. In this case the specified backing storage for the copy must
      support snapshots. This currently includes btrfs, lvm (lvm devices
      do not support snapshots of snapshots.), overlay, and zfs.
    </para>
      
    <para>
    The copy's backing storage will be of the same type as the original
    container. overlay snapshots of directory backed containers are
    exempted from this rule.
    </para>

    <para>
    When the <replaceable>-e</replaceable> flag is specified an ephemeral
    snapshot of the original container is created and started. Ephemeral
    containers will have <command>lxc.ephemeral = 1</command> set in their
    config file and will be destroyed on shutdown. When
    <replaceable>-e</replaceable> is used in combination with
    <replaceable>-D</replaceable> a non-ephemeral snapshot of the original
    container is created and started.
    Ephemeral containers can also be placed on a tmpfs with <replaceable>-t</replaceable>
    flag. NOTE: If an ephemeral container that is placed on a tmpfs is rebooted
    all changes made to it will currently be lost!
    </para>

    <para>
    When <replaceable>-e</replaceable> is specified and no newname is given via
    <replaceable>-N</replaceable> a random name for the snapshot will be chosen.
    </para>

    <para>
    Containers created and started with <replaceable>-e</replaceable> can have
    custom mounts. These are specified with the <replaceable>-m</replaceable>
    flag. Currently two types of mounts are supported:
    <replaceable>bind</replaceable>, and
    <replaceable>overlay</replaceable>. Mount types are specified as suboptions
    to the <replaceable>-m</replaceable> flag and can be specified multiple
    times separated by commas. <replaceable>overlay</replaceable> mounts are
    currently specified in the format <replaceable>-m
    overlay=/src:/dest</replaceable>.  When no destination
    <replaceable>dest</replaceable> is specified
    <replaceable>dest</replaceable> will be identical to
    <replaceable>src</replaceable>. Read-only <replaceable>bind</replaceable>
    mounts are specified <replaceable>-m bind=/src:/dest:ro</replaceable> and
    read-write <replaceable>bind</replaceable> mounts <replaceable>-m
    bind=/src:/dest:rw</replaceable>. Read-write
    <replaceable>bind</replaceable> mounts are the default and
    <replaceable>rw</replaceable> can be missing when a read-write mount is
    wanted. When <replaceable>dest</replaceable> is missing
    <replaceable>dest</replaceable> will be identical to
    <replaceable>src</replaceable>. An example for multiple mounts would be
    <replaceable>-m
    bind=/src1:/dest1:ro,bind=/src2:ro,overlay=/src3:/dest3</replaceable>.
    </para>

    <para>
    The mounts, their options, and formats supported via the
    <replaceable>-m</replaceable> flag are subject to change.
    </para>
  </refsect1>

  <refsect1>

    <title>Options</title>

    <variablelist>

	  <varlistentry>
	    <term> <option>-N,--newname <replaceable>newname</replaceable></option> </term>
	   <listitem>
	    <para>The name for the copy.</para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-p,--newpath <replaceable>newpath</replaceable></option> </term>
	   <listitem>
	    <para>The path for the copy.</para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-R,--rename </option> </term>
	   <listitem>
	    <para>Rename the original container. </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-s,--snapshot </option> </term>
	   <listitem>
            <para> Create a snapshot of the original container. The backing
            storage for the copy must support snapshots. This currently includes
            btrfs, lvm, overlay, and zfs. </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-a,--allowrunning </option> </term>
	   <listitem>
	    <para> Allow the creation of a Snapshot of an already running container.
	    This may cause data corruption or data loss depending on the used
	    filesystem and applications. Use with care. </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-F,--foreground</option> </term>
	   <listitem>
            <para>Run the snapshot in the foreground. The snapshots console will
            be attached to the current tty. (This option can only be specified
            in conjunction with <replaceable>-e</replaceable>.)
            </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-d, --daemon</option> </term>
	   <listitem>
            <para> Run the snapshot as a daemon (This is the default mode for
            ephemeral containers.). As the container has no more tty, if an
            error occurs nothing will be displayed, the log file can
            be used to check the error. (This option can only be specified in
            conjunction with <replaceable>-e</replaceable>.)
            </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-m, --mount <replaceable>mounttype</replaceable></option> </term>
	   <listitem>
            <para>  Specify a mount for a snapshot  The
            <replaceable>opts</replaceable> argument for the mount type can by
            of type {bind, overlay}. For example <option>-m
            bind=/src:/dest:ro,overlay=/src:/dest</option> (This option can
            currently only be specified in conjunction with
            <replaceable>-e</replaceable>.).</para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-t, --tmpfs </option></term>
	   <listitem>
            <para> When this option is specified the ephemeral container will be
            placed on a tmpfs. NOTE: Rebooting an ephemeral container that is
            located on a tmpfs will currently cause all changes made to it to be
            lost. This flag will only work for ephemeral containers created with
            the <replaceable>-e</replaceable> flag. The original container, from
            which the ephemeral snapshot is created, must be stored as a simple
            directory.
            </para> </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-B, --backingstorage <replaceable>backingstorage</replaceable></option></term>
	   <listitem>
            <para>Specify the backing storage type to be used for the copy
            where 'backingstorage' is of type 'btrfs', 'dir', 'lvm', 'loop',
            'overlay', or 'zfs'. </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-L, --fssize <replaceable>size [unit]</replaceable></option></term>
	   <listitem>
            <para>Specify the size for an 'lvm' filesystem. </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-K, --keepname </option></term>
	   <listitem>
            <para> When this option is specified the hostname of the original
            container will be kept for the copy.</para> </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-D, --keepdata </option></term>
	   <listitem>
            <para>When this option is specified with
            <replaceable>-e</replaceable> a non-ephemeral container is created
            and started. </para> </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-M, --keepmac </option></term>
	   <listitem>
            <para> When this option is specified the MAC address of the original
            container will be kept for the copy.</para> </listitem>
	  </varlistentry>

    </variablelist>

  </refsect1>

  <refsect1>
    <title>Copy hook</title>
    <para>
      If the container being copied has one or more
      <filename>lxc.hook.clone</filename> specified, then the specified hooks
      will be called for the new container. The first 3 arguments passed to the
      clone hook will be the container name, a section ('lxc'), and the hook
      type ('clone'). Extra arguments passed to <command>lxc-copy</command> will
      be passed to the hook program starting at argument 4. The
      <filename>LXC_ROOTFS_MOUNT</filename> environment variable gives
      the path under which the container's root filesystem is mounted. The
      configuration file pathname is stored in
      <filename>LXC_CONFIG_FILE</filename>, the new container name in
      <filename>LXC_NAME</filename>, the old container name in
      <filename>LXC_SRC_NAME</filename>, and the path or device on which the
      rootfs is located is in <filename>LXC_ROOTFS_PATH</filename>.
    </para>
  </refsect1>

  &commonoptions;

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
