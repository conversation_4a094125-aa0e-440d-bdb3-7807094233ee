<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc.container.conf</refentrytitle>
    <manvolnum>5</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc.container.conf</refname>

    <refpurpose>
      <!--
      LXC container configuration file
      -->
      LXC 컨테이너 설정파일
    </refpurpose>
  </refnamediv>

  <refsect1>
    <title><!-- Description -->설명</title>

    <para>
      <!--
      The linux containers (<command>lxc</command>) are always created
      before being used. This creation defines a set of system
      resources to be virtualized / isolated when a process is using
      the container. By default, the pids, sysv ipc and mount points
      are virtualized and isolated. The other system resources are
      shared across containers, until they are explicitly defined in
      the configuration file. For example, if there is no network
      configuration, the network will be shared between the creator of
      the container and the container itself, but if the network is
      specified, a new network stack is created for the container and
      the container can no longer use the network of its ancestor.
      -->
      linux 컨테이너(<command>lxc</command>)는 항상 사용하기 전에 생성된다.
      생성 작업은 가상화할 자원 및 컨테이너 내에서 실행되는 프로세스로부터 고립할 시스템 자원들을 정의하는 것이다.
      기본적으로 pid, sysv ipc, 마운트 포인트가 가상화되고 고립된다. 명시적으로 설정파일에서 정의되기 전까지, 다른 시스템 자원들은 컨테이너 간에 공유된다. 예를 들어, 네트워크 설정이 되어 있지 않다면, 컨테이너 생성한 쪽과 컨테이너 간에 네트워크를 서로 공유할 것이다. 그러나 네트워크가 지정이되었다면, 컨테이너를 위해 새로운 네트워크 스택이 생성된다. 그리고 컨테이너는 더이상 그를 생성한 쪽과 네트워크를 공유하지 않는다.
    </para>

    <para>
      <!--
      The configuration file defines the different system resources to
      be assigned for the container. At present, the utsname, the
      network, the mount points, the root file system, the user namespace,
      and the control groups are supported.
      -->
      설정파일은 컨테이너에 할당될 시스템 자원들을 정의한다. 현재는 utsname, 네트워크, 마운트포인트, 루트 파일시스템, 사용자 네임스페이스 그리고 컨트롤 그룹이 지원된다.
    </para>

    <para>
      <!--
      Each option in the configuration file has the form <command>key
      = value</command> fitting in one line. The '#' character means
      the line is a comment. List options, like capabilities and cgroups
      options, can be used with no value to clear any previously
      defined values of that option.
      -->
      설정파일의 옵션은 <command>key = value</command>의 한 줄로 이루어져 있다.
      '#' 문자를 앞에 붙여 주석임을 나타낼 수 있다. capability와 cgroup 옵션과 같은 리스트 옵션들은, 값을 지정하지 않고 사용할 수 있다. 값이 지정되지 않은 경우 이전에 설정했던 모든 값들을 지운다.
    </para>

    <refsect2>
      <title><!-- Configuration -->설정</title>
      <para>
        <!--
	In order to ease administration of multiple related containers, it
	is possible to have a container configuration file cause another
	file to be loaded.  For instance, network configuration
	can be defined in one common file which is included by multiple
	containers.  Then, if the containers are moved to another host,
	only one file may need to be updated.
        -->
        연관된 컨테이너들을 쉽게 관리하기 위해서, 컨테이너 설정파일은 다른 파일을 불러올 수 있다. 예를 들어서, 네트워크 설정은 여러 컨테이너들을 위해 공통된 하나의 파일로 정의될 수 있다. 그리고 만약 컨테이너들이 다른 호스트로 이동된다면, 해당 파일 하나만 수정하면 된다.
      </para>

      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.include</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify the file to be included.  The included file must be
	      in the same valid lxc configuration file format.
              -->
              include할 파일을 지정한다.
              include할 파일은 lxc 설정파일의 형식에 부합하여야 한다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Architecture -->아키텍처</title>
      <para>
        <!--
	Allows one to set the architecture for the container. For example,
	set a 32bits architecture for a container running 32bits
	binaries on a 64bits host. That fixes the container scripts
	which rely on the architecture to do some work like
	downloading the packages.
        -->
        컨테이너에 아키텍처를 지정할 수 있다. 예를 들어, 64비트 호스트에서 32비트 바이너리를 실행하는 컨테이너라면 32비트 아키텍처로 지정할 수 있다. 패키지를 다운로드 받는 등의 작업을 수행하는 아키텍처에 의존적인 컨테이너 스크립트가 잘 동작할 수 있도록 해준다.
      </para>

      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.arch</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify the architecture for the container.
              -->
              컨테이너의 아키텍처를 지정한다.
	    </para>
	    <para>
              <!--
	      Valid options are
	      <option>x86</option>,
	      <option>i686</option>,
	      <option>x86_64</option>,
	      <option>amd64</option>
              -->
              가능한 옵션은 아래와 같다.
	      <option>x86</option>,
	      <option>i686</option>,
	      <option>x86_64</option>,
	      <option>amd64</option>
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>

    </refsect2>

    <refsect2>
      <title><!-- Hostname -->호스트 이름</title>
      <para>
        <!--
	The utsname section defines the hostname to be set for the
	container. That means the container can set its own hostname
	without changing the one from the system. That makes the
	hostname private for the container.
        -->
        utsname 섹션은 컨테이너 내에서 설정할 호스트 이름을 정의한다. 컨테이너는        시스템의 호스트 이름을 변경하지 않고도 자신의 호스트 이름을 변경할 수 있다. 즉, 컨테이너마다 호스트 이름을 설정할 수 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.uts.name</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the hostname for the container
              -->
              컨테이너의 호스트 이름을 지정한다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Halt signal -->종료 시그널</title>
      <para>
        <!--
        Allows one to specify signal name or number, sent by lxc-stop to the
        container's init process to cleanly shutdown the container. Different
        init systems could use different signals to perform clean shutdown
        sequence. This option allows the signal to be specified in kill(1)
        fashion, e.g. SIGPWR, SIGRTMIN+14, SIGRTMAX-10 or plain number. The
        default signal is SIGPWR.
          -->
        lxc-stop이 컨테이너를 깨끗이 종료를 시키기 위해서 보낼 시그널의 이름이나 숫자를 지정할 수 있다.
        init 시스템마다 깨끗한 종료를 위해 각기 다른 시그널을 사용할 수 있다.
        이 옵션은 kill(1)에서 사용하는 것 처럼 시그널을 지정할 수 있다. 예를 들어 SIGPWR, SIGRTMIN+14, SIGRTMAX-10 또는 숫자를 지정할 수 있다. 기본 시그널은 SIGPWR이다.
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.signal.halt</option>
          </term>
          <listitem>
            <para>
              <!--
              specify the signal used to halt the container
              -->
              컨테이너를 종료할 때 사용할 시그널을 지정한다.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Reboot signal -->재부팅 시그널</title>
      <para>
        <!--
        Allows one to specify signal name or number, sent by lxc-stop to
        reboot the container. This option allows signal to be specified in
        kill(1) fashion, e.g. SIGTERM, SIGRTMIN+14, SIGRTMAX-10 or plain number.
        The default signal is SIGINT.
          -->
        lxc-stop이 컨테이너를 재부팅하기 위해 보낼 시그널의 이름이나 숫자를 지정할 수 있다.
        이 옵션은 kill(1)에서 사용하는 것 처럼 시그널을 지정할 수 있다. 예를 들어 SIGINT, SIGRTMIN+14, SIGRTMAX-10 또는 숫자를 지정할 수 있다. 기본 시그널은 SIGINT이다.
          </para>
          <variablelist>
        <varlistentry>
          <term>
            <option>lxc.signal.reboot</option>
          </term>
          <listitem>
            <para>
              <!--
              specify the signal used to reboot the container
                -->
              컨테이너를 재부팅할 때 사용할 시그널을 지정한다.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Stop signal -->강제종료 시그널</title>
      <para>
        <!--
        Allows one to specify signal name or number, sent by lxc-stop to forcibly
        shutdown the container. This option allows signal to be specified in
        kill(1) fashion, e.g. SIGKILL, SIGRTMIN+14, SIGRTMAX-10 or plain number.
        The default signal is SIGKILL.
          -->
        lxc-stop이 컨테이너를 강제종료하기 위해 보낼 시그널의 이름이나 숫자를 지정할 수 있다.
        이 옵션은 kill(1)에서 사용하는 것 처럼 시그널을 지정할 수 있다. 예를 들>어 SIGKILL, SIGRTMIN+14, SIGRTMAX-10 또는 숫자를 지정할 수 있다. 기본 시그널은 SIGKILL이다.
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.signal.stop</option>
          </term>
          <listitem>
            <para>
              <!--
                  specify the signal used to stop the container
                -->
              컨테이너를 강제종료할 때 사용할 시그널을 지정한다.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Init command -->Init 명령어</title>
      <para>
        <!--
        Sets the command to use as the init system for the containers.

        This option is ignored when using lxc-execute.

        Defaults to: /sbin/init
          -->
        컨테이너의 init으로 사용할 명령어를 설정한다.
        이 옵션은 lxc-execute을 사용할 때는 무시된다.
        기본값은 /sbin/init이다.
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.init.cmd</option>
          </term>
          <listitem>
            <para>
              <!--
                  Absolute path from container rootfs to the binary to use as init.
                -->
              init으로 사용할 바이저리의 컨테이너 루트 파일시스템에서의 절대 경로.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Init ID -->Init이 사용할 ID</title>
      <para>
        <!--
        Sets the UID/GID to use for the init system, and subsequent command, executed by lxc-execute.
        -->
        lxc-execute가 실행하는 컨테이너의 init 및 명령어가 사용할 UID/GID를 지정한다.

        <!--
        These options are only used when lxc-execute is started in a private user namespace.
        -->
        이 옵션들은 lxc-execute가 사용자 네임스페이스 안에서 실행될 때만 적용된다.

        <!--
        Defaults to: UID(0), GID(0)
        -->
        기본 값: UID(0), GID(0)
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.init.uid</option>
          </term>
          <listitem>
            <para>
              <!--
              UID to use within a private user namespace for init.
                -->
              init이 사용자 네임스페이스 안에서 사용할 UID.
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.init.gid</option>
          </term>
          <listitem>
            <para>
              <!--
              GID to use within a private user namespace for init.
                -->
              init이 사용자 네임스페이스 안에서 사용할 GID.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Ephemeral -->임시 컨테이너</title>
      <para>
        <!--
        Allows one to specify whether a container will be destroyed on shutdown.
          -->
        컨테이너가 종료될 때, 해당 컨테이너를 제거할지 여부를 지정할 수 있다.
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.ephemeral</option>
          </term>
          <listitem>
            <para>
              <!--
              The only allowed values are 0 and 1. Set this to 1 to destroy a
              container on shutdown.
                -->
              지정 가능한 값은 0 또는 1이다. 1로 설정하면, 컨테이너를 종료할 때 해당 컨테이너를 제거한다.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Network -->네트워크</title>
      <para>
        <!--
	The network section defines how the network is virtualized in
	the container. The network virtualization acts at layer
	two. In order to use the network virtualization, parameters
	must be specified to define the network interfaces of the
	container. Several virtual interfaces can be assigned and used
	in a container even if the system has only one physical
	network interface.
        -->
        네트워크 섹션은 어떻게 네트워크를 컨테이너 내에서 가상화할지를 정의한다.
        네트워크 가상화는 2개의 계층으로 동작한다.
        네트워크 가상화를 위해서, 컨테이너의 네트워크 인터페이스가 인수로 지정되어야 한다. 시스템이 하나의 물리적인 네트워크 인터페이스를 갖고 있어도, 컨테이너 내에서 여러개의 가상화 인터페이스들을 사용할 수 있다.
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.net</option>
          </term>
          <listitem>
            <para>
              <!--
              may be used without a value to clear all previous network options.
              -->
              값을 지정하지 않고 사용하여 이전에 설정했던 모든 네트워크 옵션들을 초기화할 수 있다.
            </para>
          </listitem>
        </varlistentry>
	<varlistentry>
	  <term>
	    <option>lxc.net.[i].type</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify what kind of network virtualization to be used
	      for the container. Each time
	      a <option>lxc.net.[i].type</option> field is found a new
	      round of network configuration begins. In this way,
	      several network virtualization types can be specified
	      for the same container, as well as assigning several
	      network interfaces for one container. The different
	      virtualization types can be:
              -->
              컨테이너가 어떤 종류의 네트워크 가상화를 사용할지 지정한다.
              <option>lxc.net.[i].type</option> 필드부터 새로운 네트워크 설정이 시작된다. 이 방법으로 여러개의 네트워크 가상화 형태를 같은 컨테이너에 지정할 수 있다. 그리고 여러개의 네트워크 인터페이스를 하나의 컨테이너에 지정할 수도 있다.
              지정 가능한 형태는 아래와 같다.
	    </para>

	    <para>
              <!--
	      <option>empty:</option> will create only the loopback
	      interface.
              -->
	      <option>empty:</option>는 루프백 인터페이스만 생성한다.
	    </para>

            <para>
              <!--
              <option>veth:</option> a virtual ethernet pair
              device is created with one side assigned to the container
              and the other side attached to a bridge specified by
              the <option>lxc.net.[i].link</option> option.
              If the bridge is not specified, then the veth pair device
              will be created but not attached to any bridge.
              Otherwise, the bridge has to be created on the system
              before starting the container.
              <command>lxc</command> won't handle any
              configuration outside of the container.
              By default, <command>lxc</command> chooses a name for the
              network device belonging to the outside of the
              container, but if you wish to handle
              this name yourselves, you can tell <command>lxc</command>
              to set a specific name with
              the <option>lxc.net.[i].veth.pair</option> option (except for
              unprivileged containers where this option is ignored for security
              reasons).
              -->
              <option>veth:</option> 한 쪽은 컨테이너로, 다른 한쪽은 <option>lxc.net.[i].link</option> 옵션으로 지정한 브리지로 붙은 가상 이더넷(veth) 장치 쌍을 생성한다.
              만약 브리지가 지정되지 않았다면, 어떤 브리지에도 붙지 않은  veth 장치 쌍을 만든다. 브리지는 컨테이너 시작전에 시스템에서 생성해야 한다.
              <command>lxc</command>는 컨테이너 이외의 설정에 대해서는 다루지 않는다. 기본값으로 <command>lxc</command>는 컨테이너 바깥에 속할 네트워크 디바이스의 이름을 정해준다. 이름을 변경하기 원한다면, <command>lxc</command>가 지정한 이름으로 설정하도록 <option>lxc.net.[i].veth.pair</option> 옵션을 사용하여야 한다. (비특권 컨테이너는 불가능하다. 이 옵션은 보안상의 이유로 무시될 것이다)
            </para>

	    <para>
              <!--
	      <option>vlan:</option> a vlan interface is linked with
	      the interface specified by
	      the <option>lxc.net.[i].link</option> and assigned to
	      the container. The vlan identifier is specified with the
	      option <option>lxc.net.[i].vlan.id</option>.
              -->
              <option>vlan:</option> vlan 인터페이스는 <option>lxc.net.[i].link</option>로 지정한 인터페이스에 연결되고, 컨테이너로 할당된다. vlan의 식별자는 <option>lxc.net.[i].vlan.id</option> 옵션으로 지정한다.
	    </para>

	    <para>
              <!--
	      <option>macvlan:</option> a macvlan interface is linked
	      with the interface specified by
	      the <option>lxc.net.[i].link</option> and assigned to
	      the container.
	      <option>lxc.net.[i].macvlan.mode</option> specifies the
	      mode the macvlan will use to communicate between
	      different macvlan on the same upper device. The accepted
              modes are <option>private</option>, <option>vepa</option>,
              <option>bridge</option> and <option>passthru</option>.
	      In <option>private</option> mode, the device never
              communicates with any other device on the same upper_dev (default).
              In <option>vepa</option> mode, the new Virtual Ethernet Port
	      Aggregator (VEPA) mode, it assumes that the adjacent
	      bridge returns all frames where both source and
	      destination are local to the macvlan port, i.e. the
	      bridge is set up as a reflective relay.  Broadcast
	      frames coming in from the upper_dev get flooded to all
	      macvlan interfaces in VEPA mode, local frames are not
              delivered locally. In <option>bridge</option> mode, it
	      provides the behavior of a simple bridge between
	      different macvlan interfaces on the same port. Frames
	      from one interface to another one get delivered directly
	      and are not sent out externally. Broadcast frames get
	      flooded to all other bridge ports and to the external
	      interface, but when they come back from a reflective
	      relay, we don't deliver them again.  Since we know all
	      the MAC addresses, the macvlan bridge mode does not
              require learning or STP like the bridge module does. In
              <option>passthru</option> mode, all frames received by
              the physical interface are forwarded to the macvlan
              interface. Only one macvlan interface in <option>passthru</option>
              mode is possible for one physical interface.
              -->
              <option>macvlan:</option> macvlan 인터페이스는 <option>lxc.net.[i].link</option>로 지정한 인터페이스에 연결되고, 컨테이너로 할당된다.
              <option>lxc.net.[i].macvlan.mode</option>은 같은 상위 디바이스에 있는 다른 macvlan과 통신할 때 사용하는 모드를 지정한다.
              지정할 수 있는 모드는 <option>private</option>、<option>vepa</option>、<option>bridge</option>、<option>passthru</option>이다.
              <option>private</option>모드는 디바이스가 같은 상위디바이스의 어떤 장치와도 통신하지 않는다. (기본값)
              새로운 가상 이더넷 포트 통합모드(Virtual Ethernet Port Aggregator), 즉 <option>vepa</option> 모드는 인접한 브리지가 소스와 목적지가 로컬인 모든 프레임들을 macvlan 포트로 반환한다고 가정한다. 즉,  브리지가 reflective relay로 설정되어 있다는 것이다.
              상위장치에서 들어오는 브로드캐스트 프레임들은 모든 macvlan 인터페이스에게 보내져버린다.  로컬 프레임들은 로컬로 보내지지 않는다.
              <option>bridge</option> 모드는 같은 포트의 다른 macvlan 인터페이스 사이에 간단한 브리지를 제공한다.
              어떤 인터페이스에서 다른 인터페이스로 프레임은 직접 전달된다. 하지만 외부로는 보내지지 않는다.
              브로드캐스트 프레임들은 모든 다른 브리지 포트들과 외부 인터페이스에 전달된다.
              그러나 reflective relay로 다시 돌아왔을 때는, 그것들을 다시 전송하지 않는다.
              모든 MAC 주소를 알기 때문에, macvlan 브리지모드는 브리지 모듈처럼 학습이나 STP를 요구하지 않는다.
              <option>passthru</option>모드는 물리 인터페이스로 부터 받은 모든 프레임들을 macvlan 인터페이스로 포워딩한다.
              <option>passthru</option>모드만이 하나의 물리 인터페이스를 설정하는게 가능하다.
	    </para>

	    <para>
              <!--
	      <option>phys:</option> an already existing interface
	      specified by the <option>lxc.net.[i].link</option> is
	      assigned to the container.
              -->
              <option>phys:</option> <option>lxc.net.[i].link</option>로 지정한 이미 존재하는 인터페이스를 컨테이너로 할당된다.
	    </para>
	  </listitem>
	  </varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].flags</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify an action to do for the
	      network.
              -->
              네트워크에 수행할 작업을 지정한다.
	    </para>

	    <para>
              <!--
              <option>up:</option> activates the interface.
              -->
              <option>up:</option> 인터페이스를 활성화시킨다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].link</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the interface to be used for real network
	      traffic.
              -->
              실제 네트워크 트래픽에 사용할 인터페이스를 지정한다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].mtu</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the maximum transfer unit for this interface.
              -->
              해당 인터페이스의 최대 전송 단위(MTU)를 지정한다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].name</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      the interface name is dynamically allocated, but if
	      another name is needed because the configuration files
	      being used by the container use a generic name,
	      eg. eth0, this option will rename the interface in the
	      container.
             -->
              인터페이스 이름은 동적으로 할당된다.
              그러나, 컨테이너가 일반적으로 사용하는 이름과 다른 이름이 필요하다면, (예: eth0) 이 옵션은 컨테이너 내에 있는 인터페이스의 이름을 지정한 것으로 변경할 수 있다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].hwaddr</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      the interface mac address is dynamically allocated by
	      default to the virtual interface, but in some cases,
	      this is needed to resolve a mac address conflict or to
	      always have the same link-local ipv6 address.
	      Any "x" in address will be replaced by random value,
	      this allows setting hwaddr templates.
              -->
              가상 인터페이스의 MAC 주소는 기본적으로 동적 할당된다. 그러나 몇몇가지 이유로 MAC 주소 충돌 문제를 해결하거나, 언제나 같은 링크 로컬 IPv6 주소가 필요하다면, 이 옵션이 필요하다.
              주소의 "x"는 무작위한 값으로 바뀐다. 템플릿에서 하드웨어 주소를 설정하는데 유용하다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].ipv4.address</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the ipv4 address to assign to the virtualized
	      interface. Several lines specify several ipv4 addresses.
	      The address is in format x.y.z.t/m,
	      eg. *************/24.The broadcast address should be
	      specified on the same line, right after the ipv4
	      address.
              -->
              가상 인터페이스에서 사용할 IPv4 주소를 지정한다.
              여러 행으로 여러개의 IPv4 주소를 지정할 수 있다.
              주소의 형식은 x.y.z.t/m으로, 예를 들어 *************/24이다. 브로드 캐스트 주소는 같은 행의 주소 바로 오른쪽에 지정하면 된다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].ipv4.gateway</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the ipv4 address to use as the gateway inside the
	      container. The address is in format x.y.z.t, eg.
	      *************.

	      Can also have the special value <option>auto</option>,
	      which means to take the primary address from the bridge
	      interface (as specified by the
	      <option>lxc.net.[i].link</option> option) and use that as
	      the gateway. <option>auto</option> is only available when
	      using the <option>veth</option> and
	      <option>macvlan</option> network types.
              -->
              컨테이너 내부에서 게이트웨이로 사용할 IPv4 주소를 지정한다.
              주소 형식은 x.y.z.t로, 예를 들면 *************이다.

              <option>auto</option>라는 특별한 값을 지정할 수있다.
              이것은 (<option>lxc.net.[i].link</option> 에서 지정된) 브리지 인터페이스의 첫번째 주소를 가져와 게이트 주소로 사용한다.
              <option>auto</option>는 네트워크 형태가 <option>veth</option>나 <option>macvlan</option>일 때만 지정 가능하다.
	    </para>
	  </listitem>
	</varlistentry>


	<varlistentry>
	  <term>
	    <option>lxc.net.[i].ipv6.address</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the ipv6 address to assign to the virtualized
	      interface. Several lines specify several ipv6 addresses.
	      The address is in format x::y/m,
	      eg. 2003:db8:1:0:214:1234:fe0b:3596/64
              -->
              가상 인터페이스에서 사용할 IPv6 주소를 지정한다.
              여러 행으로 여러개의 IPv6 주소를 지정할 수 있다.
              주소의 형식은 x::y/m으로, 예를 들어 2003:db8:1:0:214:1234:fe0b:3596/64이다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].ipv6.gateway</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the ipv6 address to use as the gateway inside the
	      container. The address is in format x::y,
	      eg. 2003:db8:1:0::1

	      Can also have the special value <option>auto</option>,
	      which means to take the primary address from the bridge
	      interface (as specified by the
	      <option>lxc.net.[i].link</option> option) and use that as
	      the gateway. <option>auto</option> is only available when
	      using the <option>veth</option> and
	      <option>macvlan</option> network types.
              -->
              컨테이너 내부에서 게이트웨이로 사용할 IPv4 주소를 지정한다.
              주소 형식은 x::y로, 예를 들면 2003:db8:1:0::1이다.

              <option>auto</option>라는 특별한 값을 지정할 수있다.
              이것은 (<option>lxc.net.[i].link</option> 에서 지정된) 브리지 인터페이스의 첫번째 주소를 가져와 게이트 주소로 사용한다.
<option>auto</option>는 네트워크 형태가 <option>veth</option>나 <option>macvlan</option>일 때만 지정 가능하다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].script.up</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      add a configuration option to specify a script to be
	      executed after creating and configuring the network used
	      from the host side. The following arguments are passed
	      to the script: container name and config section name
	      (net) Additional arguments depend on the config section
	      employing a script hook; the following are used by the
	      network system: execution context (up), network type
	      (empty/veth/macvlan/phys), Depending on the network
	      type, other arguments may be passed:
	      veth/macvlan/phys. And finally (host-sided) device name.
              -->
              네트워크를 설정하고 생성한 후에 호스트 쪽에서 실행되는 스크립트를 지정한다.
              다음 인수들이 스크립트에 넘겨진다 : 컨테이너 이름, 설정 섹션 이름(net). 그 후 인수는 훅 스크립트을 사용하는 설정 섹션에 달려있다. 다음 인수들은 네트워크 시스템에 의해 사용되어진다 : 실행 컨텍스트(up), 네트워크 형태(empty/veth/macvlan/phys). 네트워크 형태에 따라서 다음 인수들이 넘겨진다 : veth/macvlan/phys의 경우, (호스트 쪽의) 장치 이름.
            </para>
	    <para>
              <!--
	      Standard output from the script is logged at debug level.
	      Standard error is not logged, but can be captured by the
	      hook redirecting its standard error to standard output.
              -->
              스크립트의 표준출력은 debug 수준 로그로 납겨진다.
              표준 에러는 로그로 남겨지지는 않지만, 표준 에러를 표준 출력으로 리다이렉션하여 로그로 남길 수 있다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.net.[i].script.down</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      add a configuration option to specify a script to be
	      executed before destroying the network used from the
	      host side. The following arguments are passed to the
	      script: container name and config section name (net)
	      Additional arguments depend on the config section
	      employing a script hook; the following are used by the
	      network system: execution context (down), network type
	      (empty/veth/macvlan/phys), Depending on the network
	      type, other arguments may be passed:
	      veth/macvlan/phys. And finally (host-sided) device name.
              -->
              네트워크를 제거한 후에 호스트 쪽에서 실행되는 스크립트를 지정한다.
              다음 인수들이 스크립트에 넘겨진다 : 컨테이너 이름, 설정 섹션 이름(net). 그 후 인수는 훅 스크립트을 사용하는 설정 섹션에 달려있다.
              다음 인수들은 네트워크 시스템에 의해 사용되어진다 : 실행 컨텍스트(down), 네트워크 형태(empty/veth/macvlan/phys). 네트워크 형태에 따라서 다음 인수들이 넘겨진다 : veth/macvlan/phys의 경우, (호스트 쪽의) 장치 이름.
            </para>
	    <para>
              <!--
	      Standard output from the script is logged at debug level.
	      Standard error is not logged, but can be captured by the
	      hook redirecting its standard error to standard output.
              -->
              스크립트의 표준출력은 debug 수준 로그로 납겨진다.
              표준 에러는 로그로 남겨지지는 않지만, 표준 에러를 표준 출력으로 리다이렉션하여 로그로 남길 수 있다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>

    </refsect2>

    <refsect2>
      <title><!-- New pseudo tty instance (devpts) -->새로운 pseudo tty 인스턴스(devpts)</title>
      <para>
        <!--
	For stricter isolation the container can have its own private
	instance of the pseudo tty.
        -->
        강한 고립을 위해 컨테이너는 자기자신만의 pseudo tty를 가질 수 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.pty.max</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      If set, the container will have a new pseudo tty
	      instance, making this private to it. The value specifies
              the maximum number of pseudo ttys allowed for a pty
              instance (this limitation is not implemented yet).
              -->
              만약 지정되었다면, 컨테이너는 새 pseudo tty 인스턴스를 갖는다. 그리고 이것을 자기자신 전용으로 만든다. 지정하는 값은 pseudo tty의 최대 개수를 지정한다. (이 제한은 아직 구현되지 않았다)
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Container system console -->컨테이너 시스템 콘솔</title>
      <para>
        <!--
	If the container is configured with a root filesystem and the
	inittab file is setup to use the console, you may want to specify
	where the output of this console goes.
        -->
        컨테이너에 루트 파일시스템이 설정되어 있고 inittab 파일에 콘솔을 사용하는 것이 설정되어 있다면, 콘솔의 출력을 어디로 할지 지정할 수 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.console.logfile</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify a path to a file where the console output will
	      be written.
              -->
              콘솔의 출력을 쓸 파일의 경로를 지정한다.
	    </para>
	  </listitem>
	</varlistentry>
	<varlistentry>
	  <term>
	    <option>lxc.console.path</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify a path to a device to which the console will be
	      attached.  The keyword 'none' will simply disable the
	      console.  This is dangerous once if have a rootfs with a
	      console device file where the application can write, the
	      messages will fall in the host.
              -->
              콘솔을 붙일 장치의 경로를 지정한다.
              'none'이라는 값은 단순히 콘솔을 비활성화 시킨다. 만약 응용 프로그램이 쓸 수 있는 콘솔 장치 파일이 루트 파일시스템에 있으면, 메시지가 호스트 쪽에 출력되므로 이 설정은 위험할 수 있다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Console through the ttys -->tty를 통한 콘솔</title>
      <para>
        <!--
	This option is useful if the container is configured with a root
	filesystem and the inittab file is setup to launch a getty on the
	ttys. The option specifies the number of ttys to be available for
	the container. The number of gettys in the inittab file of the
	container should not be greater than the number of ttys	specified
	in this option, otherwise the excess getty sessions will die and
	respawn indefinitely giving annoying messages on the console or in
	<filename>/var/log/messages</filename>.
        -->
        컨테이너에 루트 파일시스템이 설정되어 있고 inittab 파일에 tty에서 getty를 실행하는 것이 설정되어 있다면, 이 옵션은 유용하다.
        이 옵션은 컨테이너에서 사용가능한 tty의 개수를 지정한다.
        컨테이너의 inittab 파일에 설정된 getty의 개수는 이 옵션에서 정한 tty의 개수보다 크면 안된다. 그렇지 않으면 초과된 getty 세션은 무한히 죽고 다시 살아나기를 반복하며 콘솔이나 <filename>/var/log/messages</filename>에 계속 메시지를 띄울 것이다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.tty.max</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify the number of tty to make available to the
	      container.
              -->
              컨테이너가 만들 수 있는 tty의 개수를 지정한다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Console devices location -->콘솔 장치 위치</title>
      <para>
        <!--
        LXC consoles are provided through Unix98 PTYs created on the
	host and bind-mounted over the expected devices in the container.
	By default, they are bind-mounted over <filename>/dev/console</filename>
	and <filename>/dev/ttyN</filename>.  This can prevent package upgrades
	in the guest.  Therefore you can specify a directory location (under
	<filename>/dev</filename> under which LXC will create the files and
	bind-mount over them.  These will then be symbolically linked to
	<filename>/dev/console</filename> and <filename>/dev/ttyN</filename>.
	A package upgrade can then succeed as it is able to remove and replace
	the symbolic links.
        -->
        LXC 콘솔은 호스트에서 생성된 Unix98 PTY와 컨테이너 내에 바인드 마운트될 장치들을 통해 제공된다. 기본적으로 <filename>/dev/console</filename>와 <filename>/dev/ttyN</filename>를 바인드 마운트 한다. 이것은 게스트에서 패키지 업그레이드를 방해하는 요인이 된다. 그래서 <filename>/dev</filename> 밑에 LXC가 파일을 생성하고 바인드 마운트할 디렉토리의 위치를 따로 지정해 줄 수 있다.
        그리고 만들어진 파일들은 <filename>/dev/console</filename>와 <filename>/dev/ttyN</filename>에 심볼릭 링크된다.
        심볼릭 링크들은 삭제하거나 대체하는 것이 가능하므로 패키지 업그레이드는 성공적으로 이루어질 수 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.tty.dir</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify a directory under <filename>/dev</filename>
	      under which to create the container console devices.
              -->
              컨테이너 콘솔 장치를 생성할 <filename>/dev</filename> 밑의 디렉토리를 지정한다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- /dev directory -->/dev 디렉토리</title>
      <para>
        <!--
	By default, lxc creates a few symbolic links (fd,stdin,stdout,stderr)
	in the container's <filename>/dev</filename> directory but does not
	automatically create device node entries. This allows the container's
	<filename>/dev</filename> to be set up as needed in the container
	rootfs.  If lxc.autodev is set to 1, then after mounting the container's
	rootfs LXC will mount a fresh tmpfs under <filename>/dev</filename>
	(limited to 500k) and fill in a minimal set of initial devices.
        This is generally required when starting a container containing
        a "systemd" based "init" but may be optional at other times.  Additional
        devices in the containers /dev directory may be created through the
        use of the <option>lxc.hook.autodev</option> hook.
        -->
        기본적으로 lxc는 약간의 심볼릭 링크(fd, stdin, stdout, stderr)를 컨테이너의 <filename>/dev</filename> 디렉토리에 생성한다. 그러나 자동으로 장치 노드 항목들을 생성해주지 않는다. 컨테이너의 루트 파일시스템에서 필요로하는 <filename>/dev</filename>를 생성할 수 있게 하는 것이다. lxc.autodev가 1로 지정되었다면, 컨테이너 루트 파일시스템을 마운트 한 후, LXC가 <filename>/dev</filename> 밑에 새로운 tmpfs(최대 500k)를 마운트 해준다. 그리고 최소한의 장치만을 채워준다.
        이것은 "systemd" 기반의 "init" 환경의 컨테이너를 시작할 때 일반적으로 필요하지만, 다른 환경의 경우는 선택적인 요소이다.
         컨테이너의 부가적인 장치들은 <option>lxc.hook.autodev</option> 훅 스크립트를 사용하여 /dev 디렉토리에 생성할 수 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.autodev</option>
	  </term>
	  <listitem>
	    <para>
              <!--
              Set this to 0 to stop LXC from mounting and populating a minimal
              <filename>/dev</filename> when starting the container.
              -->
              컨테이너 시작시 <filename>/dev</filename>을 마운트하고 최소한으로  <filename>/dev</filename>를 구성할지 지정한다. 0이면 해당 동작을 수행하지 않는다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Enable kmsg symlink -->kmsg 심볼릭링크 사용</title>
      <para>
        <!--
        Enable creating /dev/kmsg as symlink to /dev/console.  This defaults to 0.
        -->
        /dev/console에 대한 심볼릭 링크로 /dev/kmsg를 생성한다.
      </para>
      <variablelist>
    <varlistentry>
      <term>
        <option>lxc.kmsg</option>
      </term>
      <listitem>
        <para>
          <!--
          Set this to 1 to enable /dev/kmsg symlinking.
          -->
          이것을 1로 지정하면 /dev/kmsg 심볼릭링크를 사용한다.
        </para>
      </listitem>
    </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Mount points -->마운트 포인트</title>
      <para>
        <!--
	The mount points section specifies the different places to be
	mounted. These mount points will be private to the container
	and won't be visible by the processes running outside of the
	container. This is useful to mount /etc, /var or /home for
	examples.
        -->
        마운트 포인트 섹션은 마운트가 될 각각의 장소를 지정한다.
        이 마운트 포인트들은 컨테이너에서만 보이고 외부에서 실행하는 프로세스들에겐 보이지 않는다.
        이는 예를 들어  /etc, /var, /home을 마운트할 때 유용하다.
      </para>
      <para>
        <!--
        NOTE - LXC will generally ensure that mount targets and relative
        bind-mount sources are properly confined under the container
        root, to avoid attacks involving over-mounting host directories
        and files.  (Symbolic links in absolute mount sources are ignored)
        However, if the container configuration first mounts a directory which
        is under the control of the container user, such as /home/<USER>
        the container at some <filename>path</filename>, and then mounts
        under <filename>path</filename>, then a TOCTTOU attack would be
        possible where the container user modifies a symbolic link under
        their home directory at just the right time.
        -->
        주의 - 보통 LXC는 마운트 대상과 상대 경로로 된 바인드 마운트 소스들이 컨테이너의 루트 아래에 있도록 보장할 것이다. 이는 호스트 디렉토리와 파일들을 겹쳐서 마운트하는 유형의 공격을 피하기 위한 것이다. (절대 경로로 된 마운트 소스 내에 존재하는 심볼릭 링크들은 무시될 것이다.)
        하지만, 만약 컨테이너 설정에서 컨테이너 사용자가 제어할 수 있는, 예를 들어 /home/<USER>/filename>에 먼저 마운트 하고 나서,  <filename>path</filename> 내에 또 마운트를 하는 경우가 있다면,
        컨테이너 사용자가 자신의 home 디렉토리에 있는 심볼릭링크를 정확한 시간에 조작하여, TOCTTOU (역주 : Time of check to time of use) 공격이 가능할 것이다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.mount.fstab</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify a file location in
	      the <filename>fstab</filename> format, containing the
	      mount information.  The mount target location can and in
	      most cases should be a relative path, which will become
	      relative to the mounted container root.  For instance,
              -->
              마운트 정보를 담은 <filename>fstab</filename> 형식으로 된 파일의 위치를 지정한다.
              이 마운트 대상 위치들은 대부분 상대경로로 되어 있으며, 이는 마운트된 컨테이너 루트에서의 상대경로를 의미한다.
	     </para>
<screen>
proc proc proc nodev,noexec,nosuid 0 0
</screen>
	     <para>
               <!--
	      Will mount a proc filesystem under the container's /proc,
	      regardless of where the root filesystem comes from.  This
	      is resilient to block device backed filesystems as well as
	      container cloning.
              -->
               위의 예는 proc 파일시스템을 컨테이너 루트 파일시스템의 위치와 상관없이 컨테이너의 /proc에 마운트시키는 예제이다. 이는 백엔드 파일시스템 블록 장치뿐만 아니라 컨테이너의 복제에도 유연하게 대처할 수 있다.
	     </para>
	     <para>
              <!--
	      Note that when mounting a filesystem from an
	      image file or block device the third field (fs_vfstype)
	      cannot be auto as with
              <citerefentry>
		<refentrytitle>mount</refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              but must be explicitly specified.
              -->
              이미지 파일이나 블록 장치에서 마운트된 파일시스템의 경우, 3번째 필드 (fs_vfstype)는
              <citerefentry>
		<refentrytitle>mount</refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              와 같이 auto를 지정할수 없으며, 명시적으로 지정해야 한다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.mount.entry</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify a mount point corresponding to a line in the
	      fstab format.
              -->
              fstab의 형식으로, 한 줄당 마운트 포인트 하나를 지정한다.

              <!--
	      Moreover lxc add two options to mount.
              <option>optional</option> don't fail if mount does not work.
              <option>create=dir</option> or <option>create=file</option>
              to create dir (or file) when the point will be mounted.
              -->
              또한 마운트 옵션에 아래 2가지 옵션을 추가적으로 사용할 수 있다. 이는 LXC 자체적으로 사용하는 옵션이다.
              <option>optional</option>은 마운트를 못하더라도, 실패로 처리하지 않게 한다.
              <option>create=dir</option>와 <option>create=file</option>는 마운트할 때, 디렉토리(dir) 또는 파일(file)을 생성한다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.mount.auto</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify which standard kernel file systems should be
	      automatically mounted. This may dramatically simplify
	      the configuration. The file systems are:
              -->
              일반적인 커널의 파일시스템을 자동으로 마운트할지 지정한다.
              이 옵션을 사용하면 설정을 매우 편하게 할 수 있다.
              사용할 수 있는 파일시스템들은 아래와 같다.
	    </para>
	    <itemizedlist>
	      <listitem>
                <!--
	        <para>
	          <option>proc:mixed</option> (or <option>proc</option>):
	          mount <filename>/proc</filename> as read-write, but
	          remount <filename>/proc/sys</filename> and
	          <filename>/proc/sysrq-trigger</filename> read-only
	          for security / container isolation purposes.
	        </para>
                -->
                <para>
                  <option>proc:mixed</option> (or <option>proc</option>):
                  <filename>/proc</filename> 을 읽기/쓰기 가능으로 마운트, 그러나 <filename>/proc/sys</filename>과 <filename>/proc/sysrq-trigger</filename>는 읽기 전용으로 다시 마운트 (보안상의 이유 및 컨테이너 고립을 위해)
                </para>
	      </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>proc:rw</option>: mount
	          <filename>/proc</filename> as read-write
	        </para>
                -->
                <para>
	          <option>proc:rw</option>:
                  <filename>/proc</filename> 전체를 읽기/쓰기 가능으로 마운트
                </para>
	      </listitem>
	      <listitem>
		<!--
                <para>
                  <option>sys:mixed</option> (or <option>sys</option>):
                  mount <filename>/sys</filename> as read-only but with
                  /sys/devices/virtual/net writable.
                </para>
		-->
		<para>
		  <option>sys:mixed</option> (or <option>sys</option>):
		  /sys/devices/virtual/net는 쓰기 가능으로,  <filename>/sys</filename>는 읽기 전용으로 마운트.
		</para>
              </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>sys:ro</option>
	          mount <filename>/sys</filename> as read-only
	          for security / container isolation purposes.
	        </para>
                -->
                <para>
                  <option>sys:ro</option>:
                  <filename>/sys</filename>를 읽기 전용으로 마운트 (보안상의 이유 및 컨테이너 고립을 위해)
                </para>
	      </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>sys:rw</option>: mount
	          <filename>/sys</filename> as read-write
	        </para>
                -->
                <para>
	          <option>sys:rw</option>:
                  <filename>/sys</filename>를 읽기/쓰기 가능으로 마운트
                </para>
	      </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>cgroup:mixed</option>:
	          mount a tmpfs to <filename>/sys/fs/cgroup</filename>,
	          create directories for all hierarchies to which
	          the container is added, create subdirectories
	          there with the name of the cgroup, and bind-mount
	          the container's own cgroup into that directory.
	          The container will be able to write to its own
	          cgroup directory, but not the parents, since they
	          will be remounted read-only.
	        </para>
                -->
                <para>
	          <option>cgroup:mixed</option>:
                  <filename>/sys/fs/cgroup</filename>를 tmpfs로 마운트.
                  컨테이너가 추가될 모든 계층의 디렉토리 생성.
                  cgroup 이름의 하위 디렉토리 생성.
                  컨테이너 자신의 cgroup을 해당 디렉토리에 마운트.
                  컨테이너는 자신의 cgroup 디렉토리에는 쓰기가 가능하지만 부모의 디렉토리는 읽기전용으로 마운트 하므로 쓰기가 불가능하다.
                </para>
	      </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>cgroup:ro</option>: similar to
	          <option>cgroup:mixed</option>, but everything will
	        be mounted read-only.
	        </para>
                -->
                <para>
	          <option>cgroup:ro</option>:
                  <option>cgroup:mixed</option>와 유사, 단, 전부 읽기 전용으로 마운트
                </para>
	      </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>cgroup:rw</option>: similar to
	          <option>cgroup:mixed</option>, but everything will
	          be mounted read-write. Note that the paths leading
	          up to the container's own cgroup will be writable,
	          but will not be a cgroup filesystem but just part
	          of the tmpfs of <filename>/sys/fs/cgroup</filename>
	        </para>
                -->
                <para>
	          <option>cgroup:rw</option>:
                  <option>cgroup:mixed</option>와 유사, 단, 전부 읽기/쓰기 가능으로 마운트.
                  컨테이너 자신의 cgroup에 이르기까지의 경로가 모두 쓰기 가능이 되지만, cgroup 파일시스템이 아닌 <filename>/sys/fs/cgroup</filename>의 tmpfs의 일부로써 존재하게 되는 것에 주의해야 한다.
                </para>
	      </listitem>
	      <listitem>
	        <para>
                  <!--
	          <option>cgroup</option> (without specifier):
	          defaults to <option>cgroup:rw</option> if the
	          container retains the CAP_SYS_ADMIN capability,
	          <option>cgroup:mixed</option> otherwise.
                  -->
	          <option>cgroup</option> (별다른 옵션 없이):
                  컨테이너가 CAP_SYS_ADMIN capability를 유지하고 있는 경우 <option>cgroup:rw</option>을 기본으로 사용한다. 그렇지 않다면 <option>cgroup:mixed</option>를 사용한다.
	        </para>
	      </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>cgroup-full:mixed</option>:
	          mount a tmpfs to <filename>/sys/fs/cgroup</filename>,
	          create directories for all hierarchies to which
	          the container is added, bind-mount the hierarchies
	          from the host to the container and make everything
	          read-only except the container's own cgroup. Note
	          that compared to <option>cgroup</option>, where
	          all paths leading up to the container's own cgroup
	          are just simple directories in the underlying
	          tmpfs, here
	          <filename>/sys/fs/cgroup/$hierarchy</filename>
	          will contain the host's full cgroup hierarchy,
	          albeit read-only outside the container's own cgroup.
	          This may leak quite a bit of information into the
	          container.
	        </para>
                -->
                <para>
	          <option>cgroup-full:mixed</option>:
                  <filename>/sys/fs/cgroup</filename>을 tmpfs로 마운트.
                  컨테이너가 추가될 모든 계층의 디렉토리 생성.
                  호스트의 디렉토리들을 컨테이너로 바인드 마운트하고 컨테이너 자신의 cgroup을 제외한 모든 디렉토리는 읽기 전용으로 변경.
                  비교하자면, <option>cgroup</option>의 경우에는 컨테이너 자신의 cgroup에 이르기까지 모든 경로는 단순하게 tmpfs 아래에 있는 디렉토리에 불과하다. 하지만, 여기서는 비록 컨테이너 자신의 cgroup 이외에는 모두 읽기 전용이긴 하나 <filename>/sys/fs/cgroup/$hierarchy</filename>이 호스트의 모든 cgroup 계층구조를 포함하고 있다.
                  이는 컨테이너에게 너무 많은 정보를 노출시킬 수 있다.
                </para>
	      </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>cgroup-full:ro</option>: similar to
	          <option>cgroup-full:mixed</option>, but everything
	          will be mounted read-only.
	        </para>
                -->
                <para>
	          <option>cgroup-full:ro</option>:
                  <option>cgroup-full:mixed</option>와 유사, 단, 전부 읽기 전용으로 마운트
                </para>
	      </listitem>
	      <listitem>
                <!--
	        <para>
	          <option>cgroup-full:rw</option>: similar to
	          <option>cgroup-full:mixed</option>, but everything
	          will be mounted read-write. Note that in this case,
	          the container may escape its own cgroup. (Note also
	          that if the container has CAP_SYS_ADMIN support
	          and can mount the cgroup filesystem itself, it may
	          do so anyway.)
	        </para>
                -->
                <para>
	          <option>cgroup-full:rw</option>:
	          <option>cgroup-full:mixed</option>와 유사, 단, 전부 읽기/쓰기 가능으로 마운트.
                  이 경우는 컨테이너가 자기자신의 cgroup을 벗어날 수 있다. (만약 컨테이너가 CAP_SYS_ADMIN을 갖고 있다면, cgroup 파일시스템 자체를 마운트할 수 있음을 주의해야 한다. 이렇게 하면 같은 결과를 가져올 수 있다)
                </para>
	      </listitem>
	      <listitem>
	        <para>
                  <!--
	          <option>cgroup-full</option> (without specifier):
	          defaults to <option>cgroup-full:rw</option> if the
	          container retains the CAP_SYS_ADMIN capability,
	          <option>cgroup-full:mixed</option> otherwise.
                  -->
	          <option>cgroup-full</option> (별다른 옵션 없이):
                  컨테이너가 CAP_SYS_ADMIN capability를 유지하고 있는 경우 <option>cgroup-full:rw</option>을 기본으로 사용한다. 그렇지 않다면 <option>cgroup-full:mixed</option>를 사용한다.
	        </para>
	      </listitem>
	    </itemizedlist>
            <para>
              <!--
              If cgroup namespaces are enabled, then any <option>cgroup</option>
              auto-mounting request will be ignored, since the container can
              mount the filesystems itself, and automounting can confuse the
              container init.
              -->
              cgroup 네임스페이스가 사용 가능한 경우, <option>cgroup</option> 마운트 옵션들은 전부 무시될 것이다. 컨테이너가 직접 파일시스템을 마운트하기 때문이며, 컨테이너 초기화시 해당 옵션이 혼란을 줄 수 있기 때문이다.
            </para>
	    <para>
              <!--
	      Note that if automatic mounting of the cgroup filesystem
	      is enabled, the tmpfs under
	      <filename>/sys/fs/cgroup</filename> will always be
	      mounted read-write (but for the <option>:mixed</option>
	      and <option>:ro</option> cases, the individual
	      hierarchies,
	      <filename>/sys/fs/cgroup/$hierarchy</filename>, will be
	      read-only). This is in order to work around a quirk in
	      Ubuntu's
              <citerefentry>
		<refentrytitle>mountall</refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
	      command that will cause containers to wait for user
	      input at boot if
	      <filename>/sys/fs/cgroup</filename> is mounted read-only
	      and the container can't remount it read-write due to a
	      lack of CAP_SYS_ADMIN.
              -->
              cgroup 파일시스템이 자동으로 마운트되는게 활성화되어 있다면, <filename>/sys/fs/cgroup</filename> 밑의 tmpfs는 언제나 읽기/쓰기 가능으로 마운트 된다.(단, <option>:mixed</option>과 <option>:ro</option>의 경우에는 각각 계층 <filename>/sys/fs/cgroup/$hierarchy</filename>이 읽기전용이 될 수는 있다)
              아래의  Ubuntu 명령어에 대응하기 위함이다.
              <citerefentry>
		<refentrytitle>mountall</refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              해당 명령어는 컨테이너 부팅시에 <filename>/sys/fs/cgroup</filename>가 읽기전용으로 마운트되어 있고, 컨테이너가 CAP_SYS_ADMIN을 갖고 있지 않아 이를 읽기/쓰기 전용으로 다시 마운트 못할 경우, 부팅시에 사용자의 입력을 기다리게 만들기 때문이다.
	    </para>
	    <para>
              <!--
	      Examples:
              -->
              예제:
	    </para>
	    <programlisting>
	      lxc.mount.auto = proc sys cgroup
	      lxc.mount.auto = proc:rw sys:rw cgroup-full:rw
	    </programlisting>
	  </listitem>
	</varlistentry>

      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Root file system -->루트 파일시스템</title>
      <para>
        <!--
	The root file system of the container can be different than that
	of the host system.
        -->
        컨테이너의 루트 파일시스템은 호스트 시스템과 다르게 구성할 수 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.rootfs.path</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the root file system for the container. It can
	      be an image file, a directory or a block device. If not
	      specified, the container shares its root file system
	      with the host.
              -->
              컨테이너의 루트 파일시스템을 지정한다. 이미지 파일 또는 블록 장치의 디렉토리가 될 수도 있다. 만약 지정되지 않으면 컨테이너는 자신의 루트 파일시스템을 호스트와 공유한다.
	    </para>
	    <para>
              <!--
          For directory or simple block-device backed containers,
          a pathname can be used.  If the rootfs is backed by a nbd
          device, then <filename>nbd:file:1</filename> specifies that
          <filename>file</filename> should be attached to a nbd device,
          and partition 1 should be mounted as the rootfs.
          <filename>nbd:file</filename> specifies that the nbd device
          itself should be mounted.  <filename>overlayfs:/lower:/upper</filename>
          specifies that the rootfs should be an overlay with <filename>/upper</filename>
          being mounted read-write over a read-only mount of <filename>/lower</filename>.
          <filename>aufs:/lower:/upper</filename> does the same using aufs in place
          of overlayfs. For both <filename>overlayfs</filename> and
          <filename>aufs</filename> multiple <filename>/lower</filename>
          directories can be specified. <filename>loop:/file</filename> tells lxc to attach
          <filename>/file</filename> to a loop device and mount the loop device.
          -->
              디렉토리 또는 간단한 블록 장치로 구성된 컨테이너를 위해서 경로이름이 사용될 수 있다. 만약 루트 파일시스템이 nbd 장치의 경우, <filename>nbd:file:1</filename>는 <filename>file</filename>을 nbd 장치로 사용하고 1번 파티션이 루트 파일시스템으로 마운트되도록 지정한다.
              <filename>nbd:file</filename>는 nbd 장치 자체가 마운트되어야 한다고 지정한다.
              <filename>overlayfs:/lower:/upper</filename>는 루트 파일시스템이 읽기전용으로 마운트된 <filename>/lower</filename>를 <filename>/upper</filename>가 읽기/쓰기 가능으로 오버레이 마운트되도록 지정한다.
              <filename>aufs:/lower:/upper</filename>는 aufs에서 위와같이 지정한다. <filename>overlayfs</filename>와 <filename>aufs</filename>는 여러개의 <filename>/lower</filename> 디렉토리를 지정할 수 있다.
              <filename>loop:/file</filename>는 lxc가 <filename>/file</filename>을 loop 장치로 사용하고 loop 장치를 마운트하도록 지정한다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.rootfs.mount</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      where to recursively bind <option>lxc.rootfs.path</option>
	      before pivoting.  This is to ensure success of the
	      <citerefentry>
		<refentrytitle><command>pivot_root</command></refentrytitle>
		<manvolnum>8</manvolnum>
	      </citerefentry>
	      syscall.  Any directory suffices, the default should
	      generally work.
              -->
              루트 파일시스템을 변경하기 전에, <option>lxc.rootfs.path</option>을 어디에 재귀적으로 바인드할지 정한다. 이는 
	      <citerefentry>
		<refentrytitle><command>pivot_root</command></refentrytitle>
		<manvolnum>8</manvolnum>
	      </citerefentry> 
              시스템 콜의 성공을 보장한다.
              어떤 디렉토리도 좋으며, 기본값으로도 보통 동작할 것이다.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>lxc.rootfs.options</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      extra mount options to use when mounting the rootfs.
              -->
              루트 파일시스템을 마운트 할때 사용할 부가적인 마운트 옵션.
	    </para>
	  </listitem>
	</varlistentry>

      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Control group -->컨트롤 그룹</title>
      <para>
        <!--
	The control group section contains the configuration for the
	different subsystem. <command>lxc</command> does not check the
	correctness of the subsystem name. This has the disadvantage
	of not detecting configuration errors until the container is
	started, but has the advantage of permitting any future
	subsystem.
        -->
        컨트롤 그룹 섹션은 (lxc와는) 다른 서브시스템의 설정을 포함한다.
        <command>lxc</command>는 서브시스템의 이름을 정확히 체크하지 않는다.
        이는 컨테이너를 시작할 때까지는 설정 상의 에러를 잡아내기 힘들게 한다.
        그러나 다른 차후에 들어올 수 있는 서브시스템을 지원할 수 있는 장점도 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.cgroup.[subsystem name]</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      specify the control group value to be set.  The
	      subsystem name is the literal name of the control group
	      subsystem.  The permitted names and the syntax of their
	      values is not dictated by LXC, instead it depends on the
	      features of the Linux kernel running at the time the
	      container is started,
	      eg. <option>lxc.cgroup.cpuset.cpus</option>
              -->
              지정한 컨트롤 그룹의 값을 지정한다.
              서브시스템의 이름은 컨트롤 그룹에서의 이름이다.
              사용가능한 이름이나 값의 문법에 대해서는 LXC에서 따로 신경쓰지 않으며, 컨테이너가 시작하는 시점에 리눅스 커널이 해당 기능을 지원하는지에 달려있다.
              예를 들면 <option>lxc.cgroup.cpuset.cpus</option>이다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>Capabilities</title>
      <para>
        <!--
	The capabilities can be dropped in the container if this one
	is run as root.
        -->
        컨테이너가 root로 실행된다면, 컨테이너 내에서 capability를 제거할 수 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.cap.drop</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify the capability to be dropped in the container. A
	      single line defining several capabilities with a space
	      separation is allowed. The format is the lower case of
	      the capability definition without the "CAP_" prefix,
	      eg. CAP_SYS_MODULE should be specified as
	      sys_module. See
	      <citerefentry>
		<refentrytitle><command>capabilities</command></refentrytitle>
		<manvolnum>7</manvolnum>
              </citerefentry>.
              If used with no value, lxc will clear any drop capabilities
              specified up to this point.
              -->
              컨테이너에서 제거할 capability를 지정한다.
              한 줄에 여러개의 capability를 공백(space)으로 구분하여 정의할 수 있다.
              형식은 capability 정의에서 "CAP_" 접두사를 빼고 소문자로 작성하는 것이다. 예를들어 CAP_SYS_MODULE의 경우는 sys_module이다.
              아래를 참조할 수 있다.
	      <citerefentry>
		<refentrytitle><command>capabilities</command></refentrytitle>
		<manvolnum>7</manvolnum>
	      </citerefentry>
               값을 공백으로 지정하면, 해당 설정 이전에 지정했던 capability를 모두 취소한다. (lxc.cap.drop에 아무 것도 지정하지 않은 상태가 된다.)
	    </para>
	  </listitem>
	</varlistentry>
	<varlistentry>
	  <term>
	    <option>lxc.cap.keep</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify the capability to be kept in the container. All other
	      capabilities will be dropped. When a special value of "none" is
	      encountered, lxc will clear any keep capabilities specified up
	      to this point. A value of "none" alone can be used to drop all
	      capabilities.
              -->
              컨테이너에서 유지할 capability를 지정한다.
              다른 capability는 모두 제거될 것이다. "none"이라는 값을 지정하면, lxc는 해당 시점에서 갖고 있던 모든 capability를 제거한다.
              모든 capability를 제거하기 위해서는 "none" 하나만 사용하면 된다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Apparmor profile -->Apparmor 프로파일</title>
      <para>
        <!--
	If lxc was compiled and installed with apparmor support, and the host
	system has apparmor enabled, then the apparmor profile under which the
	container should be run can be specified in the container
        configuration.  The default is <command>lxc-container-default-cgns</command>
	if the host kernel is cgroup namespace aware, or
	<command>lxc-container-default</command> otherwise.
        -->
        lxc가 apparmor를 지원하도록 컴파일된 후 설치되었고, 호스트 시스템에서 apparmor가 활성화되었다면, 컨테이너에서 따라야할 apparmor 프로파일을 컨테이너 설정에서 지정할 수 있다. 기본값은 호스트 커널이 cgroup 네임스페이스를 지원하면 <command>lxc-container-default-cgns</command>이고, 그렇지 않다면 <command>lxc-container-default</command>이다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.apparmor.profile</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify the apparmor profile under which the container should
	      be run.  To specify that the container should be unconfined,
	      use
              -->
              컨테이너가 따라야할 apparmor 프로파일을 지정한다.
              컨테이너가 apparmor로 인한 제한을 받지 않도록 하려면, 아래와 같이 지정하면 된다.
	    </para>
	      <programlisting>lxc.apparmor.profile = unconfined</programlisting>
            <para>
	      <!--
              If the apparmor profile should remain unchanged (i.e. if you
	      are nesting containers and are already confined), then use
	      -->
              apparmor 프로파일이 변경되지 않아야 한다면(중첩 컨테이너 안에 있고, 이미 confined된 경우), 아래와 같이 지정하면 된다.
            </para>
              <programlisting>lxc.apparmor.profile = unchanged</programlisting>
	  </listitem>
	</varlistentry>
	<varlistentry>
	  <term>
	    <option>lxc.apparmor.allow_incomplete</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Apparmor profiles are pathname based.  Therefore many file
	      restrictions require mount restrictions to be effective against
	      a determined attacker.  However, these mount restrictions are not
	      yet implemented in the upstream kernel.  Without the mount
	      restrictions, the apparmor profiles still protect against accidental
	      damager.
              -->
              apparmor 프로파일은 경로이름 기반이므로, 공격자로부터 효과적으로 파일 제한을 하기위해서는 마운트 제한이 요구된다.
              하지만 이 마운트 제한들은 upstream 커널에서는 구현되어 있지 않다.
              마운트 제한 없이도, apparmor 프로파일은 우연한 손상에 대해서 보호가 가능하다.
	    </para>
	    <para>
              <!--
	      If this flag is 0 (default), then the container will not be
	      started if the kernel lacks the apparmor mount features, so that a
	      regression after a kernel upgrade will be detected.  To start the
	      container under partial apparmor protection, set this flag to 1.
              -->
              만약 이 플래그가 0(기본값)이라면, 커널에 apparmor의 마운트 기능이 부족했을때 컨테이너가 시작되지 않는다. 커널을 업그레이드한 후에 해당 기능이 빠졌는지 여부를 검사하기 위함이다. 부분적인 apparmor 보호 하에서도 컨테이너를 시작하려면, 플래그를 1로 지정하면 된다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- SELinux context -->SELinux 컨텍스트</title>
      <para>
        <!--
	If lxc was compiled and installed with SELinux support, and the host
	system has SELinux enabled, then the SELinux context under which the
	container should be run can be specified in the container
	configuration.  The default is <command>unconfined_t</command>,
	which means that lxc will not attempt to change contexts.
	See @DATADIR@/lxc/selinux/lxc.te for an example policy and more
	information.
        -->
        lxc가 SELinux를 지원하도록 컴파일된 후 설치되었고, 호스트 시스템에서 SELinux 컨텍스트가 활성화되었다면, 컨테이너에서 따라야할 SELinux 컨텍스트를 컨테이너 설정에서 지정할 수 있다.
        기본값은 <command>unconfined_t</command>이다. 이는 lxc는 컨텍스트를 변경하지않음을 의미한다.
        정책 예제와 추가적인 정보를 원한다면 @DATADIR@/lxc/selinux/lxc.te를 참고하면 된다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.selinux.context</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify the SELinux context under which the container should
	      be run or <command>unconfined_t</command>. For example
              -->
              컨테이너가 따라야할 SELinux 컨텍스트를 지정하거나, <command>unconfined_t</command>를 지정할 수 있다. 예를 들어 아래와 같이 지정 가능하다.
	    </para>
	    <programlisting>lxc.selinux.context = system_u:system_r:lxc_t:s0:c22</programlisting>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Seccomp configuration -->Seccomp 설정</title>
      <para>
        <!--
        A container can be started with a reduced set of available
	system calls by loading a seccomp profile at startup.  The
	seccomp configuration file must begin with a version number
	on the first line, a policy type on the second line, followed
	by the configuration.
        -->
        컨테이너는 seccomp 프로파일을 로드하여 사용가능한 시스템콜의 수를 줄인 체로 실행할 수 있다.
        seccomp 설정파일은 첫번째 행이 버전번호, 두번째 행이 정책 타입, 시작하며 그 이후에 설정 사항들이 포함되어야 한다.
      </para>
      <para>
        <!--
        Versions 1 and 2 are currently supported.  In version 1, the
	policy is a simple allowlist.  The second line therefore must
	read "allowlist", with the rest of the file containing one (numeric)
	syscall number per line.  Each syscall number is allowlisted,
	while every unlisted number is denylisted for use in the container
        -->
        현재는 버전1과 2만 지원된다. 버전 1에서는 정책은 단순한 화이트리스트이다. 그러므로 두번째 라인은 반드시 "allowlist"여야 한다. 파일의 나머지 내용은 한 줄에 하나의 시스템콜 번호로 채워진다. 화이트리스트에 없는 번호는 컨테이너에서 블랙리스트로 들어간다.
      </para>

      <para>
        <!--
       In version 2, the policy may be denylist or allowlist,
       supports per-rule and per-policy default actions, and supports
       per-architecture system call resolution from textual names.
          -->
        버전 2에서는 폴리시는 블랙리스트 또는 화이트리스트가 될 수 있다. 그리고  각 규칙와 각 정책의 기본 동작, 아키텍쳐별 시스템콜 설정, 텍스트로된 이름을 지원한다.
      </para>
      <para>
        <!--
       An example denylist policy, in which all system calls are
       allowed except for mknod, which will simply do nothing and
       return 0 (success), looks like:
       -->
        아래는 블랙리스트 정책 예제이다. 아래 정책에서는 mknod를 제외한 모든 시스템콜이 허용된다. mknod시에는 아무것도 수행하지 않고 0(성공)을 반환한다.
      </para>
<screen>
2
denylist
mknod errno 0
</screen>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.seccomp.profile</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify a file containing the seccomp configuration to
	      load before the container starts.
              -->
              컨테이너가 시작되기전에 읽어올 seccomp 설정이 담긴 파일을 지정한다.
	     </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>PR_SET_NO_NEW_PRIVS</title>
      <para>
        <!--
              With PR_SET_NO_NEW_PRIVS active execve() promises not to grant
              privileges to do anything that could not have been done without
              the execve() call (for example, rendering the set-user-ID and
              set-group-ID mode bits, and file capabilities non-functional).
              Once set, this bit cannot be unset. The setting of this bit is
              inherited by children created by fork() and clone(), and preserved
              across execve().
              Note that PR_SET_NO_NEW_PRIVS is applied after the container has
              changed into its intended AppArmor profile or SElinux context.
          -->
        PR_SET_NO_NEW_PRIVS가 적용되면, execve()는, execve()를 호출되기 전에는 실행하지 못했던 것을 수행하기 위해 권한을 부여하는 류의 동작을 하지 않게 된다. (예를 들어, set-user-ID와 set-group-ID 모드, 파일 캐퍼빌리티가 동작하지 않는 것이다.)
        일단 적용되면 이 비트는 해제할 수 없다. 이 비트는 fork()와 clone()으로 생성된 자식에게도 상속되며, execve() 이후에도 그대로 적용된다.
        PR_SET_NO_NEW_PRIVS는 컨테이너의 AppArmor 프로필 또는 SELinux 문맥이 변경된 이후에 적용된다.
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.no_new_privs</option>
          </term>
          <listitem>
            <para>
	      <!--
              Specify whether the PR_SET_NO_NEW_PRIVS flag should be set for the
              container. Set to 1 to activate.
		  -->
	      PR_SET_NO_NEW_PRIVS가 컨테이너에 적용되어야 하는지 여부를 지정한다. 1을 지정하면 적용된다.
           </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- UID mappings -->UID 매핑</title>
      <para>
        <!--
        A container can be started in a private user namespace with
	user and group id mappings.  For instance, you can map userid
	0 in the container to userid 200000 on the host.  The root
	user in the container will be privileged in the container,
	but unprivileged on the host.  Normally a system container
	will want a range of ids, so you would map, for instance,
	user and group ids 0 through 20,000 in the container to the
	ids 200,000 through 220,000.
        -->
        컨테이너는 사용자와 그룹 ID 매핑을 통해 자신만의 사용자 네임스페이스 내에서 실행될수 있다.
        예를 들어서 컨테이너의 UID 0번을 호스트의 UID 200000으로 매핑할 수 있다.        컨테이너의 루트 사용자는 컨테이너에서는 특권을 가지고 있지만, 호스트에서는 특권을 가지고 있지 않게 된다.
        보통 시스템 컨테이너는 ID들의 범위를 지정하려 할텐데 그 역시도 지정 가능하다. 예를 들어서, 컨테이너의 UID와 GID를 0 ~ 20,000를 호스트의 200,000 ~ 220,000로 설정 가능하다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.idmap</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Four values must be provided.  First a character, either
	      'u', or 'g', to specify whether user or group ids are
	      being mapped.  Next is the first userid as seen in the
	      user namespace of the container.  Next is the userid as
	      seen on the host.  Finally, a range indicating the number
	      of consecutive ids to map.
              -->
              4개의 값이 제공되어야 한다. 첫 번째는 'u', 'g', 'b' 문자로 각각 UID, GID, 또는 UID 및 GID 를 가리킨다. 그 다음은 사용자 네임스페이스내에서의 UID, 그다음은 호스트의 UID, 그리고 마지막으로 매핑할 ID의 범위를 지정한다.
	     </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Container hooks -->컨테이너 훅</title>
      <para>
        <!--
        Container hooks are programs or scripts which can be executed
	at various times in a container's lifetime.
        -->
        컨테이너 훅은 컨테이너의 생명주기 내에서 다양한 상황에 실행되는 프로그램 또는 스크립트이다.
      </para>
      <para>
        <!--
        When a container hook is executed, information is passed both
	as command line arguments and through environment variables.
	The arguments are:
	<itemizedlist>
	  <listitem><para> Container name. </para></listitem>
	  <listitem><para> Section (always 'lxc'). </para></listitem>
	  <listitem><para> The hook type (i.e. 'clone' or 'pre-mount'). </para></listitem>
	  <listitem><para> Additional arguments. In the
	  case of the clone hook, any extra arguments passed to
	  lxc-clone will appear as further arguments to the hook.
	  In the case of the stop hook, paths to filedescriptors
	  for each of the container's namespaces along with their types
	  are passed. </para></listitem>
	</itemizedlist>
	The following environment variables are set:
	<itemizedlist>
	  <listitem><para> LXC_NAME: is the container's name. </para></listitem>
	  <listitem><para> LXC_ROOTFS_MOUNT: the path to the mounted root filesystem. </para></listitem>
	  <listitem><para> LXC_CONFIG_FILE: the path to the container configuration file. </para></listitem>
	  <listitem><para> LXC_SRC_NAME: in the case of the clone hook, this is the original container's name. </para></listitem>
	  <listitem><para> LXC_ROOTFS_PATH: this is the lxc.rootfs.path entry for the container.  Note this is likely not where the mounted rootfs is to be found, use LXC_ROOTFS_MOUNT for that. </para></listitem>
	</itemizedlist>
        -->
        컨테이너 훅이 실행될 때, 정보는 명령어 인수나 환경 변수를 통해 넘겨진다.
        인수 :
	<itemizedlist>
	  <listitem><para>컨테이너 이름</para></listitem>
	  <listitem><para>섹션 (보통 'lxc')</para></listitem>
	  <listitem><para>훅 종류 ('clone', 'pre-mount' 등)</para></listitem>
	  <listitem><para>추가 인수. clone 훅일 경우, lxc-clone에게 넘였던 추가 인수들이 넘어온다. stop 훅일 경우, 컨테이너의 네임스페이스 각각에 대한 이름과 파일 디스크립터의 경로가 넘어온다.</para></listitem>
	</itemizedlist>
        환경 변수 :
	<itemizedlist>
	  <listitem><para> LXC_NAME: 컨테이너 이름</para></listitem>
	  <listitem><para> LXC_ROOTFS_MOUNT: 마운트될 루트 파일시스템의 경로</para></listitem>
	  <listitem><para> LXC_CONFIG_FILE: 컨테이너 설정파일의 경로</para></listitem>
	  <listitem><para> LXC_SRC_NAME: clone 훅의 경우, 원본 컨테이너의 이름</para></listitem>
	  <listitem><para> LXC_ROOTFS_PATH: 컨테이너의 lxc.rootfs.path 항목. 이 것은 마운트된 루트 파일시스템을 가리키는 것이 아님에 주의해야한다. 그 목적을 위해서는  LXC_ROOTFS_MOUNT를 사용해야 한다.</para></listitem>
        </itemizedlist>
      </para>
      <para>
        <!--
        Standard output from the hooks is logged at debug level.
        Standard error is not logged, but can be captured by the
        hook redirecting its standard error to standard output.
        -->
        훅의 표준출력은 debug 수준 로그로 납겨진다.
        표준 에러는 로그로 남겨지지는 않지만, 표준 에러를 표준 출력으로 리
다이렉션하여 로그로 남길 수 있다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.pre-start</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      A hook to be run in the host's namespace before the
	      container ttys, consoles, or mounts are up.
              -->
              컨테이너의 tty, 콘솔의 생성 및 마운트가 되기 전에, 호스트의 네임스페이스에서 실행되는 훅.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.pre-mount</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      A hook to be run in the container's fs namespace but before
	      the rootfs has been set up.  This allows for manipulation
	      of the rootfs, i.e. to mount an encrypted filesystem.  Mounts
	      done in this hook will not be reflected on the host (apart from
	      mounts propagation), so they will be automatically cleaned up
	      when the container shuts down.
              -->
              컨테이너의 마운트 네임스페이스 안에서 루트 파일시스템이 세팅되기 전에 실행되는 훅.
              예를 들어 암호화 파일시스템을 마운트 하는 등의 루트 파일시스템을 조작할 수 있게 해준다. 이 훅에서 마운트를 하더라도 호스트에는 반영되지 않는다. (mounts propagation은 제외) 그래서 컨테이너가 종료되면 자동적으로 정리된다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.mount</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      A hook to be run in the container's namespace after
	      mounting has been done, but before the pivot_root.
              -->
              마운트가 완료된 후 pivot_root 전에, 컨테이너의 마운트 네임스페이스에서 실행되는 훅.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.autodev</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      A hook to be run in the container's namespace after
	      mounting has been done and after any mount hooks have
	      run, but before the pivot_root, if
	      <option>lxc.autodev</option> == 1.
	      The purpose of this hook is to assist in populating the
	      /dev directory of the container when using the autodev
	      option for systemd based containers.  The container's /dev
	      directory is relative to the
	      ${<option>LXC_ROOTFS_MOUNT</option>} environment
	      variable available when the hook is run.
              -->
              <option>lxc.autodev</option> == 1가 지정되어 있는 경우에 마운트 완료시 마운트 훅도 실행 된 후 pivot_root전에, 컨테이너의 마운트 네임스페이스에서 실행되는 훅.
              이 훅의 목적은 systemd 기반의 컨테이너에서 autodev 옵션을 사용하는 경우  /dev 디렉토리를 구성할 때 도움을 주기위한 것이다.
              훅이 실행될 때, 컨테이너의 /dev 경로는 ${<option>LXC_ROOTFS_MOUNT</option>} 환경변수에 대한 경로이다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.start</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      A hook to be run in the container's namespace immediately
	      before executing the container's init.  This requires the
	      program to be available in the container.
              -->
              컨테이너의 init이 실행되기 직전에 컨테이너의 네임스페이스에서 실행되는 훅. 컨테이너 내에서 해당 프로그램이 실행될 수 있는 상태여야 한다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.stop</option>
	  </term>
	  <listitem>
	    <para>
	      <!--
	      A hook to be run in the host's namespace with references
	      to the container's namespaces after the container has been shut
	      down. For each namespace an extra argument is passed to the hook
	      containing the namespace's type and a filename that can be used to
	      obtain a file descriptor to the corresponding namespace, separated
	      by a colon. The type is the name as it would appear in the
	      <filename>/proc/PID/ns</filename> directory.
	      For instance for the mount namespace the argument usually looks
	      like <filename>mnt:/proc/PID/fd/12</filename>.
	      -->
	      컨테이너가 종료된 후 컨테이너 네임스페이스에 대한 참조를 넘겨받는 호스트의 네임스페이스에서 실행되는 훅.
	      각각의 네임스페이스들은 훅에 추가인수로 넘겨진다. 해당 인수는 네임스페이스의 이름과 네임스페이스의 파일 디스크립터를 얻어올 수 있는 파일이름을 가지고 있으며, 콜론으로 구분된다.
	      네임스페이스 이름은 <filename>/proc/PID/ns</filename> 디렉토리 내의 파일 이름이다. 예를 들어 마운트 네임스페이스에 대응하는 인수는 일반적으로 <filename>mnt:/proc/PID/fd/12</filename>와 같이 된다.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.post-stop</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      A hook to be run in the host's namespace after the
	      container has been shut down.
              -->
              컨테이너가 종료된 후 호스트의 네임스페이스에서 실행되는 훅.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.clone</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      A hook to be run when the container is cloned to a new one.
	      See <citerefentry><refentrytitle><command>lxc-clone</command></refentrytitle>
	      <manvolnum>1</manvolnum></citerefentry> for more information.
              -->
              컨테이너가 새로운 컨테이너로 복제되었을 경우 실행되는 훅. 아래를 참조하면 더 자세한 정보를 얻을 수 있다.
              <citerefentry><refentrytitle><command>lxc-clone</command></refentrytitle>
              <manvolnum>1</manvolnum></citerefentry>
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.hook.destroy</option>
	  </term>
	  <listitem>
	    <para>
              <!--
              A hook to be run when the container is destroyed.
              -->
              컨테이너가 제거될 때 실행되는 훅.
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Container hooks Environment Variables -->컨테이너 훅 환경 변수</title>
      <para>
        <!--
        A number of environment variables are made available to the startup
        hooks to provide configuration information and assist in the
        functioning of the hooks.  Not all variables are valid in all
        contexts.  In particular, all paths are relative to the host system
        and, as such, not valid during the <option>lxc.hook.start</option> hook.
        -->
        훅이 시작될때 설정 정보를 제공하고 훅의 기능을 돕기 위해 몇가지 환경 변수가 사용 가능하다.
        모든 컨텍스트에서 모든 변수가 사용 가능하진 않다. 특히, 모든 경로는 호스트 시스템에서의 경로이며, <option>lxc.hook.start</option> 훅에서는 유효하지 않다.
      </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>LXC_NAME</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      The LXC name of the container.  Useful for logging messages
	      in common log environments.  [<option>-n</option>]
              -->
              LXC 컨테이너의 이름. 일반적인 로그 환경에서 로그메시지에 유용하게 사용할 수 있다. [<option>-n</option>]
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>LXC_CONFIG_FILE</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Host relative path to the container configuration file.  This
	      gives the container to reference the original, top level,
	      configuration file for the container in order to locate any
	      additional configuration information not otherwise made
	      available.  [<option>-f</option>]
              -->
              컨테이너 설정파일의 호스트에서의 경로.
              이것은 다른 방법으로는 얻을 수 없는 추가적인 정보룰 찾을 수 있도록, 컨테이너가 참조하는 원래의 최상위 설정파일의 경로를 제공한다. [<option>-f</option>]
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>LXC_CONSOLE</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      The path to the console output of the container if not NULL.
	      [<option>-c</option>] [<option>lxc.console.path</option>]
              -->
              NULL이 아니라면, 컨테이너의 콘솔의 출력이 저장될 경로.
	      [<option>-c</option>] [<option>lxc.console.path</option>]
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>LXC_CONSOLE_LOGPATH</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      The path to the console log output of the container if not NULL.
	      [<option>-L</option>]
              -->
              NULL이 아니라면, 컨테이너의 콘솔의 로그 출력이 저장될 경로.
	      [<option>-L</option>]
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>LXC_ROOTFS_MOUNT</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      The mount location to which the container is initially bound.
	      This will be the host relative path to the container rootfs
	      for the container instance being started and is where changes
	      should be made for that instance.
	      [<option>lxc.rootfs.mount</option>]
              -->
              처음에 컨테이너가 마운트 되는 장소.
              이것은 시작되는 컨테이너 인스턴스를 위한 루트 파일시스템의 호스트에서의 경로이다. 해당 인스턴스에 대한 변경이 이루어져야 하는 장소이다.
	      [<option>lxc.rootfs.mount</option>]
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>LXC_ROOTFS_PATH</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      The host relative path to the container root which has been
	      mounted to the rootfs.mount location.
	      [<option>lxc.rootfs.path</option>]
              -->
              rootfs.mount에 마운트된 컨테이너 루트의 호스트에서의 경로이다.
              [<option>lxc.rootfs.path</option>]
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_SRC_NAME</option>
          </term>
          <listitem>
            <para>
              <!--
              Only for the clone hook. Is set to the original container name.
              -->
              clone 훅에서만 사용된다. 원본 컨테이너의 이름을 지정한다.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_TARGET</option>
          </term>
          <listitem>
            <para>
              <!--
              Only for the stop hook. Is set to "stop" for a container
              shutdown or "reboot" for a container reboot.
              -->
              stop 훅에서만 사용된다. 값이 "stop"이면 컨테이너가 종료되는 것을, "reboot"이면 컨테이너가 재부팅되는 것을 의미한다.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_CGNS_AWARE</option>
          </term>
          <listitem>
            <para>
              <!--
             If unset, then this version of lxc is not aware of cgroup
             namespaces.  If set, it will be set to 1, and lxc is aware
             of cgroup namespaces.  Note this does not guarantee that
             cgroup namespaces are enabled in the kernel.  This is used
             by the lxcfs mount hook.
             -->
             이 변수가 지정되지 않았다면, 현재 버전의 lxc는 cgroup 네임스페이스를 지원하지 않는다. 만약 지정되었고 값이 1이라면, lxc는 cgroup 네임스페이스를 지원하는 것이다. 단, kernel에서의 cgroup 네임스페이스 지원을 보장하는 것이 아님에 주의해야 한다. lxcfs 마운트 훅에서 사용된다.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
    <title><!-- Logging -->로그</title>
    <para>
      <!--
      Logging can be configured on a per-container basis.  By default,
      depending upon how the lxc package was compiled, container startup
      is logged only at the ERROR level, and logged to a file named after
      the container (with '.log' appended) either under the container path,
      or under @LOGPATH@.
      -->
      로그는 각 컨테이너마다 설정할 수 있다.
      기본적으로 lxc 패키지가 어떻게 컴파일되었는지에 달려있지만, 컨테이너 시작시에는 error 수준 로그만 기록된다. 컨테이너 경로나 @LOGPATH@ 밑에 컨테이너의 이름을 따서(뒤에 '.log'를 붙여서) 로그 파일을 생성한다.
    </para>
    <para>
      <!--
      Both the default log level and the log file can be specified in the
      container configuration file, overriding the default behavior.  Note
      that the configuration file entries can in turn be overridden by the
      command line options to <command>lxc-start</command>.
      -->
      기본 로그 수준과 로그파일은 컨테이너 설정파일로 지정 가능하며, 기본 동작을 덮어버린다. 마찬가지로 설 정파일 항목들은 <command>lxc-start</command> 명령어의 옵션으로 덮어쓸 수 있다.
    </para>
      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.log.level</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	    The level at which to log.  The log level is an integer in
	    the range of 0..8 inclusive, where a lower number means more
	    verbose debugging.  In particular 0 = trace, 1 = debug, 2 =
	    info, 3 = notice, 4 = warn, 5 = error, 6 = critical, 7 =
	    alert, and 8 = fatal.  If unspecified, the level defaults
	    to 5 (error), so that only errors and above are logged.
            -->
              기록할 로그 수준.
              로그 수준은 0 ~ 8 사이의 정수이다.
              숫자가 작을수록 더 자세히 로그를 기록한다.
              구체적으로는 0 = trace, 1 = debug, 2 = info, 3 = notice, 4 = warn, 5 = error, 6 = critical, 7 = alert, 8 = fatal이다.
              지정하지 않은 경우, 기본값은 5 (error)로, 에러 이거나 그보다 심각한 상황의 로그를 기록한다.
	    </para>
	    <para>
              <!--
	    Note that when a script (such as either a hook script or a
	    network interface up or down script) is called, the script's
	    standard output is logged at level 1, debug.
            -->
            (훅 스크립트 및 네트워크 인터페이스 up/down 스크립트 같은) 스크립트가 호출이되면, 스크립트의 표준 입출력은 1 번, debug 수준으로 기록된다.
	    </para>
	  </listitem>
	</varlistentry>
	<varlistentry>
	  <term>
	    <option>lxc.log</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	    The file to which logging info should be written.
            -->
              로그 정보를 쓸 파일.
	    </para>
	  </listitem>
	</varlistentry>
	<varlistentry>
	  <term>
	    <option>lxc.log.syslog</option>
	  </term>
	  <listitem>
	    <para>
	      <!--
		  Send logging info to syslog. It respects the log level defined in
		  <command>lxc.log.level</command>. The argument should be the syslog
		  facility to use, valid ones are: daemon, local0, local1, local2,
		  local3, local4, local5, local5, local7.
	      -->
	      로그정보를 syslog에 보낸다. 로그 수준은 <command>lxc.log.level</command>로 지정할 수 있다. 인자는 syslog에 정의된 값으로만 지정할 수 있다. 사용 가능한 값은 다음과 같다 : daemon, local0, local1, local2, local3, local4, local5, local5, local7 
	    </para>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
    <title><!-- Autostart -->자동시작</title>
    <para>
      <!--
        The autostart options support marking which containers should be
        auto-started and in what order. These options may be used by LXC tools
        directly or by external tooling provided by the distributions.
        -->
      자동시작 옵션들은 자동시작할 컨테이너 지정 및 순서 설정이 가능하다.
      이 옵션들은 LXC 도구로 직접 사용하거나 배포판들이 제공하는 외부 도구에 의해 사용될 수도 있다.
    </para>

    <variablelist>
        <varlistentry>
          <term>
            <option>lxc.start.auto</option>
          </term>
          <listitem>
            <para>
              <!--
              Whether the container should be auto-started.
              Valid values are 0 (off) and 1 (on).
              -->
              컨테이너가 자동으로 시작될지 여부.
              유효한 값은 0 (off) 또는 1 (on)이다.
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.start.delay</option>
          </term>
          <listitem>
            <para>
              <!--
              How long to wait (in seconds) after the container is
              started before starting the next one.
              -->
              컨테이너가 시작된 후 다음 컨테이너가 시작되기 전까지 기다릴 시간(초).
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.start.order</option>
          </term>
          <listitem>
            <para>
              <!--
              An integer used to sort the containers when auto-starting
              a series of containers at once.
              -->
              다수의 컨테이너를 한번에 자동시작할 때, 컨테이너의 부팅 순서를 결정할 때 사용하는 정수를 지정한다.
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.monitor.unshare</option>
          </term>
          <listitem>
            <para>
              <!--
              If not zero the mount namespace will be unshared from the host
              before initializing the container (before running any pre-start
              hooks). This requires the CAP_SYS_ADMIN capability at startup.
              Default is 0.
              -->
              값이 0이 아니라면, 컨테이너가 초기화되기 전 (pre-start 훅이 실행 되기 전) 호스트로부터 마운트 네임스페이스를 unshare 한다. 시작시에 CAP_SYS_ADMIN 캐퍼빌리티가 요구된다. 기본값은 0이다.
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.group</option>
          </term>
          <listitem>
            <para>
              <!--
              A multi-value key (can be used multiple times) to put the
              container in a container group. Those groups can then be
              used (amongst other things) to start a series of related
              containers.
              -->
              컨테이너를 추가할 컨테이너 그룹을 지정한다. 여러값을 설정할 수 있으며, 여러번 지정 가능하다.
              설정된 그룹은 연관된 컨테이너들을 시작할 때 사용된다.
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
    <title><!-- Autostart and System Boot -->자동시작과 시스템 부팅</title>
    <para>
      <!--
          Each container can be part of any number of groups or no group at all.
          Two groups are special. One is the NULL group, i.e. the container does
          not belong to any group. The other group is the "onboot" group.
        -->
          각각의 컨테이너는 여러 그룹에 속할수도 있고 아무그룹에도 속하지 않을 수 있다. 두개의 그룹은 특수한데, 하나는 NULL 그룹이고 컨테이너가 아무그룹에도 속하지 않을때 사용된다. 그리고 나머지 하나는 "onboot" 그룹이다.
    </para>

    <para>
      <!--
          When the system boots with the LXC service enabled, it will first
          attempt to boot any containers with lxc.start.auto == 1 that is a member
          of the "onboot" group. The startup will be in order of lxc.start.order.
          If an lxc.start.delay has been specified, that delay will be honored
          before attempting to start the next container to give the current
          container time to begin initialization and reduce overloading the host
          system. After starting the members of the "onboot" group, the LXC system
          will proceed to boot containers with lxc.start.auto == 1 which are not
          members of any group (the NULL group) and proceed as with the onboot
          group.
          -->
          LXC 서비스가 활성화된 상태로 시스템이 부팅될 때, 먼저 lxc.start.auto == 1이고 "onboot" 그룹인 컨테이너들을 시작하려고 시도한다. 시작과정은 lxc.start.order의 순서대로 이루어진다.
          만약 lxc.start.delay가 지정 되었다면, 다음 컨테이너를 시작하려고 시도>하기 전, 현재 컨테이너의 초기화 및 호스트 시스템의 부하를 줄이기 위해서 지연시간을 준다.
          "onboot" 그룹의 멤버들을 시작시킨 후, LXC 시스템은 lxc.start.auto == 1이고 어떤 그룹에도 속하지 않은(NULL 그룹) 컨테이너들을 시작한다.
    </para>

    </refsect2>

    <refsect2>
      <title><!-- Container Environment -->컨테이너 환경변수</title>
      <para>
        <!--
	If you want to pass environment variables into the container (that
	is, environment variables which will be available to init and all of
	its descendents), you can use <command>lxc.environment</command>
	parameters to do so.  Be careful that you do not pass in anything
	sensitive; any process in the container which doesn't have its
	environment scrubbed will have these variables available to it, and
	environment variables are always available via
	<command>/proc/PID/environ</command>.
        -->
        컨테이너에 환경변수를 념겨주고 싶다면(환경변수를 컨테이너의 init과 그 자손 전체가 사용할 수 있다), <command>lxc.environment</command>를 사용할 수 있다.
        민감한 정보를 넘기지 않도록 주의해야 한다. 왜냐면 컨테이너의 모든 프로세스가 이 환경변수를 획득할 수 있기 때문이다. 환경변수는 항상 <command>/proc/PID/environ</command>를 통해 획득할 수 있다.
      </para>

      <para>
        <!--
        This configuration parameter can be specified multiple times; once
        for each environment variable you wish to configure.
        -->
        이 설정항목은 여러번을 지정할 수 있으며, 설정하려는 환경변수마다 한번씩 지정한다.
      </para>

      <variablelist>
	<varlistentry>
	  <term>
	    <option>lxc.environment</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      Specify an environment variable to pass into the container.
	      Example:
              -->
              컨테이너로 전달될 환경변수를 지정한다.
              예제:
	    </para>
	    <programlisting>
	      lxc.environment = APP_ENV=production
	      lxc.environment = SYSLOG_SERVER=**********
	    </programlisting>
	  </listitem>
	</varlistentry>
      </variablelist>
    </refsect2>

  </refsect1>

  <refsect1>
    <title><!-- Examples -->예제</title>
      <para>
        <!--
	In addition to the few examples given below, you will find
	some other examples of configuration file in @DOCDIR@/examples
        -->
        아래에 소개하는 몇가지 예제말고도 다른 예제들이 @DOCDIR@/examples에 위치하고 있다.
      </para>
    <refsect2>
      <title><!-- Network -->네트워크</title>
      <para>
        <!--
        This configuration sets up a container to use a veth pair
	device with one side plugged to a bridge br0 (which has been
	configured before on the system by the administrator). The
	virtual network device visible in the container is renamed to
	eth0.
        -->
        이 설정은 컨테이너가 한 쪽은 (이전에 시스템에 이미 생성된) br0 브리지에 연결되어 있는 veth 장치 쌍을 사용하도록 세팅한다. 가상 네트워크 장치는 컨테이너 내에서 eth0라는 이름을 갖는다.
      </para>
      <programlisting>
	lxc.uts.name = myhostname
	lxc.net.0.type = veth
	lxc.net.0.flags = up
	lxc.net.0.link = br0
	lxc.net.0.name = eth0
	lxc.net.0.hwaddr = 4a:49:43:49:79:bf
	lxc.net.0.ipv4.address = *******/24 *********
	lxc.net.0.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3597
      </programlisting>
    </refsect2>

    <refsect2>
      <title><!-- UID/GID mapping -->UID/GID 매핑</title>
      <para><!-- This configuration will map both user and group ids in the
        range 0-9999 in the container to the ids 100000-109999 on the host. -->
        이 설정은 UID와 GID 둘다를 컨테이너의 0 ~ 9999를 호스트의 100000 ~ 109999로 매핑한다.
      </para>
      <programlisting>
	lxc.idmap = u 0 100000 10000
	lxc.idmap = g 0 100000 10000
      </programlisting>
    </refsect2>

    <refsect2>
      <title><!-- Control group -->컨트롤 그룹</title>
      <para>
        <!-- This configuration will setup several control groups for
      the application, cpuset.cpus restricts usage of the defined cpu,
      cpus.share prioritize the control group, devices.allow makes
      usable the specified devices.-->
        이 설정은 어플리케이션을 위해 몇가지 컨트롤 그룹을 설정한다. cpuset.cpus는 정의된 cpu만 사용하도록 제한한다. cpus.share은 컨트롤 그룹(cpu) 우선순위를 지정한다. devices.allow는 특정 장치를 사용 가능하게 한다.
      </para>
      <programlisting>
	lxc.cgroup.cpuset.cpus = 0,1
	lxc.cgroup.cpu.shares = 1234
	lxc.cgroup.devices.deny = a
	lxc.cgroup.devices.allow = c 1:3 rw
	lxc.cgroup.devices.allow = b 8:0 rw
      </programlisting>
    </refsect2>

    <refsect2>
      <title><!-- Complex configuration -->복잡한 설정</title>
      <para>
        <!-- This example show a complex configuration making a complex
      network stack, using the control groups, setting a new hostname,
      mounting some locations and a changing root file system. -->
        아래의 예제는 복잡한 네트워크 스택, 컨트롤 그룹 사용, 호스트 이름 설정, 몇몇 장소 마운트, 루트 파일시스템 변경 등의 복잡한 설정을 보여준다.
      </para>
      <programlisting>
	lxc.uts.name = complex
	lxc.net.0.type = veth
	lxc.net.0.flags = up
	lxc.net.0.link = br0
	lxc.net.0.hwaddr = 4a:49:43:49:79:bf
	lxc.net.0.ipv4.address = ********/24 **********
	lxc.net.0.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3597
	lxc.net.0.ipv6.address = 2003:db8:1:0:214:5432:feab:3588
	lxc.net.1.type = macvlan
	lxc.net.1.flags = up
	lxc.net.1.link = eth0
	lxc.net.1.hwaddr = 4a:49:43:49:79:bd
	lxc.net.1.ipv4.address = ********/24
	lxc.net.1.ipv4.address = **************/24
	lxc.net.1.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3596
	lxc.net.2.type = phys
	lxc.net.2.flags = up
	lxc.net.2.link = random0
	lxc.net.2.hwaddr = 4a:49:43:49:79:ff
	lxc.net.2.ipv4.address = ********/24
	lxc.net.2.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3297
	lxc.cgroup.cpuset.cpus = 0,1
	lxc.cgroup.cpu.shares = 1234
	lxc.cgroup.devices.deny = a
	lxc.cgroup.devices.allow = c 1:3 rw
	lxc.cgroup.devices.allow = b 8:0 rw
	lxc.mount.fstab = /etc/fstab.complex
	lxc.mount.entry = /lib /root/myrootfs/lib none ro,bind 0 0
	lxc.rootfs.path = dir:/mnt/rootfs.complex
	lxc.cap.drop = sys_module mknod setuid net_raw
	lxc.cap.drop = mac_override
      </programlisting>
    </refsect2>

  </refsect1>

  <refsect1>
    <title><!-- See Also -->참조</title>
    <simpara>
      <citerefentry>
	<refentrytitle><command>chroot</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>,

      <citerefentry>
	<refentrytitle><command>pivot_root</command></refentrytitle>
	<manvolnum>8</manvolnum>
      </citerefentry>,

      <citerefentry>
	<refentrytitle><filename>fstab</filename></refentrytitle>
	<manvolnum>5</manvolnum>
      </citerefentry>

      <citerefentry>
	<refentrytitle><filename>capabilities</filename></refentrytitle>
	<manvolnum>7</manvolnum>
      </citerefentry>

    </simpara>
  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
