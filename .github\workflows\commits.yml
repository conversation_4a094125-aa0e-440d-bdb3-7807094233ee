name: Commits
on:
  - pull_request

permissions:
  contents: read

jobs:
  dco-check:
    permissions:
      pull-requests: read  # for tim-actions/get-pr-commits to get list of commits from the PR
    name: Signed-off-by (DCO)
    runs-on: ubuntu-24.04
    steps:
    - name: Get PR Commits
      id: 'get-pr-commits'
      uses: tim-actions/get-pr-commits@master
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Check that all commits are signed-off
      uses: tim-actions/dco@master
      with:
        commits: ${{ steps.get-pr-commits.outputs.commits }}

  target-branch:
    permissions:
      contents: none
    name: Branch target
    runs-on: ubuntu-24.04
    steps:
    - name: Check branch target
      env:
        TARGET: ${{ github.event.pull_request.base.ref }}
      run: |
        set -x
        [ "${TARGET}" = "main" ] && exit 0

        echo "Invalid branch target: ${TARGET}"
        exit 1
