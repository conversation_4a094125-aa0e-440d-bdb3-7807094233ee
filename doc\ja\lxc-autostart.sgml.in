<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [
    <!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
    <!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>
    <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>
    <refmeta>
        <refentrytitle>lxc-autostart</refentrytitle>
        <manvolnum>1</manvolnum>
    </refmeta>

    <refnamediv>
        <refname>lxc-autostart</refname>

        <refpurpose>
          <!--
            start/stop/kill auto-started containers
            -->
          自動起動の設定がされたコンテナの開始/停止/kill
        </refpurpose>
    </refnamediv>

    <refsynopsisdiv>
        <cmdsynopsis>
            <command>lxc-autostart</command>
            <arg choice="opt">-k</arg>
            <arg choice="opt">-L</arg>
            <arg choice="opt">-r</arg>
            <arg choice="opt">-s</arg>
            <arg choice="opt">-a</arg>
            <arg choice="opt">-A</arg>
            <arg choice="opt">-g <replaceable>groups</replaceable></arg>
            <arg choice="opt">-t <replaceable>timeout</replaceable></arg>
        </cmdsynopsis>
    </refsynopsisdiv>

    <refsect1>
        <title><!-- Description -->説明</title>

        <para>
          <!--
            <command>lxc-autostart</command> processes containers
            with lxc.start.auto set. It lets the user start, shutdown,
            kill, restart containers in the right order, waiting the
            right time. Supports filtering by lxc.group or just run
            against all defined containers. It can also be used by
            external tools in list mode where no action will be performed
            and the list of affected containers (and if relevant, delays)
            will be shown.
            -->
          <command>lxc-autostart</command> は lxc.start.auto が設定されたコンテナの処理を行います。
          ユーザがコンテナの開始、シャットダウン、kill、再起動を、設定した時間間隔で、設定した順番で行えるようにします。
          lxc.group でのフィルタリングによって、もしくは定義された全てのコンテナを実行します。
          何の動作も行わず、対象のコンテナ (とコンテナに設定された起動待機時間) のリストを表示するリストモードを外部ツールから使用することも可能です。
        </para>

        <para>
          <!--
            The <optional>-r</optional>, <optional>-s</optional>
            and <optional>-k</optional> options specify the action to perform.
            If none is specified, then the containers will be started.
            <optional>-a</optional> and <optional>-g</optional> are used to
            specify which containers will be affected. By default only
            containers without a lxc.group set will be affected.
            <optional>-t TIMEOUT</optional> specifies the maximum amount
            of time to wait for the container to complete the shutdown
            or reboot.
            -->
          <optional>-r</optional>, <optional>-s</optional>, <optional>-k</optional> オプションは実行する動作を指定します。何も指定しない場合は、コンテナを起動します。
          <optional>-a</optional>, <optional>-g</optional> は、どのコンテナを対象にするかを指定するのに使います。デフォルトでは、lxc.group が指定されていないコンテナにだけが対象となります。
          <optional>-t TIMEOUT</optional> はコンテナが完全にシャットダウンもしくはリブートを待つ最大時間を指定します。
        </para>
    </refsect1>

    <refsect1>
        <title><!-- Options -->オプション</title>
        <variablelist>
            <varlistentry>
                <term>
                    <option>-r,--reboot </option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Request a reboot of the container.
                        -->
                      コンテナのリブートを要求します。
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>-s,--shutdown </option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Request a clean shutdown. If a
                        <optional>-t timeout</optional> greater than 0 is
                        given and the container has not shut down within
                        this period, it will be killed as with the
                        <optional>-k kill</optional> option.
                        -->
                      クリーンなシャットダウンを要求します。もし、<optional>-t timeout</optional> が 0 より大きい場合で、コンテナがこの時間内にシャットダウンしない場合は、コンテナは <optional>-k kill</optional> オプションを指定した時のように kill されます。
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>-k,--kill </option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Rather than requesting a clean shutdown of the
                        container, explicitly kill all tasks in the container.
                        -->
                      コンテナのクリーンなシャットダウンを要求するのではなく、明確にコンテナの全てのタスクを kill します。
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>-L,--list </option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Rather than performing the action, just print
                        the container name and wait delays until starting the next container.
                        -->
                      実際の動作は行わず、コンテナ名と次のコンテナを開始するまでの間隔の表示だけを行います。
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>-t,--timeout <replaceable>TIMEOUT</replaceable></option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Wait TIMEOUT seconds before hard-stopping the container.
                        -->
                      コンテナの強制停止まで TIMEOUT 秒待ちます。
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>-g,--groups <replaceable>GROUP</replaceable></option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Comma separated list of groups to select
                        (defaults to those without a lxc.group - the NULL group).
                        This option may be specified multiple times
                        and the arguments concatenated.  The NULL or
                        empty group may be specified as a leading comma,
                        trailing comma, embedded double comma, or empty
                        argument where the NULL group should be processed.
                        Groups are processed in the order specified on the
                        command line.  Multiple invocations of the -g option
                        may be freely intermixed with the comma separated
                        lists and will be combined in specified order.
                        -->
                      対象にするコンテナのグループのカンマ区切りのリスト (デフォルトでは lxc.group 指定のないコンテナ、つまり NULL グループが対象になります)。
                      このオプションは複数回指定することができ、オプションは連結されます。NULL もしくは空のグループは、NULL グループを処理すべき場所に指定された先頭のカンマ、末尾のカンマ、途中に現れる 2 つ続きのカンマ、空のオプション引数で指定することができます。
                      グループはコマンドラインで指定された順番に処理されます。-g オプションの複数回の呼び出しはカンマ区切りのリストと自由に混ぜることができ、指定した順番に連結されます。
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>-a,--all</option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Ignore lxc.group and select all auto-started containers.
                        -->
                      lxc.group の指定を無視して、自動起動が設定されているコンテナを全て選択します。
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>-A,--ignore-auto</option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Ignore the lxc.start.auto flag. Combined with
                        -a, will select all containers on the system.
                        -->
                      lxc.start.auto で設定されているフラグを無視します。-a と組み合わせることにより、システム上の全てのコンテナを選択します。
                    </para>
                </listitem>
            </varlistentry>
        </variablelist>
    </refsect1>

    <refsect1>
        <title><!-- Autostart and System Boot -->自動起動とシステムブート</title>

        <para>
          <!--
            The <command>lxc-autostart</command> command is used as part of the
            LXC system service, when enabled to run on host system at bootup and at
            shutdown.  It's used to select which containers to start in what order
            and how much to delay between each startup when the host system boots.
            -->
          <command>lxc-autostart</command> コマンドは、LXC システムサービスがホストシステムのブートおよびシャットダウン時に実行するように有効化されているとき、LXC システムサービスの一部として使用されます。
          このコマンドはホストシステムのブート時に、どのコンテナをどういう順番で、それぞれのコンテナの起動間隔をどれくらい開けるかを選択するのに使います。
        </para>

        <para>
          <!--
            Each container can be part of any number of groups or no group at all.
            Two groups are special. One is the NULL group, i.e. the container does
            not belong to any group. The other group is the "onboot" group.
            -->
          コンテナはいくつでもグループに属することができ、全く属さないことも可能です。特別なグループが 2 つ存在します。1 つは NULL グループです。これはどのグループにも属さないコンテナです。もう 1 つは "onboot" グループです。
        </para>

        <para>
          <!--
            When the system boots with the LXC service enabled, it will first
            attempt to boot any containers with lxc.start.auto == 1 that is a member
            of the "onboot" group. The startup will be in order of lxc.start.order.
            If an lxc.start.delay has been specified, that delay will be honored
            before attempting to start the next container to give the current
            container time to begin initialization and reduce overloading the host
            system. After starting the members of the "onboot" group, the LXC system
            will proceed to boot containers with lxc.start.auto == 1 which are not
            members of any group (the NULL group) and proceed as with the onboot
            group.
            -->
          LXC サービスが有効になった状態でシステムがブートすると、最初に "onboot" グループのメンバーである lxc.start.auto == 1 が設定されたコンテナを起動しようとします。起動は lxc.start.order の順に起動します。
          lxc.start.delay が指定されている場合、現在対象となっているコンテナに初期化の時間を与え、ホストシステムの負荷を低減するために、次のコンテナを開始させるまでに遅延時間を与えます。
          "onboot" グループのメンバーが開始した後、LXC システムは lxc.start.auto == 1 が設定された、どのグループのメンバーでもない (NULL グループの) コンテナのブートを onboot グループのコンテナと同様に開始します。
        </para>
    </refsect1>

    <refsect1>
        <title><!-- Startup Group Examples -->スタートアップグループの例</title>
        <variablelist>
            <varlistentry>
                <term>
                    <option>-g "onboot,"</option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Start the "onboot" group first then the NULL group.
                        -->
                      まず最初に "onboot" グループの処理を開始し、その後 NULL グループの処理を開始します。
                    </para>
                    <para>
                      <!--
                        This is the equivalent of: <option>-g onboot -g ""</option>.
                        -->
                      これは以下と等価です: <option>-g onboot -g ""</option>
                    </para>
                </listitem>
            </varlistentry>
            <varlistentry>
                <term>
                    <option>-g "dns,web,,onboot"</option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Starts the "dns" group first, the "web" group second, then
                        the NULL group followed by the "onboot" group.
                        -->
                      まず最初に "dns" グループの処理を開始し、2 番目に "web" グループ、その後 NULL グループ、"onboot" グループの順に処理を開始します。
                    </para>
                    <para>
                      <!--
                        This is the equivalent of: <option>-g dns,web -g ,onboot</option> or <option>-g dns -g web -g "" -g onboot</option>.
                        -->
                      これは以下と等価です: <option>-g dns,web -g ,onboot</option> もしくは <option>-g dns -g web -g "" -g onboot</option>
                    </para>
                </listitem>
            </varlistentry>
        </variablelist>
    </refsect1>

    &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
