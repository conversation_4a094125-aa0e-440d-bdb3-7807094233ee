<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<refsect1>
  <title><!-- Common Options -->共通オプション</title>

  <para>
    <!--
    These options are common to most of lxc commands.
    -->
    ここで紹介するオプションは lxc コマンドの大部分で共通のものです。
  </para>

  <variablelist>
    <varlistentry>
      <term><option>-?, -h, --help</option></term>
      <listitem>
	<para>
          <!--
	  Print a longer usage message than normal.
          -->
          通常より長い使い方のメッセージを表示します。
	</para>
      </listitem>
    </varlistentry>
    <varlistentry>
      <term><option>--usage</option></term>
      <listitem>
	<para>
          <!--
	  Give the usage message
          -->
          使い方を表示します。
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-q, --quiet</option></term>
      <listitem>
	<para>
          <!--
	  mute on
          -->
          出力を抑制します。
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-P, --lxcpath=<replaceable>PATH</replaceable></option></term>
      <listitem>
	<para>
          <!--
	  Use an alternate container path.  The default is @LXCPATH@.
          -->
          デフォルトと別のコンテナパスを使用します。デフォルトは @LXCPATH@ です。
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-o, --logfile=<replaceable>FILE</replaceable></option></term>
      <listitem>
	<para>
          <!--
	  Output to an alternate log
	  <replaceable>FILE</replaceable>. The default is no log.
          -->
          追加のログを <replaceable>FILE</replaceable> に出力します。デフォルトは出力しません。
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-l, --logpriority=<replaceable>LEVEL</replaceable></option></term>
      <listitem>
	<para>
          <!--
	  Set log priority to
	  <replaceable>LEVEL</replaceable>. The default log
	  priority is <literal>ERROR</literal>. Possible values are :
	  <literal>FATAL</literal>, <literal>ALERT</literal>,
	  <literal>CRIT</literal>,
	  <literal>WARN</literal>, <literal>ERROR</literal>,
	  <literal>NOTICE</literal>, <literal>INFO</literal>,
	  <literal>DEBUG</literal>, <literal>TRACE</literal>.
          -->
          ログの優先度を <replaceable>LEVEL</replaceable> に設定します。デフォルトの優先度は <literal>ERROR</literal> です。以下の値を設定可能です:
	  <literal>FATAL</literal>, <literal>ALERT</literal>,
	  <literal>CRIT</literal>,
	  <literal>WARN</literal>, <literal>ERROR</literal>,
	  <literal>NOTICE</literal>, <literal>INFO</literal>,
	  <literal>DEBUG</literal>, <literal>TRACE</literal>。
	</para>
	<para>
          <!--
	Note that this option is setting the priority of the events
	log in the alternate log file. It do not have effect on the
	ERROR events log on stderr.
        -->
          このオプションは追加のログファイルへのイベントログの優先度の設定である事に注意してください。stderr への ERROR イベントのログには影響しません。
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-n, --name=<replaceable>NAME</replaceable></option></term>
      <listitem>
	<para>
          <!--
	  Use container identifier <replaceable>NAME</replaceable>.
	  The container identifier format is an alphanumeric string.
          -->
          <replaceable>NAME</replaceable> という名前でコンテナを識別します。コンテナ識別子のフォーマットは英数字の文字列です。
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
     <term><option>--rcfile=<replaceable>FILE</replaceable></option></term>
      <listitem>
	<para>
	  <!--
	  Specify the configuration file to configure the virtualization
	  and isolation functionalities for the container.
	  -->
	  コンテナの仮想化、隔離機能の設定のための設定ファイルを指定します。
	</para>
	<para>
	  <!--
	  This configuration file if present will be used even if there is
	  already a configuration file present in the previously created
	  container (via lxc-create).
	  -->
	  (lxc-create 経由で) 前もってコンテナが作られた際の設定ファイルが既にあった場合でも、このオプションが指定された場合は、指定した設定ファイルが使用されます。
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>--version</option></term>
      <listitem>
        <para>
          <!--
          Show the version number.
          -->
          バージョン番号を表示します。
        </para>
      </listitem>
    </varlistentry>
  </variablelist>

</refsect1>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
