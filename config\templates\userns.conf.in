# CAP_SYS_ADMIN in init-user-ns is required for cgroup.devices
#
# Default legacy cgroup configuration
#
lxc.cgroup.devices.deny =
lxc.cgroup.devices.allow =

# Default unified cgroup configuration
#
lxc.cgroup2.devices.deny =
lxc.cgroup2.devices.allow =

# Start with a full set of capabilities in user namespaces.
lxc.cap.drop =
lxc.cap.keep =

# We can't move bind-mounts, so don't use /dev/lxc/
lxc.tty.dir =

# Setup the default mounts
lxc.mount.auto = sys:rw

# Lastly, include all the configs from @LXCTEMPLATECONFIG@/userns.conf.d/
lxc.include = @LXCTEMPLATECONFIG@/userns.conf.d/
