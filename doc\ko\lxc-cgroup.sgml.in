<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-cgroup</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-cgroup</refname>

    <refpurpose>
      <!--
      manage the control group associated with a container
      -->
      컨테이너와 관련된 컨트롤 그룹 관리
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-cgroup</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="req"><replaceable>state-object</replaceable></arg>
      <arg choice="opt">value</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->설명</title>

    <para>
      <!--
      <command>lxc-cgroup</command> gets or sets the value of a
      <replaceable>state-object</replaceable> (e.g., 'cpuset.cpus')
      in the container's cgroup for the corresponding subsystem (e.g.,
      'cpuset'). If no <optional>value</optional> is specified, the
      current value of the <replaceable>state-object</replaceable> is
      displayed; otherwise it is set.
      -->
      <command>lxc-cgroup</command>는 지정한 서브시스템(예를 들어 'cpuset')의 컨테이너 cgroup의 <replaceable>state-object</replaceable> (예를들어 'cpuset.cpus')의 값을 얻어오거나 설정한다.
      만약 <optional>value</optional>가 지정되지 않았다면, <replaceable>state-object</replaceable>의 현재 값을 표시한다. 지정한 경우에는 해당 값으로 설정한다.
    </para>

    <para>
      <!--
      Note that <command>lxc-cgroup</command> does not check that the
      <replaceable>state-object</replaceable> is valid for the running
      kernel, or that the corresponding subsystem is contained in any
      mounted cgroup hierarchy.
      -->
      <command>lxc-cgroup</command>는 <replaceable>state-object</replaceable>가 실행중인 커널에서 사용가능한지 검사하지 않는 것을 주의해야 한다. 또한 지정한 서브시스템이 마운트된 cgroup에 포함이 되어 있는지도 검사하지 않는다.
    </para>

  </refsect1>

  <refsect1>
    <title><!-- Options -->옵션</title>
    <variablelist>

      <varlistentry>
	<term>
	  <option><replaceable>state-object</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the state object name.
            -->
            cgroup의 state object 이름을 지정한다.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><optional>value</optional></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the value to assign to the state object.
            -->
            cgroup의 state object에 설정할 값을 지정한다.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Examples -->예제</title>
    <variablelist>
      <varlistentry>
	<term>lxc-cgroup -n foo devices.list</term>
	<listitem>
	<para>
          <!--
	  display the allowed devices to be used.
          -->
          허용된 디바이스를 표시한다.
	</para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>lxc-cgroup -n foo cpuset.cpus "0,3"</term>
	<listitem>
	<para>
          <!--
	  assign the processors 0 and 3 to the container.
          -->
          프로세서 0과 3을 컨테이너에게 할당한다.
	</para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  <refsect1>
    <title><!-- Diagnostic -->진단</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
            <!--
	    The container is not running.
            -->
            컨테이너가 실행중이 아니다.
          </para>
        </listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
