<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-checkpoint</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-checkpoint</refname>

    <refpurpose>
      <!--
      checkpoint a container
      -->
      コンテナのチェックポイントとリストア
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-checkpoint</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="req">-D <replaceable>PATH</replaceable></arg>
      <arg choice="opt">-r</arg>
      <arg choice="opt">-s</arg>
      <arg choice="opt">-v</arg>
      <arg choice="opt">-d</arg>
      <arg choice="opt">-F</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>
    <para>
      <!--
      <command>lxc-checkpoint</command> checkpoints and restores containers.
      -->
      <command>lxc-checkpoint</command> はコンテナのチェックポイント処理による状態保存とリストアを行います。
    </para>
  </refsect1>

  <refsect1>
    <title><!-- Options -->オプション</title>
    <variablelist>

      <varlistentry>
        <term>
          <option>-r, --restore</option>
        </term>
        <listitem>
          <para>
            <!--
            Restore the checkpoint for the container, instead of dumping it.
            This option is incompatible with <option>-s</option>.
            -->
            コンテナの状態を保存するのでなく、チェックポイントからのリストアを行います。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-D <replaceable>PATH</replaceable>, --checkpoint-dir=<replaceable>PATH</replaceable></option>
        </term>
        <listitem>
          <para>
            <!--
            The directory to dump the checkpoint metadata.
            -->
            チェックポイントのメタデータを保存するディレクトリを指定します。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-s, --stop</option>
        </term>
        <listitem>
          <para>
            <!--
            Optionally stop the container after dumping. This option is
            incompatible with <option>-r</option>.
            -->
            コンテナの状態を保存した後にコンテナを停止します。このオプションは <option>-r</option> と同時に指定できません。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-v, --verbose</option>
        </term>
        <listitem>
          <para>
            <!--
            Enable verbose criu logging.
            -->
            CRIU のログ出力を冗長モードにします。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-d, --daemon</option>
        </term>
        <listitem>
          <para>
            <!--
            Restore the container in the background (this is the default).
            Only available when providing <option>-r</option>.
            -->
            コンテナをバックグラウンドで起動した状態でリストアします (これがデフォルトです)。<option>-r</option> を指定したときだけ使用できます。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-F, --foreground</option>
        </term>
        <listitem>
          <para>
            <!--
            Restore the container in the foreground. Only available when
            providing <option>-r</option>.
            -->
            コンテナなフォアグラウンドで起動した状態でリストアします。<option>-r</option> を指定したときだけ使用できます。
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Examples -->例</title>
    <variablelist>

      <varlistentry>
        <term>lxc-checkpoint -n foo -D /tmp/checkpoint</term>
        <listitem>
          <para>
            <!--
            Checkpoint the container foo into the directory /tmp/checkpoint.
            -->
            foo という名前のコンテナのチェックポイント処理を実行し、データを /tmp/checkpoint に保存します。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>lxc-checkpoint -r -n foo -D /tmp/checkpoint</term>
        <listitem>
          <para>
            <!--
            Restore the checkpoint from the directory /tmp/checkpoint.
            -->
            /tmp/checkpoint に保存されたチェックポイントデータからリストアを行います。
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
