<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-cgroup</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-cgroup</refname>

    <refpurpose>
      <!--
      manage the control group associated with a container
      -->
      コンテナに関係する control group の管理
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-cgroup</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="req"><replaceable>state-object</replaceable></arg>
      <arg choice="opt">value</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      <command>lxc-cgroup</command> gets or sets the value of a
      <replaceable>state-object</replaceable> (e.g., 'cpuset.cpus')
      in the container's cgroup for the corresponding subsystem (e.g.,
      'cpuset'). If no <optional>value</optional> is specified, the
      current value of the <replaceable>state-object</replaceable> is
      displayed; otherwise it is set.
      -->
      <command>lxc-cgroup</command> は、コンテナの cgroup の一致するサブシステム (例えば 'cpuset') の <replaceable>state-object</replaceable> (例えば 'cpuset.cpus') の値を取得したり、値を設定したりします。
      <optional>value</optional> が指定されないときは、<replaceable>state-object</replaceable> の値を表示します。指定されている場合は値を設定します。
    </para>

    <para>
      <!--
      Note that <command>lxc-cgroup</command> does not check that the
      <replaceable>state-object</replaceable> is valid for the running
      kernel, or that the corresponding subsystem is contained in any
      mounted cgroup hierarchy.
      -->
      <command>lxc-cgroup</command> は <replaceable>state-object</replaceable> が実行中のカーネルで有効かどうかのチェックを行いません。
      また、指定したサブシステムがマウントされた cgroup の階層構造に含まれているかどうかのチェックを行いません。
    </para>

  </refsect1>

  <refsect1>
    <title><!-- Options -->オプション</title>
    <variablelist>

      <varlistentry>
	<term>
	  <option><replaceable>state-object</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the state object name.
            -->
            (cgroup の) 状態オブジェクト (state object) 名を指定します。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><optional>value</optional></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the value to assign to the state object.
            -->
            (cgroup の) 状態オブジェクト (state object) に設定する値を指定します。
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Examples -->例</title>
    <variablelist>
      <varlistentry>
	<term>lxc-cgroup -n foo devices.list</term>
	<listitem>
	<para>
          <!--
	  display the allowed devices to be used.
          -->
          使用が許可されているデバイスを表示します。
	</para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>lxc-cgroup -n foo cpuset.cpus "0,3"</term>
	<listitem>
	<para>
          <!--
	  assign the processors 0 and 3 to the container.
          -->
          コンテナにプロセッサ番号 0 と 3 のプロセッサを割り当てます。
	</para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  <refsect1>
    <title><!-- Diagnostic -->診断</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
            <!--
	    The container is not running.
            -->
            コンテナが実行されていません。
          </para>
        </listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
