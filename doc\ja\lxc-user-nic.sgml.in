<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-user-nic</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-user-nic</refname>

    <refpurpose>
      <!--
      Manage nics in another namespace
      -->
      現在と異なるネットワーク名前空間の NIC を管理する
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-user-nic</command>
      <command>create</command>
      <arg choice="req"><replaceable>lxcpath</replaceable></arg>
      <arg choice="req"><replaceable>name</replaceable></arg>
      <arg choice="req"><replaceable>pid</replaceable></arg>
      <arg choice="req"><replaceable>type</replaceable></arg>
      <arg choice="req"><replaceable>bridge</replaceable></arg>
      <arg choice="opt"><replaceable>container nicname</replaceable></arg>
    </cmdsynopsis>

     <cmdsynopsis>
      <command>lxc-user-nic</command>
      <command>delete</command>
      <arg choice="req"><replaceable>lxcpath</replaceable></arg>
      <arg choice="req"><replaceable>name</replaceable></arg>
      <arg choice="req"><replaceable>path to network namespace</replaceable></arg>
      <arg choice="req"><replaceable>type</replaceable></arg>
      <arg choice="req"><replaceable>bridge</replaceable></arg>
      <arg choice="req"><replaceable>container nicname</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      <command>lxc-user-nic</command> is a setuid-root program with which
      unprivileged users may create network interfaces for use by a
      lxc container.
      -->
      <command>lxc-user-nic</command> は root に setuid されたプログラムで、lxc コンテナが使うネットワークインターフェースを、特権を持たないユーザが作成できます。
    </para>
    <para>
      <!--
      It will consult the configuration file <filename>@LXC_USERNIC_CONF@</filename>
      to determine the number of interfaces which the calling user is allowed to
      create, and which bridge they may attach them to.  It tracks the
      number of interfaces each user has created using the file
      <filename>@LXC_USERNIC_DB@</filename>.  It ensures that the calling
      user is privileged over the network namespace to which the interface
      will be attached.
      <command>lxc-user-nic</command> also allows one to delete network devices.
      Currently only ovs ports can be deleted.
      -->
      このプログラムは、<filename>@LXC_USERNIC_CONF@</filename> という設定ファイルを参照して、呼び出したユーザが作成することができるインターフェースの数と、どのブリッジに接続するかを決定します。
      また、ユーザが作成したインターフェースの数を <filename>@LXC_USERNIC_DB@</filename> を使ってチェックします。
      これにより、呼び出したユーザが、インターフェースを割り当てるネットワーク名前空間上で特権を持つことが保証されます。
      <command>lxc-user-nic</command> はネットワークデバイスを削除することもできます。
      現時点では ovs ポートの削除のみ可能です。
    </para>

  </refsect1>

  <refsect1>

    <title><!-- Options -->オプション</title>

    <variablelist>
      <varlistentry>
	<term>
	  <option><replaceable>lxcpath</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	  The path of the container. This is currently not used.
	      -->
	    すべてのコンテナが保存されるパス。これは現在使われていません。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>name</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	  The name of the container. This is currently not used.
	      -->
	    コンテナ名。これは現在使われていません。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>pid</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	  The process id for the task to whose network namespace the interface
	  should be attached.
              -->
            インターフェースを割り当てたいネットワーク名前空間を持つタスクのプロセス ID。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>type</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	  The network interface type to attach. Currently only veth is
	  supported.  With this type, two interfaces representing each
	  tunnel endpoint are created.  One endpoint will be attached
	  to the specified bridge, while the other will be passed into
	  the container.
              -->
            割り当てるネットワークインターフェースのタイプ。現時点では、veth のみサポートされます。
            このタイプを指定すると、それぞれがトンネルのエンドポイントとなる 2 つのインターフェースが作成されます。
            一方のエンドポイントは指定したブリッジに接続され、もう一方はコンテナに割り当てられます。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>bridge</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	  The bridge to which to attach the network interface, for
	  instance <filename>lxcbr0</filename>.
              -->
            ネットワークインターフェースを接続するブリッジ。
            例えば <filename>lxcbr0</filename> のように指定します。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>container nicname</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	  The desired interface name in the container. This will be
	  <filename>eth0</filename> if unspecified.
          -->
            コンテナ内に作られるインターフェースの名前。
            もし指定しない場合、<filename>eth0</filename> となります。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>path to network namespace</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	  A path to open to get a file descriptor for the target
	  network namespace.
	  This is only relevant when an veth device is deleted.
	      -->
	    対象のネットワーク名前空間のファイルディスクリプタを取得するために open するためのパス。
	    これは veth デバイスを削除するときのみ関係します。
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  <refsect1>
    <title>See Also</title>

    <simpara>
      <citerefentry>
	<refentrytitle><command>lxc</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>,

      <citerefentry>
	<refentrytitle><command>lxc-start</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>,

      <citerefentry>
	<refentrytitle><command>lxc-usernet</command></refentrytitle>
	<manvolnum>5</manvolnum>
      </citerefentry>
    </simpara>
   </refsect1>
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
