<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-usernsexec</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-usernsexec</refname>

    <refpurpose>
      Run a task as root in a new user namespace.
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-usernsexec</command>
      <arg choice="opt">-m <replaceable>uid-map</replaceable></arg>
      <arg choice="req">-- command</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-usernsexec</command> can be used to run a task as root
      in a new user namespace.
    </para>

  </refsect1>

  <refsect1>

    <title>Options</title>

    <variablelist>

      <varlistentry>
	<term>
	  <option>-m <replaceable>uid-map</replaceable></option>
	</term>
	<listitem>
	  <para>
	  The uid map to use in the user namespace.  Each map consists of
	  four colon-separate values.  First a character 'u', 'g' or 'b' to
	  specify whether this map pertains to user ids, group ids, or
	  both; next the first userid in the user namespace;  next the
	  first userid as seen on the host;  and finally the number of
	  ids to be mapped.
	  </para>
	  <para>
	  More than one map can be specified.  If no map is
	  specified, then by default the full uid and gid ranges granted
	  by /etc/subuid and /etc/subgid will be mapped to the
	  uids and gids starting at 0 in the container.
	  </para>
	  <para>
	  Note that <replaceable>lxc-usernsexec</replaceable> always tries
	  to setuid and setgid to 0 in the namespace.  Therefore uid 0 in
	  the namespace must be mapped.
	  </para>
	</listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  <refsect1>
    <title>Examples</title>
      <para>
        To spawn a shell with the full allotted subuids mapped into
	the container, use
        <programlisting>
	  lxc-usernsexec
        </programlisting>
	To run a different shell than <replaceable>/bin/sh</replaceable>, use
        <programlisting>
	  lxc-usernsexec -- /bin/bash
        </programlisting>
      </para>
      <para>
	If your user id is 1000, root in a container is mapped to 190000, and
	you wish to chown a file you own to root in the container, you can use:
        <programlisting>
	  lxc-usernsexec -m b:0:1000:1 -m b:1:190000:1 -- /bin/chown 1:1 $file
        </programlisting>
	This maps your userid to root in the user namespace, and 190000 to uid 1.
	Since root in the user namespace is privileged over all userids mapped
	into the namespace, you are allowed to change the file ownership, which
	you could not do on the host using a simple chown.
      </para>
  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
