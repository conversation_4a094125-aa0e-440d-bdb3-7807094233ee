<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc.container.conf</refentrytitle>
    <manvolnum>5</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc.container.conf</refname>

    <refpurpose>
      <!--
      LXC container configuration file
      -->
      LXC コンテナ設定ファイル
    </refpurpose>
  </refnamediv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      LXC is the well-known and heavily tested low-level Linux container
      runtime. It is in active development since 2008 and has proven itself in
      critical production environments world-wide. Some of its core contributors
      are the same people that helped to implement various well-known
      containerization features inside the Linux kernel.
        -->
      LXC は良く知られた、多くのテストが行われた Linux コンテナのランタイムです。LXC は、2008 年以来アクティブに開発されており、世界中の重要な本番環境で実証されています。開発への貢献者の中には、Linux カーネル内の良く知られた様々なコンテナ機能の実装に貢献した人と同じ人もいます。
    </para>

    <para>
      <!--
      LXC's main focus is system containers. That is, containers which offer an
      environment as close as possible as the one you'd get from a VM but
      without the overhead that comes with running a separate kernel and
      simulating all the hardware.
        -->
      LXC は主にシステムコンテナにフォーカスを当てています。つまり、VM で得られる環境と可能な限り近い環境を提供を提供するにも関わらず、別々のカーネルを実行したり、ハードウェアをすべてシミュレートしたりすることによるオーバーヘッドがないコンテナのことです。
    </para>

    <para>
      <!--
      This is achieved through a combination of kernel security features such as
      namespaces, mandatory access control and control groups.
        -->
      このような環境は、名前空間 (namespace)、強制アクセスコントロール、cgroup といったカーネルのセキュリティ機能の組み合わせで実現しています。
    </para>

    <para>
      <!--
      LXC has supports unprivileged containers.  Unprivileged containers are
      containers that are run without any privilege.  This requires support for
      user namespaces in the kernel that the container is run on.  LXC was the
      first runtime to support unprivileged containers after user namespaces
      were merged into the mainline kernel.
        -->
      LXC は非特権コンテナをサポートしています。非特権コンテナは、いかなる特権も持たずに実行するコンテナです。非特権コンテナの実行には、コンテナを実行しているカーネルにユーザ名前空間 (user namespace) のサポートが必要です。LXC は、ユーザ名前空間がメインラインカーネルにマージされてから、初めて非特権コンテナをサポートしたランタイムです。
    </para>

    <para>
      <!--
      In essence, user namespaces isolate given sets of UIDs and GIDs. This is
      achieved by establishing a mapping between a range of UIDs and GIDs on the
      host to a different (unprivileged) range of UIDs and GIDs in the
      container. The kernel will translate this mapping in such a way that
      inside the container all UIDs and GIDs appear as you would expect from the
      host whereas on the host these UIDs and GIDs are in fact unprivileged. For
      example, a process running as UID and GID 0 inside the container might
      appear as UID and GID 100000 on the host.  The implementation and working
      details can be gathered from the corresponding user namespace man page.
      UID and GID mappings can be defined with the <option>lxc.idmap</option>
      key.
        -->
      本質的には、ユーザ名前空間は与えられた UID、GID の組を隔離します。ユーザ名前空間は、ホスト上の UID、GID のある範囲を、それとは異なるコンテナ上の UID、GID の範囲へマッピングすることで実現します。カーネルは、ホスト上では実際には UID、GID は特権を持たないにも関わらず、コンテナ内ではすべての UID、GID が期待されるように見えるように変換を行います。
      例えば、コンテナ内では UID、GID が 0 として実行中のプロセスは、ホスト上では UID、GID が 100000 として見えるでしょう。実装と動作の詳細は、ユーザ名前空間の man ページから得られます。UID と GID のマッピングは <option>lxc.idmap</option> を使って定義できます。
    </para>

    <para>
      <!--
      Linux containers are defined with a simple configuration file.  Each
      option in the configuration file has the form <command>key =
      value</command> fitting in one line. The "#" character means the line is a
      comment. List options, like capabilities and cgroups options, can be used
      with no value to clear any previously defined values of that option.
        -->
      Linux コンテナは、簡単な設定ファイルで定義します。設定ファイル中のオプションは <command>key = value</command> の形で一行で表します。'#' は、その行はコメントであることを示します。ケーパビリティや cgroup のオプションのような、リスト形式で指定するオプションでは、value がない形式で指定でき、そのように使うと、それ以前に定義した値をすべてクリアします。
    </para>

    <para>
      <!--
      LXC namespaces configuration keys by using single dots. This means complex
      configuration keys such as <option>lxc.net.0</option> expose various
      subkeys such as <option>lxc.net.0.type</option>,
      <option>lxc.net.0.link</option>, <option>lxc.net.0.ipv6.address</option>, and
      others for even more fine-grained configuration.
      -->
      LXC は、シングルドットを使って設定キーの名前空間を表します。<option>lxc.net.0</option> のような複雑な設定キーは、<option>lxc.net.0.type</option>、<option>lxc.net.0.link</option>、<option>lxc.net.0.ipv6.address</option> や、さらに細分化された設定向けの色々なサブキーを持つことを意味します。
    </para>

    <refsect2>
      <title><!-- Configuration -->設定</title>
      <para>
        <!--
        In order to ease administration of multiple related containers, it is
        possible to have a container configuration file cause another file to be
        loaded. For instance, network configuration can be defined in one common
        file which is included by multiple containers. Then, if the containers
        are moved to another host, only one file may need to be updated.
          -->
        複数の関係するコンテナの管理を容易にするために、コンテナの設定ファイルに別のファイルをロードすることが可能です。
        例えば、ネットワークの設定を、複数のコンテナから include させるように 1 つのファイルに定義することが可能です。
        その場合、コンテナが他のホストに移動すると、そのファイルだけを更新する必要があるかもしれません。
      </para>

      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.include</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the file to be included.  The included file must be
              in the same valid lxc configuration file format.
              -->
              include させたいファイルを指定します。
              include するファイルは、lxc 設定ファイルのフォーマットとして有効でなければいけません。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Architecture -->アーキテクチャ</title>
      <para>
        <!--
        Allows one to set the architecture for the container. For example, set a
        32bits architecture for a container running 32bits binaries on a 64bits
        host. This fixes the container scripts which rely on the architecture to
        do some work like downloading the packages.
        -->
        コンテナに対してアーキテクチャを設定することが可能です。
        例えば、64 ビットのホスト上で 32 ビットのバイナリを動かすために 32 ビットアーキテクチャを設定することが可能です。
        この設定を行うことにより、パッケージのダウンロードを行うなどの作業のうち、アーキテクチャ名に依存するような作業を行うコンテナスクリプトの修正を行います。
      </para>

      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.arch</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the architecture for the container.
              -->
              コンテナに設定するアーキテクチャを指定します。
            </para>
            <para>
              <!--
              Some valid options are
              <option>x86</option>,
              <option>i686</option>,
              <option>x86_64</option>,
              <option>amd64</option>
              -->
              有効なオプションには以下のようなものがあります。
              <option>x86</option>,
              <option>i686</option>,
              <option>x86_64</option>,
              <option>amd64</option>
            </para>
          </listitem>
        </varlistentry>
      </variablelist>

    </refsect2>

    <refsect2>
      <title><!-- Hostname -->ホスト名</title>
      <para>
        <!--
        The utsname section defines the hostname to be set for the container.
        That means the container can set its own hostname without changing the
        one from the system. That makes the hostname private for the container.
        -->
        utsname セクションは、コンテナに設定されるホスト名を定義します。
        コンテナは、システムのホスト名を変えることなく、自身のホスト名を持つ事が可能です。
        このことにより、ホスト名はコンテナ専用となります。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.uts.name</option>
          </term>
          <listitem>
            <para>
              <!--
              specify the hostname for the container
              -->
              コンテナのホスト名を指定します。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Halt signal -->クリーンなシャットダウン時のシグナル</title>
      <para>
        <!--
        Allows one to specify signal name or number sent to the container's
        init process to cleanly shutdown the container. Different init systems
        could use different signals to perform clean shutdown sequence. This
        option allows the signal to be specified in kill(1) fashion, e.g.
        SIGPWR, SIGRTMIN+14, SIGRTMAX-10 or plain number. The default signal is
        SIGPWR.
          -->
        コンテナをクリーンにシャットダウンするためにコンテナの init プロセスに送るシグナル名か番号を指定できます。init システムによって、クリーンなシャットダウンを行うために使うシグナルは異なります。このオプションではシグナルとして kill(1) で使う形式を指定できます。
        例えば SIGKILL, SIGRTMIN+14, SIGRTMAX-10 のような形式、もしくは数字を指定します。デフォルトのシグナルは SIGPWR です。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.signal.halt</option>
          </term>
          <listitem>
            <para>
              <!--
              specify the signal used to halt the container
              -->
              コンテナをシャットダウンするために使うシグナルを指定します。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>リブート時のシグナル <!-- Reboot signal --></title>
      <para>
        <!--
        Allows one to specify signal name or number to reboot the container.
        This option allows signal to be specified in kill(1) fashion, e.g.
        SIGTERM, SIGRTMIN+14, SIGRTMAX-10 or plain number. The default signal
        is SIGINT.
          -->
        コンテナをリブートするために送るシグナル名か番号を指定できます。このオプションではシグナルとして kill(1) で使う形式を指定できます。
        例えば SIGKILL, SIGRTMIN+14, SIGRTMAX-10 のような形式、もしくは数字を指定します。デフォルトのシグナルは SIGINT です。
          </para>
          <variablelist>
        <varlistentry>
          <term>
            <option>lxc.signal.reboot</option>
          </term>
          <listitem>
            <para>
              <!--
              specify the signal used to reboot the container
                -->
              コンテナをリブートするために使うシグナルを指定します。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Stop signal -->強制停止時のシグナル</title>
      <para>
        <!--
        Allows one to specify signal name or number to forcibly shutdown the
        container. This option allows signal to be specified in kill(1) fashion,
        e.g. SIGKILL, SIGRTMIN+14, SIGRTMAX-10 or plain number.  The default
        signal is SIGKILL.
          -->
        コンテナを強制的にシャットダウンするために送るシグナル名か番号を指定できます。このオプションではシグナルとして kill(1) で使う形式を指定できます。
        例えば SIGKILL, SIGRTMIN+14, SIGRTMAX-10 のような形式、もしくは数字を指定します。デフォルトのシグナルは SIGKILL です。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.signal.stop</option>
          </term>
          <listitem>
            <para>
              <!--
                  specify the signal used to stop the container
                -->
              コンテナを停止するのに使用するシグナルを指定します。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Init command -->Init コマンド</title>
      <para>
        <!--
        Sets the command to use as the init system for the containers.
          -->
        コンテナの init として使うコマンドを設定します。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.execute.cmd</option>
          </term>
          <listitem>
            <para>
              <!--
              Absolute path from container rootfs to the binary to run by default.  This
              mostly makes sense for lxc-execute.
              -->
              デフォルトで実行するバイナリのコンテナの root からの絶対パスを指定します。これは <command>lxc-execute</command> のための設定です。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.init.cmd</option>
          </term>
          <listitem>
            <para>
              <!--
                  Absolute path from container rootfs to the binary to use as init. This
                  mostly makes sense for lxc-start. Default is /sbin/init.
                -->
              init として使うバイナリの、コンテナの root からの絶対パスを指定します。これは <command>lxc-start</command> のための設定です。デフォルトは <command>/sbin/init</command> です。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>Init のワーキングディレクトリ <!-- Init working directory --></title>
      <para>
        <!--
        Sets the absolute path inside the container as the working directory for the containers.
        LXC will switch to this directory before executing init.
          -->
        コンテナのワーキングディレクトリとして、コンテナ内の絶対パスを設定します。LXC は init を実行する前に、このディレクトリに移動します。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.init.cwd</option>
          </term>
          <listitem>
            <para>
              <!--
              Absolute path inside the container to use as the working directory.
                -->
              ワーキングディレクトリとして使うコンテナ内の絶対パス
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Init ID -->Init が使う ID</title>
      <para>
        <!--
        Sets the UID/GID to use for the init system, and subsequent commands.
        Note that using a non-root uid when booting a system container will
        likely not work due to missing privileges. Setting the UID/GID is mostly
        useful when running application container.

        Defaults to: UID(0), GID(0)
          -->
        init と後続のコマンドが使う UID/GID を設定します。システムコンテナを起動するのに非 root な UID を使うと、特権がないために動作しないでしょう。UID/GID の指定は、通常はアプリケーションコンテナの動作の際に役に立ちます。

        デフォルト値: UID(0)、GID(0)
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.init.uid</option>
          </term>
          <listitem>
            <para>
              <!--
              UID to use for init.
              -->
              init が使う UID です。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.init.gid</option>
          </term>
          <listitem>
            <para>
              <!--
              GID to use for init.
                -->
              init が使う GID です。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Core Scheduling -->コアスケジューリング</title>
      <para>
        <!--
        Core scheduling defines if the container payload
        is marked as being schedulable on the same core. Doing so will cause
        the kernel scheduler to ensure that tasks that are not in the same
        group never run simultaneously on a core. This can serve as an extra
        security measure to prevent the container payload from using
        cross hyper thread attacks.
        -->
        コアスケジューリングは、コンテナのペイロードが同じコアでスケジュール可能であるとマークするかどうかを指定します。
        これによりカーネルスケジューラーは、同じグループに属さないタスクが同一コア上で同時に実行されないようにします。
        これは、コンテナペイロードがクロスハイパースレッド攻撃を受けることを防ぐための、追加のセキュリティ対策として機能させることができます。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.sched.core</option>
          </term>
          <listitem>
            <para>
              <!--
              The only allowed values are 0 and 1. Set this to 1 to create a
              core scheduling domain for the container or 0 to not create one.
              If not set explicitly no core scheduling domain will be created
              for the container.
                -->
              0 または 1 のみ指定できます。1 を設定すると、コンテナに対するコアスケジューリングドメインを作成し、0 を設定すると作成しません。
              明示的に指定していない場合は、コンテナに対するコアスケジューリングドメインは作成されません。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>Proc</title>
      <para>
        <!--
        Configure proc filesystem for the container.
          -->
        コンテナ内の proc ファイルシステムで設定できるパラメータを設定します。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.proc.[proc file name]</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the proc file name to be set. The file name available
              are those listed under /proc/PID/.
              Example:
                -->
              設定したい proc ファイルシステムのファイル名を指定します。指定できるファイル名は /proc/PID/ 以下に存在するものです。
              例:
            </para>
            <programlisting>
              lxc.proc.oom_score_adj = 10
            </programlisting>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Ephemeral -->一時的なコンテナ</title>
      <para>
        <!--
        Allows one to specify whether a container will be destroyed on shutdown.
          -->
        シャットダウン後にコンテナを削除するかどうかを指定できます。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.ephemeral</option>
          </term>
          <listitem>
            <para>
              <!--
              The only allowed values are 0 and 1. Set this to 1 to destroy a
              container on shutdown. 
                -->
              指定できる値は 0 または 1 のみです。この値を 1 に設定すると、シャットダウン後にコンテナを削除します。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Network -->ネットワーク</title>
      <para>
        <!--
        The network section defines how the network is virtualized in
        the container. The network virtualization acts at layer
        two. In order to use the network virtualization, parameters
        must be specified to define the network interfaces of the
        container. Several virtual interfaces can be assigned and used
        in a container even if the system has only one physical
        network interface.
        -->
        ネットワークセクションは、コンテナ内でどのようにネットワークを仮想化するかを定義します。
        ネットワークの仮想化はレイヤー 2 で作動します。
        ネットワークの仮想化を使用するためには、コンテナのネットワークインターフェースを定義しなければなりません。
        いくつかの仮想インターフェースをアサインすることができます。
        そして、仮に物理ネットワークインターフェースが一つしかなくても、コンテナ内でいくつもの仮想インターフェースを使うことができます。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.net</option>
          </term>
          <listitem>
            <para>
              <!--
              may be used without a value to clear all previous network options.
              -->
              値を指定せずに使い、それ以前に定義されたすべてのネットワークオプションをクリアできます。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.net.[i].type</option>
          </term>
          <listitem>
            <para>
              <!--
              specify what kind of network virtualization to be used
              for the container.
              Must be specified before any other option(s) on the net device.
              Multiple networks can be specified by using an additional index
              <option>i</option>
              after all <option>lxc.net.*</option> keys. For example,
              <option>lxc.net.0.type = veth</option> and
              <option>lxc.net.1.type = veth</option> specify two different
              networks of the same type. All keys sharing the same index
              <option>i</option> will be treated as belonging to the same
              network. For example, <option>lxc.net.0.link = br0</option>
              will belong to <option>lxc.net.0.type</option>.
              Currently, the different virtualization types can be:
              -->
              コンテナがどの種類のネットワーク仮想化を使うかを指定します。ネットワークデバイスの他のオプションを設定する前に指定しなければいけません。
              すべての <option>lxc.net.*</option> キーに、追加のインデックス <option>i</option> を使うと、複数のネットワークを指定できます。例えば、<option>lxc.net.0.type = veth</option> と <option>lxc.net.1.type = veth</option> は、同じタイプの異なるネットワークを 2 つ指定します。
              同じインデックスを指定したキーはすべて同じネットワークの指定になります。例えば、<option>lxc.net.0.link = br0</option> は <option>lxc.net.0.type</option> と同じネットワークの設定になります。
              現時点では、以下のネットワーク仮想化のタイプが使えます:
            </para>

            <para>
              <!--
              <option>empty:</option> will create only the loopback
              interface.
              -->
              <option>empty:</option> ループバックインターフェースだけを作成します。
            </para>

            <para>
              <!--
              <option>veth:</option> a virtual ethernet pair
              device is created with one side assigned to the container
              and the other side on the host.
              <option>lxc.net.[i].veth.mode</option> specifies the
              mode the veth parent will use on the host.
              The accepted  modes are <option>bridge</option> and <option>router</option>.
              The mode defaults to bridge if not specified.
              In <option>bridge</option> mode the host side is attached to a bridge specified by
              the <option>lxc.net.[i].link</option> option.
              If the bridge link is not specified, then the veth pair device
              will be created but not attached to any bridge.
              Otherwise, the bridge has to be created on the system
              before starting the container.
              <command>lxc</command> won't handle any
              configuration outside of the container.
              In <option>router</option> mode static routes are created on the host for the
              container's IP addresses pointing to the host side veth interface.
              Additionally Proxy ARP and Proxy NDP entries are added on the host side veth interface
              for the gateway IPs defined in the container to allow the container to reach the host.
              By default, <command>lxc</command> chooses a name for the
              network device belonging to the outside of the
              container, but if you wish to handle
              this name yourselves, you can tell <command>lxc</command>
              to set a specific name with
              the <option>lxc.net.[i].veth.pair</option> option (except for
              unprivileged containers where this option is ignored for security
              reasons).

              Static routes can be added on the host pointing to the container using the
              <option>lxc.net.[i].veth.ipv4.route</option> and
              <option>lxc.net.[i].veth.ipv6.route</option> options.
              Several lines specify several routes.
              The route is in format x.y.z.t/m, eg. ***********/24.

              In <option>bridge</option> mode untagged VLAN membership can be set with the
              <option>lxc.net.[i].veth.vlan.id</option> option. It accepts a special value of 'none' indicating
              that the container port should be removed from the bridge's default untagged VLAN.
              The <option>lxc.net.[i].veth.vlan.tagged.id</option> option can be specified multiple times to set
              the container's bridge port membership to one or more tagged VLANs.
              -->
              <option>veth:</option> 一方がコンテナに、もう一方がホストに接続されるペアの仮想イーサネットデバイスを作成します。
              <option>lxc.net.[i].veth.mode</option> は、veth の親（ホスト側）がホスト上で使うモードを指定します。
              指定できるモードは <option>bridge</option> と <option>router</option> です。
              指定しない場合のデフォルトのモードは bridge です。
              <option>bridge</option> モードでは、ホスト側は、<option>lxc.net.[i].link</option> オプションで指定したブリッジに接続されます。
              もし、ブリッジが指定されていない場合、veth ペアデバイスは作成されますが、ブリッジには接続されません。
              ブリッジはコンテナが開始する前にシステムで事前に設定しておく必要があります。
              <command>lxc</command> はコンテナ外の設定を扱うことはありません。
              <option>router</option> モードでは、ホスト側の veth インターフェースを指すコンテナの IP アドレスに対して、ホスト上でスタティックルートが作成されます。
              加えて、コンテナがホストに到達できるために、コンテナ内で定義されたゲートウェイの IP アドレスに対して、Proxy ARP と Proxy NDP エントリがホスト側の veth インターフェースに追加されます。
              デフォルトでは、<command>lxc</command> がコンテナの外部に属するネットワークデバイスに対する名前を決定します。
              しかし、もしこの名前を自分で指定したい場合、<option>lxc.net.[i].veth.pair</option> オプションを使って名前を設定し、lxc に対して指定をすることができます (非特権コンテナの場合をのぞきます。セキュリティ上の理由からこのオプションは無視されます)。

              <option>lxc.net.[i].veth.ipv4.route</option>、<option>lxc.net.[i].veth.ipv6.route</option> オプションを使って、静的ルーティングをコンテナを指し示すホスト上に追加できます。
              複数のルートがある場合は複数の設定を指定します。
              ルートは x.y.z.t/m の形式です。例: ***********/24

	      <option>bridge</option> モードでは、タグなし VLAN は <option>lxc.net.[i].veth.vlan.id</option> で設定できます。このオプションでは、コンテナポートをブリッジのデフォルトのタグなし VLAN から削除するための特別な値 'none' が指定できます。コンテナのブリッジポートを複数のタグ付き VLAN に所属させるために、<option>lxc.net.[i].veth.vlan.tagged.id</option> を複数回指定できます。
            </para>

            <para>
              <!--
              <option>vlan:</option> a vlan interface is linked with
              the interface specified by
              the <option>lxc.net.[i].link</option> and assigned to
              the container. The vlan identifier is specified with the
              option <option>lxc.net.[i].vlan.id</option>.
              -->
              <option>vlan:</option> vlan インターフェースは <option>lxc.net.[i].link</option> で指定されたインターフェースとリンクし、コンテナに割り当てられます。
              vlan の指定は <option>lxc.net.[i].vlan.id</option> オプションで指定します。
            </para>

            <para>
              <!--
              <option>macvlan:</option> a macvlan interface is linked
              with the interface specified by
              the <option>lxc.net.[i].link</option> and assigned to
              the container.
              <option>lxc.net.[i].macvlan.mode</option> specifies the
              mode the macvlan will use to communicate between
              different macvlan on the same upper device. The accepted
              modes are <option>private</option>, <option>vepa</option>,
              <option>bridge</option> and <option>passthru</option>.
              In <option>private</option> mode, the device never
              communicates with any other device on the same upper_dev (default).
              In <option>vepa</option> mode, the new Virtual Ethernet Port
              Aggregator (VEPA) mode, it assumes that the adjacent
              bridge returns all frames where both source and
              destination are local to the macvlan port, i.e. the
              bridge is set up as a reflective relay.  Broadcast
              frames coming in from the upper_dev get flooded to all
              macvlan interfaces in VEPA mode, local frames are not
              delivered locally. In <option>bridge</option> mode, it
              provides the behavior of a simple bridge between
              different macvlan interfaces on the same port. Frames
              from one interface to another one get delivered directly
              and are not sent out externally. Broadcast frames get
              flooded to all other bridge ports and to the external
              interface, but when they come back from a reflective
              relay, we don't deliver them again.  Since we know all
              the MAC addresses, the macvlan bridge mode does not
              require learning or STP like the bridge module does. In
              <option>passthru</option> mode, all frames received by
              the physical interface are forwarded to the macvlan
              interface. Only one macvlan interface in <option>passthru</option>
              mode is possible for one physical interface.
              -->
              <option>macvlan:</option> macvlan インターフェースは <option>lxc.net.[i].link</option> により指定されるインターフェースとリンクし、コンテナに割り当てられます。
              <option>lxc.net.[i].macvlan.mode</option> でモードを指定すると、その macvlan の指定を、同じ上位デバイスで異なる macvlan 間の通信をする時に使います。
              指定できるモードは <option>private</option>、<option>vepa</option>、<option>bridge</option>、<option>passthru</option> のいずれかです。
              <option>private</option> モードの場合、デバイスは同じ上位デバイスの他のデバイスとの通信を行いません (デフォルト)。
              新しい仮想イーサネットポート集約モード (Virtual Ethernet Port Aggregator (VEPA)) である <option>vepa</option> モードの場合、隣接したポートが、ソースとデスティネーションの両方が macvlan ポートに対してローカルであるフレームを全て返すと仮定します。
              すなわち、ブリッジが reflective relay として設定されているということです。
              上位デバイスから入ってくるブロードキャストフレームは、VEPA モードである全ての macvlan インターフェースに送りつけられます。
              ローカルのフレームはローカルには配送されません。
              <option>bridge</option> モードの場合、同じポートの異なる macvlan インターフェースの間のシンプルなブリッジとして動作します。
              あるインターフェースから他のインターフェースへのフレームは、直接配送され、外部には送出されません。
              ブロードキャストフレームは、全ての他のブリッジと外部のインターフェースに対して送られます。
              しかし、reflective relay からフレームが返ってきたときは、再度それを配送することはしません。
              全ての MAC アドレスを知っているので、ブリッジモジュールのように、macvlan ブリッジモードは学習や STP の必要はありません。
              <option>passthru</option> モードの場合、物理インターフェースで受け取った全てのフレームは macvlan インターフェースに転送されます。<option>passthru</option> モードの場合、ひとつの macvlan インターフェースだけが、ひとつの物理インターフェースに対して設定できます。
            </para>

            <para>
              <!--
              <option>ipvlan:</option> an ipvlan interface is linked
              with the interface specified by
              the <option>lxc.net.[i].link</option> and assigned to
              the container.
              <option>lxc.net.[i].ipvlan.mode</option> specifies the
              mode the ipvlan will use to communicate between
              different ipvlan on the same upper device. The accepted
              modes are <option>l3</option>, <option>l3s</option> and
              <option>l2</option>. It defaults to <option>l3</option> mode.
              In <option>l3</option> mode TX processing up to L3 happens on the stack instance
              attached to the dependent device and packets are switched to the stack instance of the
              parent device for the L2 processing and routing from that instance will be
              used before packets are queued on the outbound device. In this mode the dependent devices
              will not receive nor can send multicast / broadcast traffic.
              In <option>l3s</option> mode TX processing is very similar to the L3 mode except that
              iptables (conn-tracking) works in this mode and hence it is L3-symmetric (L3s).
              This will have slightly less performance but that shouldn't matter since you are
              choosing this mode over plain-L3 mode to make conn-tracking work.
              In <option>l2</option> mode TX processing happens on the stack instance attached to
              the dependent device and packets are switched and queued to the parent device to send
              out. In this mode the dependent devices will RX/TX multicast and broadcast (if applicable) as well.
              <option>lxc.net.[i].ipvlan.isolation</option> specifies the isolation mode.
              The accepted isolation values are <option>bridge</option>,
              <option>private</option> and <option>vepa</option>.
              It defaults to <option>bridge</option>.
              In <option>bridge</option> isolation mode dependent devices can cross-talk among themselves
              apart from talking through the parent device.
              In <option>private</option> isolation mode the port is set in private mode.
              i.e. port won't allow cross communication between dependent devices.
              In <option>vepa</option> isolation mode the port is set in VEPA mode.
              i.e. port will offload switching functionality to the external entity as
              described in 802.1Qbg.
              -->
              <option>ipvlan:</option> ipvlan インターフェースは <option>lxc.net.[i].link</option> により指定されるインターフェースとリンクし、コンテナに割り当てられます。
              <option>lxc.net.[i].ipvlan.mode</option> でモードを指定すると、その ipvlan の指定を、同じ上位デバイスで異なる ipvlan 間の通信をする時に使います。
              指定できるモードは <option>l3</option>、<option>l3s</option>、<option>l2</option> で、デフォルトは <option>l3</option> モードです。
              <option>l3</option> モードでは、L3 までの TX (送信) 処理はスレーブデバイスにアタッチされたスタックインスタンス上で行われます。
              そしてパケットは、L2 処理のためにマスターデバイスのスタックインスタンスにスイッチされます。このインスタンスからのルーティングは、発信デバイス上でキューに入る前に使われます。
              このモードでは、スレーブはマルチキャスト・ブロードキャストのトラフィックを受信しませんし、送信もできません。
              <option>l3s</option> モードは、TX (送信) 処理は L3 モードと非常に似ていますが、iptables (conn-tracking) がこのモードで動作します。
              それゆえに L3対称 (symmetric) (L3s) です。このモードは若干パフォーマンスが低下しますが、conn-tracking (接続追跡) が動作するように、普通の L3 モードの代わりにこのモードを選んでいるので問題にはならないはずです。
              <option>l2</option> モードでは、TX (送信) 処理はスレーブデバイスにアタッチされたスタックインスンタンス上で行われます。
              パケットは送信のため、マスターデバイスにスイッチされ、マスターデバイス上でキューに入ります。このモードでは、スレーブはマルチキャストも（該当する場合）ブロードキャストも RX/TX (送受信) 処理します。

              <option>lxc.net.[i].ipvlan.isolation</option> は隔離モードを指定します。隔離モードには <option>bridge</option>、<option>private</option>、<option>vepa</option> が指定できます。デフォルトは <option>bridge</option> モードです。
              <option>bridge</option> 隔離モードでは、スレーブはマスターデバイス経由の通信とは別に、スレーブ同士で通信できます。
              <option>private</option> 隔離モードでは、ポートはプライベートに設定されます。つまり、スレーブ間の通信はできません。
              <option>vepa</option> 隔離モードでは、ポートは VEPA モードに設定されます。つまり、802.1Qbg で説明されているように、ポートはスイッチング機能を外部エンティティにオフロードします。
            </para>

            <para>
              <!--
              <option>phys:</option> an already existing interface
              specified by the <option>lxc.net.[i].link</option> is
              assigned to the container.
              -->
              <option>phys:</option> <option>lxc.net.[i].link</option> で指定された、すでに存在しているインターフェースがコンテナに割り当てられます。
            </para>
          </listitem>
          </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].flags</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify an action to do for the network.
              -->
              ネットワークに対して行うアクションを指定します。
            </para>

            <para>
              <!--
              <option>up:</option> activates the interface.
              -->
              <option>up:</option> インターフェースを起動させます。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].link</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the interface to be used for real network traffic.
              -->
              実際のネットワークトラフィックに使うインターフェースを指定します。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].l2proxy</option>
          </term>
          <listitem>
            <para>
              <!--
              Controls whether layer 2 IP neighbour proxy entries will be added to the
              lxc.net.[i].link interface for the IP addresses of the container.
              Can be set to 0 or 1. Defaults to 0.
              When used with IPv4 addresses, the following sysctl values need to be set:
              net.ipv4.conf.[link].forwarding=1
              When used with IPv6 addresses, the following sysctl values need to be set:
              net.ipv6.conf.[link].proxy_ndp=1
              net.ipv6.conf.[link].forwarding=1
                -->
              レイヤ 2 IP 近隣プロキシエントリを、コンテナの IP アドレスに対応する lxc.net.[i].link インターフェースに追加するかどうかを制御します。0 か 1 が設定でき、デフォルトは 0 です。
              IPv4 アドレスで使う場合は、次の sysctl 設定が必要です:
              net.ipv4.conf.[link].forwarding=1
              IPv6 アドレスで使う場合は、次の sysctl 設定が必要です:
              net.ipv6.conf.[link].proxy_ndp=1
              net.ipv6.conf.[link].forwarding=1
              </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].mtu</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the maximum transfer unit for this interface.
              -->
              インターフェースに対する MTU を指定します。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].name</option>
          </term>
          <listitem>
            <para>
              <!--
              The interface name is dynamically allocated, but if another name
              is needed because the configuration files being used by the
              container use a generic name, eg. eth0, this option will rename
              the interface in the container.
              -->
              インターフェース名は動的に割り当てられます。しかし、もしコンテナが使用する設定ファイルが一般的な名前を使用するために、他の特定の名前が必要であれば (例えば eth0 など)、コンテナ内のインターフェースは、このオプションで指定した名前にリネームされます。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].hwaddr</option>
          </term>
          <listitem>
            <para>
              <!--
              The interface mac address is dynamically allocated by default to
              the virtual interface, but in some cases, this is needed to
              resolve a mac address conflict or to always have the same
              link-local ipv6 address.  Any "x" in address will be replaced by
              random value, this allows setting hwaddr templates.
              -->
              仮想インターフェースの MAC アドレスは、デフォルトでは動的に割り当てられます。しかし、MAC アドレスの衝突や、リンクローカルIPv6 アドレスを常に同じにした場合などは、このオプションが必要です。アドレス中の "x" という文字は、ランダムな値に置き換えられます。これによりテンプレートに hwaddr を設定することが可能になります。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].ipv4.address</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the ipv4 address to assign to the virtualized interface.
              Several lines specify several ipv4 addresses. The address is in
              format x.y.z.t/m, eg. *************/24.
              You can optionally specify the broadcast address after the IP adress,
              e.g. *************/24 ***************.
              Otherwise it is automatically calculated from the IP address.
              -->
              仮想インターフェースに割り当てる ipv4 アドレスを指定します。複数行により複数の ipv4 アドレスを指定します。このアドレスは x.y.z.t/m というフォーマットで指定します。例) *************/24
              IP アドレスのあとにオプションでブロードキャストアドレスを指定できます。例）*************/24 ***************
              指定しなければ IP アドレスから自動的に計算されます。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].ipv4.gateway</option>
          </term>
          <listitem>
            <para>
              <!--
              specify the ipv4 address to use as the gateway inside the
              container. The address is in format x.y.z.t, eg. *************.

              Can also have the special value <option>auto</option>,
              which means to take the primary address from the bridge
              interface (as specified by the
              <option>lxc.net.[i].link</option> option) and use that as
              the gateway. <option>auto</option> is only available when
              using the <option>veth</option>,
              <option>macvlan</option> and <option>ipvlan</option> network types.
              Can also have the special value of <option>dev</option>,
              which means to set the default gateway as a device route.
              This is primarily for use with layer 3 network modes, such as IPVLAN.
              -->
              コンテナでゲートウェイとして使う IPv4 アドレスを指定します。アドレスは x.y.z.t というフォーマットです。例) *************
              <option>auto</option> という特別な値を指定できます。これは (<option>lxc.net.[i].link</option> で指定した) ブリッジインターフェースの最初のアドレスを使用し、それをゲートウェイに使うという意味になります。<option>auto</option> はネットワークタイプとして <option>veth</option>、<option>macvlan</option>、<option>ipvlan</option> を指定している時だけ有効となります。
              特別な値である <option>dev</option> も設定できます。これはデバイスのルートとしてデフォルトゲートウェイを設定するという意味です。これは主に、IPVLAN のようなレイヤ 3 のネットワークモードで使用します。
            </para>
          </listitem>
        </varlistentry>


        <varlistentry>
          <term>
            <option>lxc.net.[i].ipv6.address</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the ipv6 address to assign to the virtualized
              interface. Several lines specify several ipv6 addresses. The
              address is in format x::y/m, eg.
              2003:db8:1:0:214:1234:fe0b:3596/64
              -->
              仮想インターフェースに割り当てる ipv6 アドレスを指定します。複数行により複数の ipv6 アドレスを指定します。このアドレスは x::y/m というフォーマットで指定します。例) 2003:db8:1:0:214:1234:fe0b:3596/64
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].ipv6.gateway</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the ipv6 address to use as the gateway inside the
              container. The address is in format x::y, eg. 2003:db8:1:0::1

              Can also have the special value <option>auto</option>,
              which means to take the primary address from the bridge
              interface (as specified by the
              <option>lxc.net.[i].link</option> option) and use that as
              the gateway. <option>auto</option> is only available when
              using the <option>veth</option>,
              <option>macvlan</option> and <option>ipvlan</option> network types.
              Can also have the special value of <option>dev</option>,
              which means to set the default gateway as a device route.
              This is primarily for use with layer 3 network modes, such as IPVLAN.
              -->
              コンテナでゲートウェイとして使う IPv6 アドレスを指定します。アドレスは x::y というフォーマットです。例) 2003:db8:1:0::1
              <option>auto</option> という特別な値を記述する事も可能です。これは (<option>lxc.net.[i].link</option> で指定した) ブリッジインターフェースの最初のアドレスを使用し、それをゲートウェイに使うという意味になります。<option>auto</option> はネットワークタイプとして <option>veth</option>、<option>macvlan</option>、<option>ipvlan</option> を指定している時だけ有効となります。
              特別な値である <option>dev</option> も設定できます。これはデバイスのルートとしてデフォルトゲートウェイを設定するという意味です。これは主に、IPVLAN のようなレイヤ 3 のネットワークモードで使用します。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].script.up</option>
          </term>
          <listitem>
            <para>
              <!--
              Add a configuration option to specify a script to be
              executed after creating and configuring the network used
              from the host side.
                -->
              ホスト側から使われる、ネットワークの作成と設定が済んだ後に実行するスクリプトを指定します。
            </para>

            <para>
              <!--
              In addition to the information available to all hooks. The
              following information is provided to the script:
                -->
              すべてのフックで追加の情報が使えます。以下の情報がスクリプトに提供されます:
              <itemizedlist>
                <listitem>
                 <para>
                 LXC_HOOK_TYPE: フックタイプ。'up' か 'down' のいずれかです <!-- the hook type. This is either 'up' or 'down'. -->
                  </para>
                </listitem>

                <listitem>
                 <para>
                 LXC_HOOK_SECTION: セクションタイプとして 'net' が設定されます<!-- the section type 'net'. -->
                  </para>
                </listitem>

                <listitem>
                 <para>
                 LXC_NET_TYPE: ネットワークタイプ。有効なネットワークタイプのうちのひとつです (例: 'vlan', 'macvlan', 'ipvlan', 'veth') <!-- the network type. This is one of the valid
                 network types listed here (e.g. 'vlan', 'macvlan', 'ipvlan', 'veth'). -->
                  </para>
                </listitem>

                <listitem>
                 <para>
                 LXC_NET_PARENT: ホスト上の親デバイス名。これはネットワークタイプが 'macvlan'、'veth'、'phys' のどれかのときだけ設定されます <!-- the parent device on the host. This is only
                 set for network types 'mavclan', 'veth', 'phys'. -->
                  </para>
                </listitem>

                <listitem>
                 <para>
                   LXC_NET_PEER: ホスト上のピアデバイス名。これはネットワークタイプが 'veth' の場合のみ設定されます。この情報は <option>lxc.hook.version</option> が 1 に設定されている場合のみ設定されます
                   <!-- the name of the peer device on the host. This is
                 only set for 'veth' network types. Note that this information
                 is only available when <option>lxc.hook.version</option> is set
                 to 1. -->
                  </para>
                </listitem>
              </itemizedlist>

              <!--
              Whether this information is provided in the form of environment
              variables or as arguments to the script depends on the value of
              <option>lxc.hook.version</option>. If set to 1 then information is
              provided in the form of environment variables. If set to 0
              information is provided as arguments to the script.
              -->
              この情報が環境変数の形で提供されるか、スクリプトへの引数の形で提供されるかは <option>lxc.hook.version</option> の値によって決まります。もし <option>lxc.hook.version</option> が 1 に設定されている場合は、環境変数の形で提供されます。もし 0 が設定されている場合は、スクリプトへの引数として提供されます。
            </para>

            <para>
              <!--
              Standard output from the script is logged at debug level.
              Standard error is not logged, but can be captured by the
              hook redirecting its standard error to standard output.
              -->
              スクリプトからの標準出力は debug レベルでロギングされます。標準エラー出力はロギングされません。しかし、フックの標準エラー出力を標準出力にリダイレクトすることにより保存することは可能です。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.net.[i].script.down</option>
          </term>
          <listitem>
            <para>
              <!--
              Add a configuration option to specify a script to be
              executed before destroying the network used from the
              host side.
                -->
              ホスト側から使われる、ネットワークを破壊する前に実行するスクリプトを指定します。
            </para>

            <para>
              <!--
              In addition to the information available to all hooks. The
              following information is provided to the script:
                -->
              すべてのフックで追加の情報が使えます。以下の情報がスクリプトに提供されます:
              <itemizedlist>
                <listitem>
                 <para>
                 LXC_HOOK_TYPE: フックタイプ。'up' か 'down' のいずれかです <!-- the hook type. This is either 'up' or 'down'. -->
                  </para>
                </listitem>

                <listitem>
                 <para>
                 LXC_HOOK_SECTION: セクションタイプとして 'net' が設定されます<!-- the section type 'net'. -->
                  </para>
                </listitem>

                <listitem>
                  <para>
                 LXC_NET_TYPE: ネットワークタイプ。有効なネットワークタイプのうちのひとつです (例: 'vlan', 'macvlan', 'ipvlan', 'veth') <!-- the network type. This is one of the valid
                 network types listed here (e.g. 'vlan', 'macvlan', 'ipvlan', 'veth'). -->
                  </para>
                </listitem>

                <listitem>
                 <para>
                 LXC_NET_PARENT: ホスト上の親デバイス名。これはネットワークタイプが 'macvlan'、'veth'、'phys' のどれかのときだけ設定されます <!-- the parent device on the host. This is only
                 set for network types 'mavclan', 'veth', 'phys'. -->
                  </para>
                </listitem>

                <listitem>
                 <para>
                   LXC_NET_PEER: ホスト上のピアデバイス名。これはネットワークタイプが 'veth' の場合のみ設定されます。この情報は <option>lxc.hook.version</option> が 1 に設定されている場合のみ設定されます
                   <!-- the name of the peer device on the host. This is
                 only set for 'veth' network types. Note that this information
                 is only available when <option>lxc.hook.version</option> is set
                 to 1. -->
                  </para>
                </listitem>
              </itemizedlist>

              <!--
              Whether this information is provided in the form of environment
              variables or as arguments to the script depends on the value of
              <option>lxc.hook.version</option>. If set to 1 then information is
              provided in the form of environment variables. If set to 0
              information is provided as arguments to the script.
              -->
              この情報が環境変数の形で提供されるか、スクリプトへの引数の形で提供されるかは <option>lxc.hook.version</option> の値によって決まります。もし <option>lxc.hook.version</option> が 1 に設定されている場合は、環境変数の形で提供されます。もし 0 が設定されている場合は、スクリプトへの引数として提供されます。
            </para>

            <para>
              <!--
              Standard output from the script is logged at debug level.
              Standard error is not logged, but can be captured by the
              hook redirecting its standard error to standard output.
              -->
              スクリプトからの標準出力は debug レベルでロギングされます。標準エラー出力はロギングされません。しかし、フックの標準エラー出力を標準出力にリダイレクトすることにより保存することは可能です。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>

    </refsect2>

    <refsect2>
      <title><!-- New pseudo tty instance (devpts) -->新しい擬似端末のインスタンス (devpts)</title>
      <para>
        <!--
        For stricter isolation the container can have its own private
        instance of the pseudo tty.
        -->
        さらに厳しい隔離のために、コンテナは自身のプライベートな pseudo tty (擬似端末) を持つことが可能です。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.pty.max</option>
          </term>
          <listitem>
            <para>
              <!--
              If set, the container will have a new pseudo tty
              instance, making this private to it. The value specifies
              the maximum number of pseudo ttys allowed for a pty
              instance (this limitation is not implemented yet).
              -->
              もし設定された場合、コンテナは新しい pseudo tty インスタンスを持ち、それを自身のプライベートとします。
              この値は pty インスタンスに許可される pseudo tty の最大数を指定します (この制限はまだ実装されていません)。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Container system console -->コンテナのシステムコンソール</title>
      <para>
        <!--
        If the container is configured with a root filesystem and the
        inittab file is setup to use the console, you may want to specify
        where the output of this console goes.
        -->
        コンテナでルートファイルシステムを持つように設定されており、inittab ファイルでコンソールの使用が設定されている場合、このコンソールの出力がどこになされるのかを指定したいと思うでしょう。
      </para>
      <variablelist>

        <varlistentry>
          <term>
            <option>lxc.console.buffer.size</option>
          </term>
          <listitem>
            <para>
              <!--
            Setting this option instructs liblxc to allocate an in-memory
            ringbuffer. The container's console output will be written to the
            ringbuffer. Note that ringbuffer must be at least as big as a
            standard page size. When passed a value smaller than a single page
            size liblxc will allocate a ringbuffer of a single page size. A page
            size is usually 4KB.
                -->
              このオプションを設定すると、liblxc はインメモリのリングバッファを割り当てます。コンテナのコンソールはリングバッファに出力されます。リングバッファは少なくとも標準ページサイズの大きさでなければなりません。ページサイズより小さい値を与えた場合は、liblxc  はページサイズのリングバッファを割り当てます。ページサイズは通常は 4KB です。

              <!--
            The keyword 'auto' will cause liblxc to allocate a ringbuffer of
            128KB.
                -->
              'auto' を指定すると、liblxc は 128KB のリングバッファを割り当てます。

              <!--
            When manually specifying a size for the ringbuffer the value should
            be a power of 2 when converted to bytes. Valid size prefixes are
            'KB', 'MB', 'GB'. (Note that all conversions are based on multiples
            of 1024. That means 'Kb' == 'KiB', 'MB' == 'MiB', 'GB' == 'GiB'.
            Additionally, the case of the suffix is ignored, i.e. 'kB', 'KB' and
            'Kb' are treated equally.)
                -->
              リングバッファサイズを数値指定する場合、値がバイトに変換されるときに 2 の累乗になります。サイズ接頭辞付きの単位として 'KB'、'MB'、'GB' が使えます。(この場合の変換は 1024 の倍数に基づいています。つまり 'KB' == 'KiB'、'MB' == 'MiB'、'GB' == 'GiB' という意味です。加えて、単位の大文字小文字は無視されます。すなわち 'kB'、'KB'、'Kb' は同一に扱われます。)
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.console.size</option>
          </term>
          <listitem>
            <para>
              <!--
            Setting this option instructs liblxc to place a limit on the size of
            the console log file specified in
            <option>lxc.console.logfile</option>. Note that size of the log file
            must be at least as big as a standard page size. When passed a value
            smaller than a single page size liblxc will set the size of log file
            to a single page size. A page size is usually 4KB.
            -->
              liblxc は <option>lxc.console.logfile</option> で指定したコンソールログのサイズを、このオプションで設定した値に制限します。ログファイルのサイズは少なくとも標準ページサイズでなければなりません。ページサイズ以下の値を設定した場合は、liblxc はログファイルのサイズをページサイズに設定します。ページサイズは通常は 4KB です。

              <!--
            The keyword 'auto' will cause liblxc to place a limit of 128KB on
            the log file.
                -->
              'auto' を指定すると、liblxc はログファイルのサイズを 128KB に制限します。

              <!--
            When manually specifying a size for the log file the value should
            be a power of 2 when converted to bytes. Valid size prefixes are
            'KB', 'MB', 'GB'. (Note that all conversions are based on multiples
            of 1024. That means 'KB' == 'KiB', 'MB' == 'MiB', 'GB' == 'GiB'.
            Additionally, the case of the suffix is ignored, i.e. 'kB', 'KB' and
            'Kb' are treated equally.)
                -->
              ログファイルサイズの値を数値指定する場合、値がバイトに変換されるときに 2 の累乗になります。サイズ接頭辞付きの単位として 'kB'、'MB'、'GB' が使えます。(この場合の変換は 1024 の倍数に基づいています。つまり 'kB' == 'KiB'、'MB' == 'MiB'、'GB' == 'GiB' という意味です。加えて、単位の大文字小文字は無視されます。すなわち 'kB'、'KB'、'Kb' は同一に扱われます。)

              <!--
            If users want to mirror the console ringbuffer on disk they should set
            <option>lxc.console.size</option> equal to
            <option>lxc.console.buffer.size</option>.
            -->
              ディスク上のコンソールリングバッファとミラーになるようにしたい場合は、<option>lxc.console.size</option> と <option>lxc.console.buffer.size</option> の値を同じ値に設定します。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.console.logfile</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify a path to a file where the console output will be written.
              Note that in contrast to the on-disk ringbuffer logfile this file
              will keep growing potentially filling up the users disks if not
              rotated and deleted. This problem can also be avoided by using the
              in-memory ringbuffer options
              <option>lxc.console.buffer.size</option> and
              <option>lxc.console.buffer.logfile</option>.
              -->
              コンソール出力を書き込むファイルのパスを指定します。ディスクに保存されるリングバッファログと異なり、このファイルはサイズが大きくなり続けるので、ファイルがローテートや削除されない限りは、ユーザのディスクをいっぱいにしてしまう可能性があります。この問題は、インメモリのリングバッファオプションである、<option>lxc.console.buffer.size</option> と <option>lxc.console.buffer.logfile</option> を使うことでも回避できます。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.console.rotate</option>
          </term>
          <listitem>
            <para>
              <!--
              Whether to rotate the console logfile specified in
              <option>lxc.console.logfile</option>. Users can send an API
              request to rotate the logfile. Note that the old logfile will have
              the same name as the original with the suffix ".1" appended.
              -->
              <option>lxc.console.logfile</option> で指定したコンソールログファイルをローテートするかどうかを指定します。ユーザはログファイルをローテートするように API リクエストを送ることができます。古いログファイルは、元のファイル名と同じ名前のファイルに ".1" というサフィックスが付け加わります。

              <!--
              Users wishing to prevent the console log file from filling the
              disk should rotate the logfile and delete it if unneeded. This
              problem can also be avoided by using the in-memory ringbuffer
              options <option>lxc.console.buffer.size</option> and
              <option>lxc.console.buffer.logfile</option>.
              -->
              ユーザがコンソールログでディスクがいっぱいになるのを防ぐには、ログファイルをローテートし、不要なログファイルを削除してください。この問題はインメモリのリングバッファオプションである <option>lxc.console.buffer.size</option> と <option>lxc.console.buffer.logfile</option> を使うことでも防げます。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.console.path</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify a path to a device to which the console will be
              attached. The keyword 'none' will simply disable the
              console. Note, when specifying 'none' and creating a device node
              for the console in the container at /dev/console or bind-mounting
              the hosts's /dev/console into the container at /dev/console the
              container will have direct access to the hosts's /dev/console.
              This is dangerous when the container has write access to the
              device and should thus be used with caution.
                -->
              コンソールを割り当てるデバイスのパスを指定します。'none' というキーワードは、単純にコンソールを無効にします。
              'none' を指定し、コンテナ内のコンソールに対するデバイスノードを /dev/console に作成するか、もしくはホストの /dev/console をコンテナ内の /dev/console に bind mount する場合、そのコンテナはホストの /dev/console へ直接アクセスを行うことに注意が必要です。
              そのコンテナがデバイスへの書き込み権を持っている場合は危険ですので、注意してこの指定を使用する必要があります。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Console through the ttys -->tty を通したコンソール</title>
      <para>
        <!--
        This option is useful if the container is configured with a root
        filesystem and the inittab file is setup to launch a getty on the
        ttys. The option specifies the number of ttys to be available for
        the container. The number of gettys in the inittab file of the
        container should not be greater than the number of ttys         specified
        in this option, otherwise the excess getty sessions will die and
        respawn indefinitely giving annoying messages on the console or in
        <filename>/var/log/messages</filename>.
        -->
        このオプションはコンテナが root ファイルシステムを持つように設定されており、inittab ファイルで tty 上に getty の起動が設定されている場合に役に立ちます。
        このオプションはコンテナで利用できる tty の数を指定します。
        inittab ファイルに設定する getty の数は、このオプションの指定する tty の数より大きくしてはいけません。
        さもなければ、超過した分の getty セッションはコンソールか /var/log/messages にうっとうしいメッセージを生死を表示しながら、永久に生死を繰り返すでしょう。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.tty.max</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the number of tty to make available to the
              container.
              -->
              コンテナに作成出来る tty の数を指定します。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Console devices location -->コンソールデバイスの位置</title>
      <para>
        <!--
        LXC consoles are provided through Unix98 PTYs created on the
        host and bind-mounted over the expected devices in the container.
        By default, they are bind-mounted over <filename>/dev/console</filename>
        and <filename>/dev/ttyN</filename>.  This can prevent package upgrades
        in the guest.  Therefore you can specify a directory location (under
        <filename>/dev</filename> under which LXC will create the files and
        bind-mount over them.  These will then be symbolically linked to
        <filename>/dev/console</filename> and <filename>/dev/ttyN</filename>.
        A package upgrade can then succeed as it is able to remove and replace
        the symbolic links.
        -->
        LXC のコンソールはホストによって作られ、コンテナ内で要求されたデバイスに bind マウントされた Unix98 PTY 経由で提供されます。
        デフォルトでは <filename>/dev/console</filename> と <filename>/dev/ttyN</filename> に bind マウントされます。
        これはゲスト内でのパッケージのアップグレードを妨げる可能性があります。
        なので <filename>/dev</filename> 以下のディレクトリを指定することができます。
        LXC はこのディレクトリ以下にファイルを作成し、これらのファイルを bind マウントします。
        そして、これらの (作成された) ファイルは <filename>/dev/console</filename> と <filename>/dev/ttyN</filename> にシンボリックリンクされます。
        シンボリックリンクを消去したり置き換えたりすることは可能ですから、パッケージのアップグレードは成功します。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.tty.dir</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify a directory under <filename>/dev</filename>
              under which to create the container console devices. Note that LXC
              will move any bind-mounts or device nodes for /dev/console into
              this directory.
                -->
              コンテナのコンソールデバイスを作成するための <filename>/dev</filename> 以下のディレクトリを指定します。
              LXC は /dev/console に対する bind mount や /dev/console デバイスノードをこのディレクトリ以下に移動することに注意が必要です。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- /dev directory -->/dev ディレクトリ</title>
      <para>
        <!--
        By default, lxc creates a few symbolic links (fd,stdin,stdout,stderr)
        in the container's <filename>/dev</filename> directory but does not
        automatically create device node entries. This allows the container's
        <filename>/dev</filename> to be set up as needed in the container
        rootfs.  If lxc.autodev is set to 1, then after mounting the container's
        rootfs LXC will mount a fresh tmpfs under <filename>/dev</filename>
        (limited to 500K by default, unless defined in lxc.autodev.tmpfs.size)
        and fill in a minimal set of initial devices.
        This is generally required when starting a container containing
        a "systemd" based "init" but may be optional at other times.  Additional
        devices in the containers /dev directory may be created through the
        use of the <option>lxc.hook.autodev</option> hook.
        -->
        デフォルトでは、lxc はコンテナの <filename>/dev</filename> 以下に fd, stdin, stdout, stderr のシンボリックリンクを作成しますが、自動的にはデバイスノードのエントリは作成しません。
        これは、コンテナの rootfs で必要な設定を行えるようにするものです。
        lxc.autodev が 1 に設定されている場合、コンテナの rootfs をマウントした後、LXC は新しい tmpfs を <filename>/dev</filename> 以下にマウントします (デフォルトでは 500k 制限でマウント、lxc.autodev.tmpfs.size で指定可能)。
        そして初期デバイスの最小限のセットを作成します。
        これは、"systemd" ベースの "init" 環境のコンテナを起動する時に通常必要ですが、他の環境の場合はオプショナルなものです。
        コンテナの /dev ディレクトリ内の追加デバイスは <option>lxc.hook.autodev</option> フックを使用して作成されます。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.autodev</option>
          </term>
          <listitem>
            <para>
              <!--
              Set this to 0 to stop LXC from mounting and populating a minimal
              <filename>/dev</filename> when starting the container.
              -->
              コンテナの起動時に LXC が /dev をマウントして最小限の /dev を作成するのを止めるには、この値を 0 に設定してください。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.autodev.tmpfs.size</option>
          </term>
          <listitem>
            <para>
	      <!--
              Set this to define the size of the /dev tmpfs.
              The default value is 500000 (500K). If the parameter is used
              but without value, the default value is used.
		-->
	      この設定で /dev tmpfs のサイズを指定します。
	      デフォルト値は 500000 (500K) です。このパラメータを値なしで使うと、デフォルト値が使われます。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Mount points -->マウントポイント</title>
      <para>
        <!--
        The mount points section specifies the different places to be
        mounted. These mount points will be private to the container
        and won't be visible by the processes running outside of the
        container. This is useful to mount /etc, /var or /home for
        examples.
        -->
        マウントポイントセクションは、マウントするための区別された場所を指定します。
        これらのマウントポイントは、コンテナだけに見え、コンテナ外で実行されるプロセスから見えることはありません。
        例えば、/etc や /var や /home をマウントするときに役に立つでしょう。
      </para>
      <para>
        <!--
        NOTE - LXC will generally ensure that mount targets and relative
        bind-mount sources are properly confined under the container
        root, to avoid attacks involving over-mounting host directories
        and files.  (Symbolic links in absolute mount sources are ignored)
        However, if the container configuration first mounts a directory which
        is under the control of the container user, such as /home/<USER>
        the container at some <filename>path</filename>, and then mounts
        under <filename>path</filename>, then a TOCTTOU attack would be
        possible where the container user modifies a symbolic link under
        their home directory at just the right time.
          -->
        注意: 通常 LXC は、マウント対象と相対パス指定のバインドマウントを、適切にコンテナルート以下に閉じ込めます。
        これは、ホストのディレクトリやファイルに対して重ね合わせを行うようなマウントによる攻撃を防ぎます。(絶対パス指定のマウントソース中の各パスがシンボリックリンクである場合は無視されます。)
        しかし、もしコンテナの設定が最初に、/home/<USER>/filename> にマウントし、その後 <filename>path</filename> 以下でマウントが行われるような場合、コンテナユーザがタイミングを見計らって自身のホームディレクトリ以下でシンボリックリンクを操作するような TOCTTOU 攻撃が成立する可能性があります。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.mount.fstab</option>
          </term>
          <listitem>
            <para>
              <!--
              specify a file location in
              the <filename>fstab</filename> format, containing the
              mount information.  The mount target location can and in
              most cases should be a relative path, which will become
              relative to the mounted container root.  For instance,
              -->
              マウント情報の書かれた <filename>fstab</filename> フォーマットで書かれたファイルの場所を指定します。
              マウントする場所は相対バスで書くことができます。そして、ほとんどの場合にコンテナの root からの相対パスとなるはずです。例えば、以下のように書きます。
             </para>
             <programlisting>
             proc proc proc nodev,noexec,nosuid 0 0
             </programlisting>
             <para>
               <!--
              Will mount a proc filesystem under the container's /proc,
              regardless of where the root filesystem comes from.  This
              is resilient to block device backed filesystems as well as
              container cloning.
              -->
               この例は、root ファイルシステムがどこにあっても、コンテナの /proc 以下に proc ファイルシステムをマウントします。これは、ブロックデバイスがバックエンドのファイルシステムだけでなく、コンテナのクローンにも柔軟に対応できます。
             </para>
             <para>
              <!--
              Note that when mounting a filesystem from an
              image file or block device the third field (fs_vfstype)
              cannot be auto as with
              <citerefentry>
                <refentrytitle>mount</refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              but must be explicitly specified.
              -->
              ファイルシステムがイメージファイルやブロックデバイスからマウントされている場合、3 つ目のフィールド (fs_vfstype) は
              <citerefentry>
                <refentrytitle>mount</refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              のように auto を指定することはできず、明確に指定しなければいけません。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.mount.entry</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify a mount point corresponding to a line in the
              fstab format.
              -->
              fstab フォーマットの一行と同じフォーマットのマウントポイントの指定をします。

              <!--
              Moreover lxc supports mount propagation, such as rshared or
              rprivate, and adds three additional mount options.
              <option>optional</option> don't fail if mount does not work.
              <option>create=dir</option> or <option>create=file</option>
              to create dir (or file) when the point will be mounted.
              <option>relative</option> source path is taken to be relative to
              the mounted container root. For instance,
              -->
              加えて、LXC では rshared や rprivate といったマウント・プロパゲーションオプションと、独自の 3 つのマウントオプションが使えます。
              <option>optional</option> は、マウントが失敗しても失敗を返さずに無視します。
              <option>create=dir</option> と <option>create=file</option> は、マウントポイントをマウントする際にディレクトリもしくはファイルを作成します。
              <option>relative</option> を指定すると、マウントされたコンテナルートからの相対パスとして取得されます。
            </para>
            <programlisting>
              dev/null proc/kcore none bind,relative 0 0
            </programlisting>
            <para>
              <!--
              Will expand dev/null to ${<option>LXC_ROOTFS_MOUNT</option>}/dev/null,
              and mount it to proc/kcore inside the container.
                -->
              は dev/null を ${<option>LXC_ROOTFS_MOUNT</option>}/dev/null と展開し、コンテナ内の proc/kcore にマウントします。
             </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.mount.auto</option>
          </term>
          <listitem>
            <para>
              <!--
              specify which standard kernel file systems should be
              automatically mounted. This may dramatically simplify
              the configuration. The file systems are:
              -->
              標準のカーネルファイルシステムで自動的にマウントするものを指定します。
              これは劇的に設定を容易にする可能性があります。
            </para>
            <itemizedlist>
              <listitem>
                <para>
                  <option>proc:mixed</option> (or <option>proc</option>):
                <!--
                  mount <filename>/proc</filename> as read-write, but
                  remount <filename>/proc/sys</filename> and
                  <filename>/proc/sysrq-trigger</filename> read-only
                  for security / container isolation purposes.
                -->
                  <filename>/proc</filename> を読み書き可能でマウントします。
                  ただし、<filename>/proc/sys</filename> と <filename>/proc/sysrq-trigger</filename> は、セキュリティとコンテナの隔離の目的でリードオンリーで再マウントされます。
                </para>
              </listitem>
              <listitem>
                <para>
                  <option>proc:rw</option>:
                <!--
                  <filename>/proc</filename> as read-write
                -->
                  <filename>/proc</filename> を読み書き可能でマウントします。
                </para>
              </listitem>
              <listitem>
                <para>
                  <option>sys:mixed</option> (or <option>sys</option>):
                <!--
                  mount <filename>/sys</filename> as read-only but with
                  /sys/devices/virtual/net writable.
                -->
                  /sys/devices/virtual/net のみ書き込み可能で、その他の <filename>/sys</filename> はリードオンリーでマウントします。
                </para>
              </listitem>
              <listitem>
                <para>
                  <option>sys:ro</option>:
                <!--
                  mount <filename>/sys</filename> as read-only
                  for security / container isolation purposes.
                -->
                  <filename>/sys</filename> を、セキュリティとコンテナの隔離の目的でリードオンリーでマウントします。
                </para>
              </listitem>
              <listitem>
                <para>
                  <option>sys:rw</option>:
                <!--
                  <filename>/sys</filename> as read-write
                -->
                  <filename>/sys</filename> を読み書き可能でマウントします。
                </para>
              </listitem>
              <listitem>
                <para>
                  <option>cgroup:mixed</option>:
                  <!--
                  Mount a tmpfs to <filename>/sys/fs/cgroup</filename>,
                  create directories for all hierarchies to which the container
                  is added, create subdirectories in those hierarchies with the
                  name of the cgroup, and bind-mount the container's own cgroup
                  into that directory. The container will be able to write to
                  its own cgroup directory, but not the parents, since they will
                  be remounted read-only.
                    -->
                  <filename>/sys/fs/cgroup</filename> を tmpfs でマウントし、そのコンテナの追加が行われた全ての階層に対するディレクトリを作成し、それらの階層内に cgroup 名でサブディレクトリを作成し、そのコンテナ自身の cgroup をそのディレクトリにバインドマウントします。コンテナは自身の cgroup ディレクトリに書き込みが可能ですが、親ディレクトリはリードオンリーで再マウントされているため書き込めません。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup:mixed:force</option>:
                  <!--
                  The <option>force</option> option will cause LXC to perform
                  the cgroup mounts for the container under all circumstances.
                  Otherwise it is similar to <option>cgroup:mixed</option>.
                  This is mainly useful when the cgroup namespaces are enabled
                  where LXC will normally leave mounting cgroups to the init
                  binary of the container since it is perfectly safe to do so.
                    -->
                  <option>force</option> を指定すると、LXC はあらゆる状況でコンテナのための cgroup マウントを実行します。それ以外は <option>cgroup:mixed</option> と同様です。これは主に cgroup 名前空間が有効な場合に便利です。この場合は完全に安全ですので、LXC は通常コンテナの init バイナリが cgroup をマウントしたままの状態にしておきます。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup:ro</option>:
                <!--
                  <option>cgroup:mixed</option>, but everything will
                be mounted read-only.
                -->
                  <option>cgroup:mixed</option> と同様にマウントされますが、全てリードオンリーでマウントされます。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup:ro:force</option>:
                  <!--
                  The <option>force</option> option will cause LXC to perform
                  the cgroup mounts for the container under all circumstances.
                  Otherwise it is similar to <option>cgroup:ro</option>.
                  This is mainly useful when the cgroup namespaces are enabled
                  where LXC will normally leave mounting cgroups to the init
                  binary of the container since it is perfectly safe to do so.
                    -->
                  <option>force</option> を指定すると、LXC はあらゆる状況でコンテナのための cgroup マウントを実行します。それ以外は <option>cgroup:ro</option> と同様です。これは主に cgroup 名前空間が有効な場合に便利です。この場合は完全に安全ですので、LXC は通常コンテナの init バイナリが cgroup をマウントしたままの状態にしておきます。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup:rw</option>:
                <!--
                  <option>cgroup:mixed</option>, but everything will be mounted
                  read-write. Note that the paths leading up to the container's
                  own cgroup will be writable, but will not be a cgroup
                  filesystem but just part of the tmpfs of
                  <filename>/sys/fs/cgroup</filename>
                -->
                  <option>cgroup:mixed</option> と同様にマウントされますが、全て読み書き可能でマウントされます。
                  コンテナ自身の cgroup に至るまでのパスも書き込み可能になることに注意が必要ですが、cgroup ファイルシステムにはならず、
                  <filename>/sys/fs/cgroup</filename> の tmpfs の一部分になるでしょう。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup:rw:force</option>:
                  <!--
                  The <option>force</option> option will cause LXC to perform
                  the cgroup mounts for the container under all circumstances.
                  Otherwise it is similar to <option>cgroup:rw</option>.
                  This is mainly useful when the cgroup namespaces are enabled
                  where LXC will normally leave mounting cgroups to the init
                  binary of the container since it is perfectly safe to do so.
                    -->
                  <option>force</option> を指定すると、LXC はあらゆる状況でコンテナのための cgroup マウントを実行します。それ以外は <option>cgroup:rw</option> と同様です。これは主に cgroup 名前空間が有効な場合に便利です。この場合は完全に安全ですので、LXC は通常コンテナの init バイナリが cgroup をマウントしたままの状態にしておきます。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup</option> (マウントオプションなしの場合):
                  <!--
                  defaults to <option>cgroup:rw</option> if the
                  container retains the CAP_SYS_ADMIN capability,
                  <option>cgroup:mixed</option> otherwise.
                  -->
                  コンテナが CAP_SYS_ADMIN ケーパビリティを保持している場合、<option>cgroup:rw</option> となります。保持していない場合、<option>cgroup:mixed</option> となります。
                </para>
              </listitem>
	      
              <listitem>
                <para>
                  <option>cgroup-full:mixed</option>:
                <!--
                  mount a tmpfs to <filename>/sys/fs/cgroup</filename>,
                  create directories for all hierarchies to which
                  the container is added, bind-mount the hierarchies
                  from the host to the container and make everything
                  read-only except the container's own cgroup. Note
                  that compared to <option>cgroup</option>, where
                  all paths leading up to the container's own cgroup
                  are just simple directories in the underlying
                  tmpfs, here
                  <filename>/sys/fs/cgroup/$hierarchy</filename>
                  will contain the host's full cgroup hierarchy,
                  albeit read-only outside the container's own cgroup.
                  This may leak quite a bit of information into the
                  container.
                -->
                  <filename>/sys/fs/cgroup</filename> を tmpfs でマウントし、そのコンテナの追加が行われた全ての階層構造に対するディレクトリを作製し、ホストからコンテナまでの階層構造を全てバインドマウントし、コンテナ自身の cgroup を除いてリードオンリーにします。
                  <option>cgroup</option> と比べると、コンテナ自身の cgroup に至るまでの全てのパスが tmpfs の下層のシンプルなディレクトリとなり、コンテナ自身の cgroup の外ではリードオンリーになりますが、<filename>/sys/fs/cgroup/$hierarchy</filename> はホストの全ての cgroup 階層構造を含みます。
                  これにより、コンテナにはかなりの情報が漏洩します。
                </para>
              </listitem>

	      <listitem>
                <para>
                  <option>cgroup-full:mixed:force</option>:
		  <!--
                  The <option>force</option> option will cause LXC to perform
                  the cgroup mounts for the container under all circumstances.
                  Otherwise it is similar to <option>cgroup-full:mixed</option>.
                  This is mainly useful when the cgroup namespaces are enabled
                  where LXC will normally leave mounting cgroups to the init
                  binary of the container since it is perfectly safe to do so.
		  -->
                  <option>force</option> を指定すると、LXC はあらゆる状況でコンテナのための cgroup マウントを実行します。それ以外は <option>cgroup-full:mixed</option> と同様です。これは主に cgroup 名前空間が有効な場合に便利です。この場合は完全に安全ですので、LXC は通常コンテナの init バイナリが cgroup をマウントしたままの状態にしておきます。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup-full:ro</option>:
                <!--
                  similar to
                  <option>cgroup-full:mixed</option>, but everything
                  will be mounted read-only.
                -->
                  <option>cgroup-full:mixed</option> と同様にマウントされますが、全てリードオンリーでマウントされます。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup-full:ro:force</option>:
		  <!--
                  The <option>force</option> option will cause LXC to perform
                  the cgroup mounts for the container under all circumstances.
                  Otherwise it is similar to <option>cgroup-full:ro</option>.
                  This is mainly useful when the cgroup namespaces are enabled
                  where LXC will normally leave mounting cgroups to the init
                  binary of the container since it is perfectly safe to do so.
		    -->
                  <option>force</option> を指定すると、LXC はあらゆる状況でコンテナのための cgroup マウントを実行します。それ以外は <option>cgroup-full:ro</option> と同様です。これは主に cgroup 名前空間が有効な場合に便利です。この場合は完全に安全ですので、LXC は通常コンテナの init バイナリが cgroup をマウントしたままの状態にしておきます。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup-full:rw</option>:
                <!--
                  similar to
                  <option>cgroup-full:mixed</option>, but everything
                  will be mounted read-write. Note that in this case,
                  the container may escape its own cgroup. (Note also
                  that if the container has CAP_SYS_ADMIN support
                  and can mount the cgroup filesystem itself, it may
                  do so anyway.)
                -->
                  <option>cgroup-full:mixed</option>と同様にマウントされますが、全て読み書き可能でマウントされます。
                  この場合、コンテナは自身の cgroup から脱出する可能性があることに注意してください (コンテナが CAP_SYS_ADMIN を持ち、自身で cgroup ファイルシステムをマウント可能なら、いずれにせよそのようにするかもしれないことにも注意してください)。
                </para>
              </listitem>

              <listitem>
                <para>
                  <option>cgroup-full:rw:force</option>:
		  <!--
                  The <option>force</option> option will cause LXC to perform
                  the cgroup mounts for the container under all circumstances.
                  Otherwise it is similar to <option>cgroup-full:rw</option>.
                  This is mainly useful when the cgroup namespaces are enabled
                  where LXC will normally leave mounting cgroups to the init
                  binary of the container since it is perfectly safe to do so.
		    -->
                  <option>force</option> を指定すると、LXC はあらゆる状況でコンテナのための cgroup マウントを実行します。それ以外は <option>cgroup-full:rw</option> と同様です。これは主に cgroup 名前空間が有効な場合に便利です。この場合は完全に安全ですので、LXC は通常コンテナの init バイナリが cgroup をマウントしたままの状態にしておきます。
                </para>
              </listitem>

              <listitem>
                <para>
                  <!--
                  <option>cgroup-full</option> (without specifier):
                  defaults to <option>cgroup-full:rw</option> if the
                  container retains the CAP_SYS_ADMIN capability,
                  <option>cgroup-full:mixed</option> otherwise.
                  -->
                  <option>cgroup-full</option> (マウントオプションなしの場合):
                  コンテナが CAP_SYS_ADMIN ケーパビリティを保持している場合、<option>cgroup-full:rw</option> となります。保持していない場合、<option>cgroup-full:mixed</option> となります。
                </para>
              </listitem>

            </itemizedlist>
            <para>
              <!--
              If cgroup namespaces are enabled, then any <option>cgroup</option>
              auto-mounting request will be ignored, since the container can
              mount the filesystems itself, and automounting can confuse the
              container init.
                -->
              cgroup 名前空間が有効の場合、<option>cgroup</option> の自動マウントの指定はどれも無視されます。これは、コンテナが自身でファイルシステムをマウントするため、自動マウントがコンテナの init を混乱させる可能性があるためです。
            </para>
            <para>
              <!--
              Note that if automatic mounting of the cgroup filesystem
              is enabled, the tmpfs under
              <filename>/sys/fs/cgroup</filename> will always be
              mounted read-write (but for the <option>:mixed</option>
              and <option>:ro</option> cases, the individual
              hierarchies,
              <filename>/sys/fs/cgroup/$hierarchy</filename>, will be
              read-only). This is in order to work around a quirk in
              Ubuntu's
              <citerefentry>
                <refentrytitle>mountall</refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              command that will cause containers to wait for user
              input at boot if
              <filename>/sys/fs/cgroup</filename> is mounted read-only
              and the container can't remount it read-write due to a
              lack of CAP_SYS_ADMIN.
              -->
              cgroup ファイルシステムの自動マウントが有効の場合、<filename>/sys/fs/cgroup</filename> 以下の tmpfs は常に読み書き可能でマウントされることに注意が必要です (しかし <option>:mixed</option> と <option>:ro</option> の場合は、個々の階層の <filename>/sys/fs/cgroup/$hierarchy</filename> は読み込み専用となるでしょう)。これは Ubuntu の
              <citerefentry>
                <refentrytitle>mountall</refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              コマンドの特異な動きに対処するためのものです。特異な動きとは、<filename>/sys/fs/cgroup</filename> が読み込み専用でマウントされた状態で、コンテナが CAP_SYS_ADMIN を持たない場合、/sys/fs/cgroup を読み書き可能で再マウントしようとしてできないため、コンテナのブート時にユーザからの入力を待ってしまうというものです。
            </para>
            <para>
              <!--
              Examples:
              -->
              例:
            </para>
            <programlisting>
              lxc.mount.auto = proc sys cgroup
              lxc.mount.auto = proc:rw sys:rw cgroup-full:rw
            </programlisting>
          </listitem>
        </varlistentry>

      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Root file system -->ルートファイルシステム</title>
      <para>
        <!--
        The root file system of the container can be different than that
        of the host system.
        -->
        コンテナのルートファイルシステムは、ホストのルートファイルシステムと異なるようにすることも可能です。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.rootfs.path</option>
          </term>
          <listitem>
            <para>
              <!--
              specify the root file system for the container. It can
              be an image file, a directory or a block device. If not
              specified, the container shares its root file system
              with the host.
              -->
              コンテナのルートファイルシステムを指定します。
              この値はイメージファイル、ディレクトリ、ブロックデバイスのどれかを取ることができます。
              もし指定されない場合、コンテナはホストとルートファイルシステムを共有します。
            </para>
            <para>
              <!--
          For directory or simple block-device backed containers,
          a pathname can be used.  If the rootfs is backed by a nbd
          device, then <filename>nbd:file:1</filename> specifies that
          <filename>file</filename> should be attached to a nbd device,
          and partition 1 should be mounted as the rootfs.
          <filename>nbd:file</filename> specifies that the nbd device
          itself should be mounted.  <filename>overlayfs:/lower:/upper</filename>
          specifies that the rootfs should be an overlay with <filename>/upper</filename>
          being mounted read-write over a read-only mount of <filename>/lower</filename>.
          For <filename>overlayfs</filename> multiple <filename>/lower</filename>
          directories can be specified. <filename>loop:/file</filename> tells lxc to attach
          <filename>/file</filename> to a loop device and mount the loop device.
          -->
              ディレクトリ、単純なブロックデバイスのバックエンドを持つコンテナの場合、パス名を使うことができます。
              もし rootfs が nbd デバイスの場合、<filename>nbd:file:1</filename> という指定は <filename>file</filename> を nbd デバイスとして使用し、その 1 番目のパーティションが rootfs としてマウントされます。
              <filename>nbd:file</filename> という指定は、nbd デバイス自身をマウントします。
              <filename>overlayfs:/lower:/upper</filename> という指定は、rootfs は <filename>/lower</filename> という読み込み専用でマウントされるディレクトリの上に、<filename>/upper</filename> というディレクトリを読み書き可能で重ね合わせてマウントします。
              <filename>overlayfs</filename> は、複数の <filename>/lower</filename> ディレクトリを指定できます。
              <filename>loop:/file</filename> は <filename>/file</filename> を loop デバイスとして使用し、loop デバイスをマウントします。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.rootfs.mount</option>
          </term>
          <listitem>
            <para>
              <!--
              where to recursively bind <option>lxc.rootfs.path</option>
              before pivoting.  This is to ensure success of the
              <citerefentry>
                <refentrytitle><command>pivot_root</command></refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              syscall.  Any directory suffices, the default should
              generally work.
              -->
              root ファイルシステムの変更の前に、<option>lxc.rootfs.path</option> を再帰的にどこにバインドするのかを指定します。これは
              <citerefentry>
                <refentrytitle><command>pivot_root</command></refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>
              システムコールが確実に成功する事を保証します。
              どんなディレクトリでも良く、デフォルトでも通常は動くはずです。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.rootfs.options</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify extra mount options to use when mounting the rootfs.
                The format of the mount options corresponds to the
                format used in fstab. In addition, LXC supports the custom
                <option>idmap=</option> mount option. This option can be used
                to tell LXC to create an idmapped mount for the container's
                rootfs. This is useful when the user doesn't want to recursively
                chown the rootfs of the container to match the idmapping of the
                user namespace the container is going to use. Instead an
                idmapped mount can be used to handle this.
                The argument for
                <option>idmap=</option>
                can either be a path pointing to a user namespace file that
                LXC will open and use to idmap the rootfs or the special value
                "container" which will instruct LXC to use
                the container's user namespace to idmap the rootfs.
                -->
                rootfs をマウントするときに使うマウントオプション。マウントオプションのフォーマットは fstab で使うフォーマットと同じです。
                加えて、LXC では独自の <option>idmap=</option> マウントオプションが使えます。このオプションを使うと、LXC に対してコンテナの rootfs を idmapped マウントするように指示できます。
                これは、コンテナが使うユーザー名前空間の ID マッピングと一致させるために、コンテナの rootfs を再帰的に chown したくない場合に役に立ちます。代わりに idmapped マウントが使えます。
                <option>idmap=</option> の引数は、LXC が開いて rootfs を idmap するのに使うユーザー名前空間ファイルを指すパス、もしくは "container" という特別な値のどちらかです。"container" という値は、コンテナのユーザー名前空間を使って rootfs を idmap するように LXC に指示します。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.rootfs.managed</option>
          </term>
          <listitem>
            <para>
              <!--
              Set this to 0 to indicate that LXC is not managing the
              container storage, then LXC will not modify the
              container storage. The default is 1.
                -->
              LXC がコンテナのストレージを管理していない場合は、この値を 0 に設定します。
              0 に設定すると、LXC はコンテナのストレージを変更しません。デフォルト値は 1 です。
            </para>
          </listitem>
        </varlistentry>

      </variablelist>
    </refsect2>

    <refsect2>
      <title>Control group ("cgroup")</title>
      <para>
        <!--
        The control group section contains the configuration for the
        different subsystem. <command>lxc</command> does not check the
        correctness of the subsystem name. This has the disadvantage
        of not detecting configuration errors until the container is
        started, but has the advantage of permitting any future
        subsystem.
        -->
        CONTROL GROUP セクションは、(lxc とは) 別のサブシステムの設定を含みます。
        <command>lxc</command> は、このサブシステム名の正しさはチェックしません。
        実行時のエラーを検出するのに不便ですが、別の将来のサブシステムをサポート出来るという有利な点もあります。
      </para>

      <para>
        <!--
        The kernel implementation of cgroups has changed significantly over the
        years. With Linux 4.5 support for a new cgroup filesystem was added
        usually referred to as "cgroup2" or "unified hierarchy". Since then the
        old cgroup filesystem is usually referred to as "cgroup1" or the
        "legacy hierarchies". Please see the cgroups manual page for a detailed
        explanation of the differences between the two versions.
          -->
        カーネルにおける cgroup 実装は長年にわたって大きく変化してきました。
        Linux 4.5 で新しい cgroup ファイルシステムのサポートが追加されました。通常は "cgroup2" や "unified hierarchy"（単一階層構造） と呼ばれています。
        それ以来、通常は古い cgroup ファイルシステムは "cgroup1" や "legacy hierarchies"（レガシー階層構造）と呼ばれています。
        この 2 つのバージョンの違いについての詳細な説明は、cgroup のマニュアルページをご覧ください。
      </para>

      <para>
        <!--
        LXC distinguishes settings for the legacy and the unified hierarchy by
        using different configuration key prefixes. To alter settings for
        controllers in a legacy hierarchy the key prefix
        <option>lxc.cgroup.</option> must be used and in order to alter the
        settings for a controller in the unified hierarchy the
        <option>lxc.cgroup2.</option> key must be used. Note that LXC will
        ignore <option>lxc.cgroup.</option> settings on systems that only use
        the unified hierarchy. Conversely, it will ignore
        <option>lxc.cgroup2.</option> options on systems that only use legacy
        hierarchies.
        -->
        LXC は cgroup1（レガシー階層構造）と cgroup2（単一階層構造）に対する設定を、異なる設定プレフィックスを使って区別しています。
        cgroup1 に対する設定を変更するには <option>lxc.cgroup.</option> というプレフィックスを使う必要があり、cgroup2 の設定を変更するには <option>lxc.cgroup2.</option> を使う必要があります。
        LXC は、cgroup2 だけが使われているシステム上の <option>lxc.cgroup.</option> を無視します。逆に cgroup1 だけが使われているシステム上の <option>lxc.cgroup2.</option> を無視します。
      </para>

      <para>
        <!--
        At its core a cgroup hierarchy is a way to hierarchically organize
        processes. Usually a cgroup hierarchy will have one or more
        "controllers" enabled. A "controller" in a cgroup hierarchy is usually
        responsible for distributing a specific type of system resource along
        the hierarchy. Controllers include the "pids" controller, the "cpu"
        controller, the "memory" controller and others. Some controllers
        however do not fall into the category of distributing a system
        resource, instead they are often referred to as "utility" controllers.
        One utility controller is the device controller. Instead of
        distributing a system resource it allows one to manage device access.
        -->
        cgroup 階層の本質は、プロセスを階層的に構造化する方法です。通常は、cgroup 階層では 1 つ以上の「コントローラー」が有効になっています。
        通常、cgroup 階層の「コントローラー」は階層に従って特定のタイプのシステムリソースを分配する役割を果たします。
        コントローラーには "pids" コントローラー、"cpu" コントローラー、"memory" コントローラーなどがあります。
        しかし、システムリソースの分配するという役割に該当しないコントローラーもあります。このようなコントローラーは「ユーティリティー」コントローラーと呼ばれたりします。
        ユーティリティーコントローラーの 1 つにデバイスコントローラーがあります。このコントローラーはシステムリソースを分配する代わりにデバイスへのアクセスを管理できます。
      </para>

      <para>
        <!--
        In the legacy hierarchy the device controller was implemented like most
        other controllers as a set of files that could be written to. These
        files where named "devices.allow" and "devices.deny". The legacy device
        controller allowed the implementation of both "allowlists" and
        "denylists".
        -->
        cgroup1 では、デバイスコントローラーは他の多くのコントローラーと同様に、書き込みできるファイルのセットとして実装されていました。
        これらのファイルは "devices.allow" と "devices.deny" という名前のファイルでした。レガシーデバイスコントローラーは「許可リスト（allowlists）」と「拒否リスト（denylists）」の両方を実装できました。
      </para>

      <para>
        <!--
        An allowlist is a device program that by default blocks access to all
        devices. In order to access specific devices "allow rules" for
        particular devices or device classes must be specified. In contrast, a
        denylist is a device program that by default allows access to all
        devices. In order to restrict access to specific devices "deny rules"
        for particular devices or device classes must be specified.
        -->
        許可リスト（allowlist）とは、すべてのデバイスへのアクセスをブロックするデバイスプログラムです。特定のデバイスへのアクセスを行うには、特定のデバイスもしくはデバイスクラスに対する「許可ルール（allow rules）」を指定する必要があります。
        一方、拒否リスト（denylist）はデフォルトですべてのデバイスへのアクセスを許可するデバイスプログラムです。特定のデバイスへのアクセスを拒否するには、特定のデバイスもしくはデバイスクラスに対する「拒否ルール（deny rules）」を指定する必要があります。
      </para>

      <para>
        <!--
        In the unified cgroup hierarchy the implementation of the device
        controller has completely changed. Instead of files to read from and
        write to a eBPF program of
        <option>BPF_PROG_TYPE_CGROUP_DEVICE</option> can be attached to a
        cgroup. Even though the kernel implementation has changed completely
        LXC tries to allow for the same semantics to be followed in the legacy
        device cgroup and the unified eBPF-based device controller. The
        following paragraphs explain the semantics for the unified eBPF-based
        device controller.
        -->
        cgroup2 では、デバイスコントローラーの実装が完全に変わりました。読み書きするファイルの代わりに、<option>BPF_PROG_TYPE_CGROUP_DEVICE</option> の eBPF プログラムを cgroup にアタッチできます。
        カーネルの実装が完全に変わったのにもかかわらず、LXC は cgroup1 のデバイスコントローラーと cgroup2 の eBPF ベースのデバイスコントローラーで同じセマンティクスに従えるようにしています。
        このあとの段落では、cgroup2 の eBPF デバイスコントローラーに対するセマンティクスを説明します。
      </para>

      <para>
        <!--
        As mentioned the format for specifying device rules for the unified
        eBPF-based device controller is the same as for the legacy cgroup
        device controller; only the configuration key prefix has changed.
        Specifically, device rules for the legacy cgroup device controller are
        specified via <option>lxc.cgroup.devices.allow</option> and
        <option>lxc.cgroup.devices.deny</option> whereas for the
        cgroup2 eBPF-based device controller
        <option>lxc.cgroup2.devices.allow</option> and
        <option>lxc.cgroup2.devices.deny</option> must be used.
        -->
        先に述べたように、cgroup2 の eBPF ベースのデバイスコントローラーに対するデバイスルールを指定するフォーマットは、cgroup1 のデバイスコントローラーと同じです。ただし、設定キーのプレフィックスは変更されています。
        具体的には、cgroup1 のデバイスコントローラーに対するデバイスルールは <option>lxc.cgroup.devices.allow</option> と <option>lxc.cgroup.devices.deny</option> を使って指定します。一方、cgroup2 の eBPF ベースのコントローラーでは <option>lxc.cgroup2.devices.allow</option> と <option>lxc.cgroup2.devices.deny</option> を使わなければなりません。
      </para>
      <para>
        <itemizedlist>
          <listitem>
            <para>
              <!--
              A denylist device rule
              <programlisting>
                lxc.cgroup2.devices.deny = a
              </programlisting>
              will cause LXC to instruct the kernel to block access to all
              devices by default. To grant access to devices allow device rules
              must be added via the <option>lxc.cgroup2.devices.allow</option>
              key. This is referred to as a "allowlist" device program.
              -->
              拒否リスト（denylist）のデバイスルール
              <programlisting>
                lxc.cgroup2.devices.deny = a
              </programlisting>
              は、カーネルに対してデフォルトですべてのデバイスへのアクセスをブロックするように LXC が指示します。
              デバイスへのアクセスを許可するには、デバイスに対する許可ルールを <option>lxc.cgroup2.devices.allow</option> を使って追加する必要があります。これは「許可リスト」デバイスプログラムとして参照されます。
            </para>
          </listitem>

          <listitem>
            <para>
              <!--
              An allowlist device rule
              <programlisting>
                lxc.cgroup2.devices.allow = a
              </programlisting>
              will cause LXC to instruct the kernel to allow access to all
              devices by default. To deny access to devices deny device rules
              must be added via <option>lxc.cgroup2.devices.deny</option> key.
              This is referred to as a "denylist" device program.
              -->
              許可リスト（allowlist）のデバイスルール
              <programlisting>
                lxc.cgroup2.devices.allow = a
              </programlisting>
              は、カーネルに対してすべてのデバイスへのアクセスをデフォルトで許可するように LXC が指示します。
              デバイスへのアクセスを拒否するには、デバイスに対する拒否ルールを <option>lxc.cgroup2.devices.deny</option> を使って追加する必要があります。これは「拒否リスト」デバイスプログラムとして参照されます。
            </para>
          </listitem>

          <listitem>
            <para>
              <!--
              Specifying any of the aforementioned two rules will cause all
              previous rules to be cleared, i.e. the device list will be reset.
              -->
              前述の 2 つのルールのいずれかを指定すると、それ以前に指定していたルールがすべてクリアされます。つまり、デバイスリストがリセットされます。
            </para>
          </listitem>

          <listitem>
            <para>
              <!--
            When an allowlist program is requested, i.e. access to all devices
            is blocked by default, specific deny rules for individual devices
            or device classes are ignored.
                -->
              許可リストプログラムが要求される場合、つまりデフォルトですべてのデバイスへのアクセスがブロックされている場合、個別のデバイスやデバイスクラスへの拒否ルールを指定しても無視されます。
            </para>
          </listitem>

          <listitem>
            <para>
              <!--
            When a denylist program is requested, i.e. access to all devices
            is allowed by default, specific allow rules for individual devices
            or device classes are ignored.
                -->
              拒否リストプログラムが要求される場合、つまりデフォルトですべてのデバイスへのアクセスが許可されている場合、個別のデバイスやデバイスクラスへの許可ルールを指定しても無視されます。
            </para>
          </listitem>
        </itemizedlist>
      </para>

      <para>
        <!--
        For example the set of rules:
          -->
        例えば、次のようなルールの組
        <programlisting>
          lxc.cgroup2.devices.deny = a
          lxc.cgroup2.devices.allow = c *:* m
          lxc.cgroup2.devices.allow = b *:* m
          lxc.cgroup2.devices.allow = c 1:3 rwm
        </programlisting>
        <!--
        implements an allowlist device program, i.e. the kernel will block
        access to all devices not specifically allowed in this list. This
        particular program states that all character and block devices may be
        created but only /dev/null might be read or written.
          -->
        は、許可リスト（allowlist）デバイスプログラムを実装します。つまり、カーネルはこのリストで許可されるように設定されていないすべてのデバイスへのアクセスをブロックします。
        このプログラムでは、すべてのキャラクターデバイスとブロックデバイスが作成できますが、読み書きは /dev/null に対してしか行なえません。
      </para>

      <para>
        <!--
        If we instead switch to the following set of rules:
          -->
        代わりに先のルールから次のようなルールの組に変更したとすると、
        <programlisting>
          lxc.cgroup2.devices.allow = a
          lxc.cgroup2.devices.deny = c *:* m
          lxc.cgroup2.devices.deny = b *:* m
          lxc.cgroup2.devices.deny = c 1:3 rwm
        </programlisting>
         <!--
         then LXC would instruct the kernel to implement a denylist, i.e. the
         kernel will allow access to all devices not specifically denied in
         this list. This particular program states that no character devices or
         block devices might be created and that /dev/null is not allow allowed
         to be read, written, or created.
           -->
        LXC はカーネルに拒否リスト（denylist）の実装を指示します。つまりカーネルはこのリストで拒否を指定していないすべてのデバイスへのアクセスを許可します。
        このプログラムでは、キャラクターデバイスとブロックデバイスは作成できません。そして /dev/null の読み書きと作成は許可されません。
      </para>

      <para>
        <!--
         Now consider the same program but followed by a "global rule"
         which determines the type of device program (allowlist or
         denylist) as explained above:
          -->
        ここで、同じプログラムでも、前述のようにデバイスのプログラムタイプを決定するような「グローバルルール」が続いている場合を考えてみましょう。
        <programlisting>
          lxc.cgroup2.devices.allow = a
          lxc.cgroup2.devices.deny = c *:* m
          lxc.cgroup2.devices.deny = b *:* m
          lxc.cgroup2.devices.deny = c 1:3 rwm
          lxc.cgroup2.devices.allow = a
        </programlisting>
        <!--
        The last line will cause LXC to reset the device list without changing
        the type of device program.
          -->
        最後の行は、デバイスプログラムのタイプを変更せずに、LXC がデバイスリストをリセットしてしまいます。
      </para>

      <para>
        <!--
        If we specify:
          -->
        次のように指定した場合、
        <programlisting>
          lxc.cgroup2.devices.allow = a
          lxc.cgroup2.devices.deny = c *:* m
          lxc.cgroup2.devices.deny = b *:* m
          lxc.cgroup2.devices.deny = c 1:3 rwm
          lxc.cgroup2.devices.deny = a
        </programlisting>
        <!--
        instead then the last line will cause LXC to reset the device list and
        switch from an allowlist program to a denylist program.
          -->
        前の例と違って最後の行によって、LXC はデバイスリストをリセットし、許可リスト（allowlist）から拒否リスト（denylist）にプログラムを変更してしまいます。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.cgroup.[control name].[controller file]</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the control group value to be set on a legacy cgroup
              hierarchy. The controller name is the literal name of the control
              group. The permitted names and the syntax of their values is not
              dictated by LXC, instead it depends on the features of the Linux
              kernel running at the time the container is started, eg.
              <option>lxc.cgroup.cpuset.cpus</option>
              -->
              レガシー cgroup 階層 (cgroup v1) に設定する値を指定します。コントローラー名は control group そのままの名前です。
              許される名前や値の書式は LXC が指定することはなく、コンテナが実行された時に実行されている Linux カーネルの機能に依存します。
              例えば <option>lxc.cgroup.cpuset.cpus</option> のようになります。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cgroup2.[controller name].[controller file]</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the control group value to be set on the unified cgroup
              hierarchy. The controller name is the literal name of the control
              group. The permitted names and the syntax of their values is not
              dictated by LXC, instead it depends on the features of the Linux
              kernel running at the time the container is started, eg.
              <option>lxc.cgroup2.memory.high</option>
              -->
              単一の cgroup 階層 (cgroup v2) に設定する値を指定します。
              許される名前や値の書式は LXC が指定することはなく、コンテナが実行された時に実行されている Linux カーネルの機能に依存します。
              例えば <option>lxc.cgroup2.memory.high</option> のようになります。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cgroup.dir</option>
          </term>
          <listitem>
            <para>
              <!--
              specify a directory or path in which the container's cgroup will
              be created. For example, setting
              <option>lxc.cgroup.dir = my-cgroup/first</option> for a container
              named "c1" will create the container's cgroup as a sub-cgroup of
              "my-cgroup". For example, if the user's current cgroup "my-user"
              is located in the root cgroup of the cpuset controllerin in a
              cgroup v1 hierarchy this would create the cgroup
              "/sys/fs/cgroup/cpuset/my-user/my-cgroup/first/c1" for the
              container. Any missing cgroups will be created by LXC. This
              presupposes that the user has write access to its current cgroup.
              -->
              コンテナの cgroup を作成するパスやディレクトリを指定します。
              例えば、"c1" という名前のコンテナで <option>lxc.cgroup.dir = my-cgroup/first</option> のように設定すると、"my-cgroup" のサブ cgroup のようにコンテナの cgroup を作成します。
              例えば、ユーザのカレントの cgroup である "my-user" が cgroup v1 階層にある cpuset コントローラの root cgroup 内に存在する場合、この設定は "/sys/fs/cgroup/cpuset/my-user/my-cgroup/first/c1" という cgroup をこのコンテナ向けに作成します。
              存在しない cgroup は LXC が作成しますが、ユーザがカレントの cgroup に書き込み権を持っていることが前提となります。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cgroup.dir.container</option>
          </term>
          <listitem>
            <para>
	      <!--
              This is similar to <option>lxc.cgroup.dir</option>, but must be
              used together with <option>lxc.cgroup.dir.monitor</option> and
              affects only the container's cgroup path. This option is mutually
              exclusive with <option>lxc.cgroup.dir</option>.
              Note that the final path the container attaches to may be
              extended further by the
              <option>lxc.cgroup.dir.container.inner</option> option.
	      -->
	      これは <option>lxc.cgroup.dir</option> と同様の設定ですが、かならず <option>lxc.cgroup.dir.monitor</option> と同時に使わなければなりません。そして、設定はコンテナの cgroup パスにのみ影響を与えます。このオプションは <option>lxc.cgroup.dir</option> と同時に設定できません。コンテナがアタッチされる最終的なパスは <option>lxc.cgroup.dir.container.inner</option> オプションによりさらに変更される可能性があります。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cgroup.dir.monitor</option>
          </term>
          <listitem>
            <para>
	      <!--
              This is the monitor process counterpart to
              <option>lxc.cgroup.dir.container</option>.
	      -->
	      このオプションは、モニタプロセスに対して<option>lxc.cgroup.dir.container</option> と同様の働きをします。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cgroup.dir.monitor.pivot</option>
          </term>
          <listitem>
            <para>
              <!--
              On container termination the PID of the monitor process is attached to this cgroup.
              This path should not be a subpath of any other configured cgroup dir to ensure
              proper removal of other cgroup paths on container termination.
              -->
              コンテナ終了時に、モニタープロセスの PID がここで指定した cgroup にアタッチされます。
              コンテナ終了時に、他の cgroup パスが確実に適切に削除されるように、ここに設定するパスは他で設定した cgroup ディレクトリのサブパスにすべきではありません。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cgroup.dir.container.inner</option>
          </term>
          <listitem>
            <para>
	      <!--
              Specify an additional subdirectory where the cgroup namespace
              will be created. With this option, the cgroup limits will be
              applied to the outer path specified in
              <option>lxc.cgroup.dir.container</option>, which is not accessible
              from within the container, making it possible to better enforce
              limits for privileged containers in a way they cannot override
              them.
              This only works in conjunction with the
              <option>lxc.cgroup.dir.container</option> and
              <option>lxc.cgroup.dir.monitor</option> options and has otherwise
              no effect.
	      -->
	      cgroup 名前空間が作られる追加のサブディレクトリを指定します。このオプションを使うと、cgroup の制限は <option>lxc.cgroup.dir.container</option> で指定した外部パスに適用されます。<option>lxc.cgroup.dir.container</option> はコンテナ内部からアクセスできないため、特権コンテナに対する制限を上書きできない方法でよりよい方法で強制できます。
	      このオプションは <option>lxc.cgroup.dir.container</option> と <option>lxc.cgroup.dir.monitor</option> と同時に指定したときのみ機能し、それ以外の場合は効果がありません。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cgroup.relative</option>
          </term>
          <listitem>
            <para>
              <!--
              Set this to 1 to instruct LXC to never escape to the
              root cgroup. This makes it easy for users to adhere to
              restrictions enforced by cgroup2 and
              systemd. Specifically, this makes it possible to run LXC
              containers as systemd services.
                -->
              LXC に root cgroup へのエスケープを行わないように指示するには、この値を 1 に設定してください。
              これにより、ユーザは cgroup2 と systemd が強制する制限を遵守するのが容易になります。
              具体的には、これにより LXC コンテナを systemd のサービスとして実行できます。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Capabilities -->ケーパビリティ</title>
      <para>
        <!--
        The capabilities can be dropped in the container if this one
        is run as root.
        -->
        コンテナが root 権限で実行されていても、コンテナ内ではケーパビリティ (capabilities) を削除する事は可能です。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.cap.drop</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the capability to be dropped in the container. A
              single line defining several capabilities with a space
              separation is allowed. The format is the lower case of
              the capability definition without the "CAP_" prefix,
              eg. CAP_SYS_MODULE should be specified as
              sys_module. See
              <citerefentry>
                <refentrytitle><command>capabilities</command></refentrytitle>
                <manvolnum>7</manvolnum>
              </citerefentry>,
              If used with no value, lxc will clear any drop capabilities
              specified up to this point.
              -->
              コンテナ内で削除するケーパビリティ (capability) を指定します。
              一行でスペース区切りで複数のケーパビリティを指定することも可能です。
              指定は、"CAP_" というプレフィックスなしで、小文字でケーパビリティを指定します。
              例えば、CAP_SYS_MODULE というケーパビリティは sys_module と指定する必要があります。
              詳しくは以下を参照してください。
              <citerefentry>
                <refentrytitle><command>capabilities</command></refentrytitle>
                <manvolnum>7</manvolnum>
              </citerefentry>
              この設定を、値を指定しない状態で使った場合、それ以前に指定された削除対象のケーパビリティの指定をすべてクリアします (lxc.cap.drop に何も指定しない状態になります)。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.cap.keep</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the capability to be kept in the container. All other
              capabilities will be dropped. When a special value of "none" is
              encountered, lxc will clear any keep capabilities specified up
              to this point. A value of "none" alone can be used to drop all
              capabilities.
              -->
              コンテナ内で維持するケーパビリティを指定します。指定した以外の全てのケーパビリティはドロップされます。
              特別な値 "none" が指定されている時点で、lxc はこの時点で保持することになっている全てのケーパビリティをクリアします。"none" を単独で使用するとすべてのケーパビリティを削除できます。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>名前空間 <!-- Namespace --></title>
      <para>
        <!--
        A namespace can be cloned (<option>lxc.namespace.clone</option>),
        kept (<option>lxc.namespace.keep</option>) or shared
        (<option>lxc.namespace.share.[namespace identifier]</option>).
        -->
        名前空間は clone したり (<option>lxc.namespace.clone</option>)、keep したり (<option>lxc.namespace.keep</option>)、share したり (<option>lxc.namespace.share.[namespace identifier]</option>) できます。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.namespace.clone</option>
          </term>
          <listitem>
            <para>
              <!--
            Specify namespaces which the container is supposed to be created
            with. The namespaces to create are specified as a space separated
            list. Each namespace must correspond to one of the standard
            namespace identifiers as seen in the
            <filename>/proc/PID/ns</filename> directory.
            When <option>lxc.namespace.clone</option> is not explicitly set all
            namespaces supported by the kernel and the current configuration
            will be used.
            -->
              コンテナ作成時に作成する名前空間を指定します。作成する名前空間はスペース区切りのリストで指定します。指定する名前空間名は、<filename>/proc/PID/ns</filename> ディレクトリ内に存在する標準の名前空間指示子でなければなりません。
              <option>lxc.namespace.clone</option> を明示的に設定していない場合は、カーネルがサポートするすべての名前空間と現在の設定が使われます。
            </para>

            <para>
              <!--
            To create a new mount, net and ipc namespace set
            <option>lxc.namespace.clone=mount net ipc</option>.
            -->
              新しいマウント、ネット、IPC 名前空間を作る場合は <option>lxc.namespace.clone=mount net ipc</option> と指定します。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.namespace.keep</option>
          </term>
          <listitem>
            <para>
              <!--
            Specify namespaces which the container is supposed to inherit from
            the process that created it. The namespaces to keep are specified as
            a space separated list. Each namespace must correspond to one of the
            standard namespace identifiers as seen in the
            <filename>/proc/PID/ns</filename> directory.
            The <option>lxc.namespace.keep</option> is a
            denylist option, i.e. it is useful when enforcing that containers
            must keep a specific set of namespaces.
            -->
              コンテナが、作成元のプロセスから継承する (新しい名前空間を作らずに元のプロセスの名前空間のまま実行する) 名前空間を指定します。継承する名前空間はスペース区切りのリストで指定します。指定する名前空間名は、<filename>/proc/PID/ns</filename> ディレクトリ内に存在する標準の名前空間指示子でなければなりません。<option>lxc.namespace.keep</option> はブラックリストを指定するオプションです。つまり、コンテナに特定の名前空間を使い続けることを強制したい場合に便利です。
            </para>

            <para>
              <!--
            To keep the network, user and ipc namespace set
            <option>lxc.namespace.keep=user net ipc</option>.
            -->
              ネットワーク、ユーザ、IPC 名前空間を元のプロセスの名前空間のままで実行したい場合は <option>lxc.namespace.keep=user net ipc</option> と指定します。
            </para>

            <para>
              <!--
            Note that sharing pid namespaces will likely not work with most init
            systems.
                -->
              PID 名前空間を共有すると、ほとんどの init で動作しない可能性があることに注意してください。
            </para>

            <para>
              <!--
            Note that if the container requests a new user namespace and the
            container wants to inherit the network namespace it needs to inherit
            the user namespace as well.
                -->
              コンテナが新しいユーザ名前空間をリクエストし、そのコンテナがネットワーク名前空間は継承したい場合は、ユーザ名前空間も同様に継承する必要があることに注意してください。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.namespace.share.[namespace identifier]</option>
          </term>
          <listitem>
            <para>
              <!--
            Specify a namespace to inherit from another container or process.
            The <option>[namespace identifier]</option> suffix needs to be
            replaced with one of the namespaces that appear in the
            <filename>/proc/PID/ns</filename> directory.
            -->
              他のコンテナやプロセスから継承する名前空間を指定します。<option>[namespace identifier]</option> には、<filename>/proc/PID/ns</filename> ディレクトリ内に現れる名前空間のひとつが入ります。
            </para>

            <para>
              <!--
            To inherit the namespace from another process set the
            <option>lxc.namespace.share.[namespace identifier]</option> to the PID of
            the process, e.g. <option>lxc.namespace.share.net=42</option>.
            -->
              他のプロセスから名前空間を継承するには、<option>lxc.namespace.share.[namespace identifier]</option> の値をプロセスの PID に設定します。例えば <option>lxc.namespace.share.net=42</option> のようになります。
            </para>

            <para>
              <!--
            To inherit the namespace from another container set the 
            <option>lxc.namespace.share.[namespace identifier]</option> to the name of
            the container, e.g. <option>lxc.namespace.share.pid=c3</option>.
            -->
              他のコンテナから名前空間を継承するには、<option>lxc.namespace.share.[namespace identifier]</option> の値をコンテナ名に設定します。例えば <option>lxc.namespace.share.pid=c3</option> のようになります。
            </para>

            <para>
              <!--
            To inherit the namespace from another container located in a
            different path than the standard liblxc path set the
            <option>lxc.namespace.share.[namespace identifier]</option> to the full
            path to the container, e.g.
            <option>lxc.namespace.share.user=/opt/c3</option>.
            -->
              標準の liblxc のパスとは異なるコンテナパスに存在する他のコンテナから名前空間を継承するには、<option>lxc.namespace.share.[namespace identifier]</option> をそのコンテナのフルパスで指定します。例えば <option>lxc.namespace.share.user=/opt/c3</option> のようになります。
            </para>

            <para>
              <!--
            In order to inherit namespaces the caller needs to have sufficient
            privilege over the process or container.
                -->
              名前空間を継承するためには、呼び出し元が継承元のプロセスまたはコンテナに対して十分な権限を持っている必要があります。
            </para>

            <para>
              <!--
            Note that sharing pid namespaces between system containers will
            likely not work with most init systems.
                -->
              システムコンテナ間での PID 名前空間の共有は、ほとんどの init システムではうまく動作しない可能性があることに注意が必要です。
            </para>

            <para>
              <!--
            Note that if two processes are in different user namespaces and one
            process wants to inherit the other's network namespace it usually
            needs to inherit the user namespace as well.
                -->
              ふたつのプロセスが異なるユーザ名前空間に存在し、そのうちのひとつが他のネットワーク名前空間を継承したい場合、通常はユーザ名前空間も同様に継承する必要があることに注意が必要です。
            </para>

            <para>
	      <!--
            Note that without careful additional configuration of an LSM,
            sharing user+pid namespaces with a task may allow that task to
            escalate privileges to that of the task calling liblxc.
		-->
	      LSM で慎重に設定を追加しないで、タスクでユーザ + PID 名前空間を共有すると、そのタスクは liblxc を呼び出したタスクの権限に昇格できることに注意が必要です。
            </para>

          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.time.offset.boot</option>
          </term>
          <listitem>
            <para>
              <!--
            Specify a positive or negative offset for the boottime clock. The
            format accepts hours (h), minutes (m), seconds (s),
            milliseconds (ms), microseconds (us), and nanoseconds (ns).
                -->
              ブートタイム(boottime)クロックの正または負のオフセット値を指定します。フォーマットは、時(h)、分(m)、秒(s)、ミリ秒(ms)、マイクロ秒(us)、ナノ秒(ns)を指定できます。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.time.offset.monotonic</option>
          </term>
          <listitem>
            <para>
              <!--
            Specify a positive or negative offset for the montonic clock. The
            format accepts hours (h), minutes (m), seconds (s),
            milliseconds (ms), microseconds (us), and nanoseconds (ns).
                -->
              monotonicクロックの正または負のオフセット値を指定します。フォーマットは、時(h)、分(m)、秒(s)、ミリ秒(ms)、マイクロ秒(us)、ナノ秒(ns)を指定できます。
            </para>
          </listitem>
        </varlistentry>

      </variablelist>
    </refsect2>

    <refsect2>
      <title>リソース制限 <!-- Resource limits --></title>
      <para>
        <!--
        The soft and hard resource limits for the container can be changed.
        Unprivileged containers can only lower them. Resources which are not
        explicitly specified will be inherited.
          -->
        コンテナに対するソフトもしくはハードリミットを変更できます。非特権コンテナでは、制限を下げることしかできません。明示的に指定されていないリソースは継承されます。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.prlimit.[limit name]</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the resource limit to be set. A limit is specified as two
              colon separated values which are either numeric or the word
              'unlimited'. A single value can be used as a shortcut to set both
              soft and hard limit to the same value. The permitted names the
              "RLIMIT_" resource names in lowercase without the "RLIMIT_"
              prefix, eg. RLIMIT_NOFILE should be specified as "nofile". See
              <citerefentry>
                <refentrytitle><command>setrlimit</command></refentrytitle>
                <manvolnum>2</manvolnum>
              </citerefentry>.
              If used with no value, lxc will clear the resource limit
              specified up to this point. A resource with no explicitly
              configured limitation will be inherited from the process starting
              up the container.
              -->
              設定したいリソースと制限値を指定します。制限値はコロンで区切られた 2 つの値で指定します。値は数値もしくは 'unlimited' で指定します。ソフトもハードも同じ値を指定する場合は単一の値を指定できます。指定できる名前は、"RLIMIT_" 接頭辞がなく小文字で書かれた、"RLIMIT_" リソース名です。例えば、RLIMIT_NOFILE は "nofile" と指定します。詳しくは
              <citerefentry>
                <refentrytitle><command>setrlimit</command></refentrytitle>
                <manvolnum>2</manvolnum>
              </citerefentry> を参照してください。
              値を指定せずに使用した場合、lxc はこの指定以前に設定されたリソース制限をクリアします。明示的に制限が設定されていないリソースについては、コンテナを起動したプロセスから継承します。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>Sysctl</title>
      <para>
        <!--
        Configure kernel parameters for the container.
          -->
        コンテナ用のカーネルパラメータを設定します。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.sysctl.[kernel parameters name]</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the kernel parameters to be set. The parameters available 
              are those listed under /proc/sys/.
              Note that not all sysctls are namespaced. Changing Non-namespaced
              sysctls will cause the system-wide setting to be modified.
                -->
              設定したいカーネルパラメータを指定します。指定できるパラメータは /proc/sys 以下に存在するものです。
              すべての sysctl パラメータが仮想化（名前空間化）されているわけではないことに注意してください。仮想化されていない sysctl を設定すると、システムワイドで設定が変更されてしまいます。
              <citerefentry>
                <refentrytitle><command>sysctl</command></refentrytitle>
                <manvolnum>8</manvolnum>
              </citerefentry>.
              <!--
              If used with no value, lxc will clear the parameters specified up 
              to this point.
                -->
              値を指定しないでこの設定を指定した場合は、この設定より前に設定されたパラメータをクリアします。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Apparmor profile -->Apparmor プロファイル</title>
      <para>
        <!--
        If lxc was compiled and installed with apparmor support, and the host
        system has apparmor enabled, then the apparmor profile under which the
        container should be run can be specified in the container
        configuration.  The default is <command>lxc-container-default-cgns</command>
        if the host kernel is cgroup namespace aware, or
        <command>lxc-container-default</command> otherwise.
        -->
        lxc が apparmor サポートでコンパイルされ、インストールされている場合で、ホストで apparmor が有効な場合、コンテナが従って動くべき apparmor プロファイルは、コンテナの設定で指定することが可能です。
        デフォルトは、ホストのカーネルで cgroup 名前空間が使える場合は <command>lxc-container-default-cgns</command>です。使えない場合は <command>lxc-container-default</command> です。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.apparmor.profile</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the apparmor profile under which the container should
              be run.  To specify that the container should be unconfined,
              use
              -->
              コンテナが従うべき apparmor プロファイルを指定します。
              コンテナが apparmor による制限を受けないように設定するには、以下のように設定します。
            </para>
              <programlisting>lxc.apparmor.profile = unconfined</programlisting>
            <para>
              <!--
              If the apparmor profile should remain unchanged (i.e. if you
              are nesting containers and are already confined), then use
              -->
              もし apparmor プロファイルが変更されないままでなくてはならない場合 (ネストしたコンテナである場合や、すでに confined されている場合) は以下のように設定します。
            </para>
              <programlisting>lxc.apparmor.profile = unchanged</programlisting>
            <para>
              <!--
                If you instruct LXC to generate the apparmor profile,
                then use
                -->
              もし LXC に AppArmor プロファイルを生成するように指示するには次のように設定します。
            </para>
              <programlisting>lxc.apparmor.profile = generated</programlisting>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.apparmor.allow_incomplete</option>
          </term>
          <listitem>
            <para>
              <!--
              Apparmor profiles are pathname based.  Therefore many file
              restrictions require mount restrictions to be effective against
              a determined attacker.  However, these mount restrictions are not
              yet implemented in the upstream kernel.  Without the mount
              restrictions, the apparmor profiles still protect against accidental
              damager.
              -->
              apparmor プロファイルはパス名ベースですので、多数のファイルの制限を行う際、執念深い攻撃者に対して効果的であるためにはマウントの制限が必要です。
              しかし、これらのマウントの制限は upstream のカーネルではまだ実装されていません。マウントの制限なしでも、apparmor プロファイルによって予想外のダメージに対する保護が可能です。
            </para>
            <para>
              <!--
              If this flag is 0 (default), then the container will not be
              started if the kernel lacks the apparmor mount features, so that a
              regression after a kernel upgrade will be detected.  To start the
              container under partial apparmor protection, set this flag to 1.
              -->
              このフラグが 0 の場合 (デフォルト)、カーネルが apparmor のマウント機能をサポートしていない場合にコンテナが起動しません。これはカーネルを更新した後に機能が退行したことが検出できるようにするためです。
              不完全な apparmor の保護の下でコンテナを起動するためには、このフラグを 1 に設定してください。
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.apparmor.allow_nesting</option>
          </term>
          <listitem>
            <para>
              <!--
              If set this to 1, causes the following changes. When
              generated apparmor profiles are used, they will contain
              the necessary changes to allow creating a nested
              container. In addition to the usual mount points,
              <filename>/dev/.lxc/proc</filename>
              and <filename>/dev/.lxc/sys</filename> will contain
              procfs and sysfs mount points without the lxcfs
              overlays, which, if generated apparmor profiles are
              being used, will not be read/writable directly.
              -->
              1 に設定すると次のような変更が行われます。
              generated な AppArmor プロファイルが使われる場合、ネストしたコンテナを使うのに必要な変更が含まれます。通常のマウントポイントに加えて、lxcfs のオーバーレイなしで、<filename>/dev/.lxc/proc</filename> と <filename>/dev/.lxc/sys</filename> が procfs と sysfs のマウントポイントに含まれます。
              generated な AppArmor プロファイルが使われている場合は、直接読み書きはできません
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
            <option>lxc.apparmor.raw</option>
          </term>
          <listitem>
            <para>
              <!--
              A list of raw AppArmor profile lines to append to the
              profile. Only valid when using generated profiles.
                -->
              プロファイルに加える、生の AppArmor プロファイル行のリストです。generated なプロファイルを使っているときのみ有効です。
            </para>
          </listitem>
        </varlistentry>

      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- SELinux context -->SELinux コンテキスト</title>
      <para>
        <!--
        If lxc was compiled and installed with SELinux support, and the host
        system has SELinux enabled, then the SELinux context under which the
        container should be run can be specified in the container
        configuration.  The default is <command>unconfined_t</command>,
        which means that lxc will not attempt to change contexts.
        See @DATADIR@/lxc/selinux/lxc.te for an example policy and more
        information.
        -->
        lxc が SELinux サポートでコンパイルされ、インストールされている場合で、ホストで SELinux が有効な場合、コンテナが従って動くべき SELinux コンテキストは、コンテナの設定で指定することが可能です。
        デフォルトは <command>unconfined_t</command> であり、これは lxc がコンテキストを変えないという意味になります。
        ポリシーの例と追加の情報は @DATADIR@/lxc/selinux/lxc.te ファイルを参照してください。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.selinux.context</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify the SELinux context under which the container should
              be run or <command>unconfined_t</command>. For example
              -->
              コンテナが従うべき SELinux コンテキストを指定するか、<command>unconfined_t</command> を指定します。例えば以下のように設定します。
            </para>
            <programlisting>lxc.selinux.context = system_u:system_r:lxc_t:s0:c22</programlisting>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.selinux.context.keyring</option>
          </term>
          <listitem>
            <para>
	      <!--
              Specify the SELinux context under which the container's keyring
              should be created. By default this the same as lxc.selinux.context, or
              the the context lxc is executed under if lxc.selinux.context has not been set.
	      -->
	      コンテナのキーリングを作成する SELinux コンテキストを指定します。
	      デフォルトでは lxc.selinux.context と同じになります。
	      lxc.selinux.context が設定されていない場合は、LXC のコンテキストで実行されます。
            </para>
            <programlisting>lxc.selinux.context.keyring = system_u:system_r:lxc_t:s0:c22</programlisting>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>カーネルキーリング<!-- Kernel Keyring --></title>
      <para>
	<!--
        The Linux Keyring facility is primarily a way for various
        kernel components to retain or cache security data, authentication
        keys, encryption keys, and other data in the kernel. By default lxc
        will create a new session keyring for the started application.
	-->
	Linux キーリング機能は、さまざまなカーネルコンポーネントが、セキュリティーデータ、認証キー、暗号化キー、その他のデータをカーネルに保持またはキャッシュするための機能です。
	デフォルトでは、LXC は開始したアプリケーションのために、新しいセッションキーリングを作成します。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.keyring.session</option>
          </term>
          <listitem>
            <para>
	      <!--
              Disable the creation of new session keyring by lxc. The started
              application will then inherit the current session keyring.
              By default, or when passing the value 1, a new keyring will be created.
	      -->
	      LXC による新しいセッションキーリングの作成を無効にできます。その場合、開始したアプリケーションは、その時点のセッションキーリングを継承します。
	      デフォルトは 1 で、1 の場合は LXC は新しいキーリングを作成します。
            </para>
            <programlisting>lxc.keyring.session = 0</programlisting>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Seccomp configuration -->Seccomp の設定</title>
      <para>
        <!--
        A container can be started with a reduced set of available
        system calls by loading a seccomp profile at startup.  The
        seccomp configuration file must begin with a version number
        on the first line, a policy type on the second line, followed
        by the configuration.
        -->
        コンテナは、起動時に seccomp プロファイルをロードすることで、利用可能なシステムコールを減らして起動することが可能です。
        seccomp の設定ファイルは、1 行目がバージョン番号、2 行目がポリシーのタイプで始まる必要があり、その後に設定を書きます。
      </para>
      <para>
        <!--
        Versions 1 and 2 are currently supported.  In version 1, the
        policy is a simple allowlist.  The second line therefore must
        read "allowlist", with the rest of the file containing one (numeric)
        syscall number per line.  Each syscall number is allowlisted,
        while every unlisted number is denylisted for use in the container
        -->
        現時点では、バージョン番号は 1 と 2 をサポートしています。バージョン 1 では、ポリシーはシンプルなホワイトリストですので、2 行目は "allowlist" でなければなりません。
        そして残りの行には 1 行に 1 つずつ、システムコール番号を書きます。各行のシステムコール番号がホワイトリスト化され、リストにない番号は、そのコンテナではブラックリストに入ります。
      </para>

      <para>
        <!--
       In version 2, the policy may be denylist or allowlist,
       supports per-rule and per-policy default actions, and supports
       per-architecture system call resolution from textual names.
          -->
        バージョン 2 では、ポリシーはブラックリストもしくはホワイトリストで表され、ルールごとのアクションと、ポリシーごとのデフォルトのアクションを設定できます。そして、アーキテクチャごとの設定と、テキストで書かれたシステムコール名での設定が可能です。
      </para>
      <para>
        <!--
       An example denylist policy, in which all system calls are
       allowed except for mknod, which will simply do nothing and
       return 0 (success), looks like:
       -->
        以下にブラックリストのポリシーの例を示します。これは mknod 以外の全てのシステムコールが許可され、mknod が呼ばれると、何もせずに単に 0(成功) を返します。
      </para>

      <programlisting>
      2
      denylist
      mknod errno 0
      ioctl notify
      </programlisting>

      <para>
        <!--
      Specifying "errno" as action will cause LXC to register a seccomp filter
      that will cause a specific errno to be returned to the caller. The errno
      value can be specified after the "errno" action word.
          -->
        アクションとして "errno" を指定すると、LXC は seccomp フィルタを登録します。これにより、指定した errno を呼び出し元に返します。
        errno の値は "errno" という単語の後に指定します。
      </para>

      <para>
        <!--
      Specifying "notify" as action will cause LXC to register a seccomp
      listener and retrieve a listener file descriptor from the kernel. When a
      syscall is made that is registered as "notify" the kernel will generate a
      poll event and send a message over the file descriptor. The caller can
      read this message, inspect the syscalls including its arguments. Based on
      this information the caller is expected to send back a message informing
      the kernel which action to take. Until that message is sent the kernel
      will block the calling process. The format of the messages to read and
      sent is documented in seccomp itself.
          -->
        アクションとして "notify" を指定すると、LXC は seccomp リスナーを登録し、カーネルからリスナーのファイルディスクリプタを取得します。
        "notify" として指定しているシステムコールが作成されると、カーネルは poll イベントを生成し、ファイルディスクリプタを通してメッセージを送信します。
        呼び出し元はこのメッセージを読み、引数を含めてシステムコールを調査できます。
        呼び出し元はこの情報に基づき、どのアクションを取るべきかをカーネルに知らせるメッセージを送り返すことが期待されます。
        このメッセージが送られるまで、カーネルは呼び出し元のプロセスをブロックします。読み書きするメッセージのフォーマットは seccomp 自身に記述されています。
      </para>

      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.seccomp.profile</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify a file containing the seccomp configuration to
              load before the container starts.
              -->
              コンテナがスタートする前にロードする seccomp の設定を含むファイルを指定します。
             </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.seccomp.allow_nesting</option>
          </term>
          <listitem>
            <para>
              <!--
              If this flag is set to 1, then seccomp filters will be stacked
              regardless of whether a seccomp profile is already loaded.
              This allows nested containers to load their own seccomp profile.
              The default setting is 0.
                -->
              このオプションを 1 に設定すると、すでに seccomp プロファイルがロードされている、いないに関わらず、seccomp フィルタが重ね合わせられます。
              これにより、ネストされたコンテナが自身の seccomp プロファイルをロードできます。
              デフォルト値は 0 です。
             </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.seccomp.notify.proxy</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify a unix socket to which LXC will connect and forward
              seccomp events to. The path must be in the form
              unix:/path/to/socket or unix:@socket. The former specifies a
              path-bound unix domain socket while the latter specifies an
              abstract unix domain socket.
                -->
              LXC が接続し、seccomp イベントを転送する UNIX ソケットを指定します。
              パスは unix:/path/to/socket もしくは unix:@socket の形式でなければなりません。
              前者はパス指定の UNIX ドメインソケットを指定し、後者は抽象 (abstract) UNIX ドメインソケットの指定です。
             </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.seccomp.notify.cookie</option>
          </term>
          <listitem>
            <para>
              <!--
              An additional string sent along with proxied seccomp notification
              requests.
                -->
              プロキシーされた seccomp 通知リクエストと一緒に送る追加文字列。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title>PR_SET_NO_NEW_PRIVS</title>
      <para>
        <!--
              With PR_SET_NO_NEW_PRIVS active execve() promises not to grant
              privileges to do anything that could not have been done without
              the execve() call (for example, rendering the set-user-ID and
              set-group-ID mode bits, and file capabilities non-functional).
              Once set, this bit cannot be unset. The setting of this bit is
              inherited by children created by fork() and clone(), and preserved
              across execve().
              Note that PR_SET_NO_NEW_PRIVS is applied after the container has
              changed into its intended AppArmor profile or SElinux context.
          -->
        PR_SET_NO_NEW_PRIVS を付与すると、対象の execve() は、execve() の呼び出しなしでは実行できなかったことに対する特権を許可しなくなります (例えば、set-user-ID、set-group-ID 許可ビットや、ファイルケーパビリティが動作しなくなります)。
        一度設定されると、このビットは解除できません。このビットの設定は fork() や clone() で生成される子プロセスにも継承され、execve() の前後で保持されます。
        PR_SET_NO_NEW_PRIVS は、コンテナに適用しようとする AppArmor プロファイルもしくは SELinux コンテキストへの変更がなされたあとに適用されます。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.no_new_privs</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify whether the PR_SET_NO_NEW_PRIVS flag should be set for the
              container. Set to 1 to activate.
                -->
              コンテナに対して PR_SET_NO_NEW_PRIVS ビットを設定するかどうかを指定します。1 に設定すると有効になります。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- UID mappings -->UID のマッピング</title>
      <para>
        <!--
        A container can be started in a private user namespace with
        user and group id mappings.  For instance, you can map userid
        0 in the container to userid 200000 on the host.  The root
        user in the container will be privileged in the container,
        but unprivileged on the host.  Normally a system container
        will want a range of ids, so you would map, for instance,
        user and group ids 0 through 20,000 in the container to the
        ids 200,000 through 220,000.
        -->
        コンテナは、ユーザとグループの id のマッピングを持った専用のユーザ名前空間で起動することが可能です。
        たとえば、コンテナ内のユーザ id 0 を、ホストのユーザ id 200000 にマッピングすることが可能です。
        コンテナの root ユーザはコンテナ内では特権を持ちますが、ホストでは特権を持ちません。
        通常は、システムコンテナは id の範囲を要求し、それをマッピングします。
        例えば、コンテナ内のユーザとグループの id 0 から 20,000 を 200,000 から 220,000 にマッピングします。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.idmap</option>
          </term>
          <listitem>
            <para>
              <!--
              Four values must be provided.  First a character, either
              'u', or 'g', to specify whether user or group ids are
              being mapped.  Next is the first userid as seen in the
              user namespace of the container.  Next is the userid as
              seen on the host.  Finally, a range indicating the number
              of consecutive ids to map.
              -->
              4 つの値を記述する必要があります。
              最初の文字は 'u' か 'g' のどちらかで、ユーザかグループの ID のどちらをマッピングするかを指定します。
              次はコンテナのユーザ名前空間内に現れる最初のユーザ ID です。
              その次は、そのユーザ ID のホスト上での値です。
              最後は、ID のマッピングをいくつ連続して行うかの数を指定します。
             </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Container hooks -->コンテナのフック</title>
      <para>
        <!--
        Container hooks are programs or scripts which can be executed
        at various times in a container's lifetime.
          -->
        コンテナのフックは、コンテナの存続期間の色々な場面で実行することのできるプログラムやスクリプトです。
      </para>
      <para>
        <!--
        When a container hook is executed, additional information is passed
        along. The <option>lxc.hook.version</option> argument can be used to
        determine if the following arguments are passed as command line
        arguments or through environment variables. The arguments are:
          -->
        コンテナフックが実行されるとき、追加の情報が渡されます。追加の引数がコマンドライン引数で渡されるか、環境変数経由で渡されるかを判断するのに、<option>lxc.hook.version</option> が使えます。引数は:
        <itemizedlist>
          <listitem><para> コンテナ名 <!-- Container name.--></para></listitem>
          <listitem><para> セクション (常に 'lxc') <!-- Section (always 'lxc'). --></para></listitem>
          <listitem><para> フックのタイプ ('clone' や 'pre-mount' など) <!-- The hook type (i.e. 'clone' or 'pre-mount'). --></para></listitem>
          <listitem><para> 追加の引数。clone フックの場合、lxc-clone に渡される追加の引数は、フックへの引数として追加されます。stop フックの場合は、コンテナの名前空間のそれぞれに対するファイルディスクリプタへのパスが、名前空間名とともに渡されます。 <!-- Additional arguments. In the
          case of the clone hook, any extra arguments passed to
          lxc-clone will appear as further arguments to the hook.
          In the case of the stop hook, paths to filedescriptors
          for each of the container's namespaces along with their types
          are passed. --></para></listitem>
        </itemizedlist>
        <!--
        The following environment variables are set:
          -->
        次の環境変数がセットされます。
        <itemizedlist>
          <listitem><para> LXC_CGNS_AWARE: コンテナで cgroup namespace が使えるかどうか <!-- indicator whether the container is
          cgroup namespace aware. --></para></listitem>
          <listitem><para> LXC_CONFIG_FILE: コンテナの設定ファイルのパス <!-- the path to the container
          configuration file. --></para></listitem>
          <listitem><para> LXC_HOOK_TYPE: フックのタイプ (例えば 'clone'、'mount'、'pre-mount')。この環境変数が存在するかどうかは <option>lxc.hook.version</option> の値次第です。この値が 1 なら、LXC_HOOK_TYPE が設定されています。
              <!-- the hook type (e.g. 'clone', 'mount',
          'pre-mount'). Note that the existence of this environment variable is
          conditional on the value of <option>lxc.hook.version</option>. If it
          is set to 1 then LXC_HOOK_TYPE will be set. -->
          </para></listitem>
          <listitem><para> LXC_HOOK_SECTION: セクションタイプ (例えば 'lxc'、'net')。この環境変数が存在するかどうかは <option>lxc.hook.version</option> の値次第です。この値が 1 なら、LXC_HOOK_TYPE が設定されています。
              <!-- the section type (e.g. 'lxc',
          'net'). Note that the existence of this environment variable is
          conditional on the value of <option>lxc.hook.version</option>. If it
          is set to 1 then LXC_HOOK_SECTION will be set. -->
          </para></listitem>
          <listitem><para> LXC_HOOK_VERSION: フックのバージョン。この値は、コンテナの <option>lxc.hook.version</option> の値と同じです。もし、この値が 0 に設定されているなら、古いスタイルのフックが使われます。もし 1 に設定されているなら、新しいスタイルのフックが使われます。
              <!-- the version of the hooks. This
          value is identical to the value of the container's
          <option>lxc.hook.version</option> config item. If it is set to 0 then
          old-style hooks are used. If it is set to 1 then new-style hooks are
          used. --></para></listitem>
          <listitem><para> LXC_LOG_LEVEL: コンテナのログレベル <!-- the container's log level. --></para></listitem>
          <listitem><para> LXC_NAME: コンテナ名 <!-- is the container's name. --></para></listitem>
          <listitem><para> LXC_[NAMESPACE IDENTIFIER]_NS: コンテナの名前空間が参照する /proc/PID/fd/ 以下のファイルディスクリプタのパス。それぞれの名前空間ごとに別々の環境変数になります。これらの環境変数は <option>lxc.hook.version</option> が 1 に設定されてる場合のみ設定されます。
              <!-- path under
          /proc/PID/fd/ to a file descriptor referring to the container's
          namespace. For each preserved namespace type there will be a separate
          environment variable. These environment variables will only be set if
          <option>lxc.hook.version</option> is set to 1. --></para></listitem>
          <listitem><para> LXC_ROOTFS_MOUNT: マウントされた root ファイルシステムへのパス <!-- the path to the mounted root filesystem. --></para></listitem>
          <listitem><para> LXC_ROOTFS_PATH: コンテナの lxc.rootfs.path エントリ。これはマウントされた rootfs が存在する場所にはならないでしょう。それには LXC_ROOTFS_MOUNT を使用してください。 <!-- this is the lxc.rootfs.path entry
          for the container. Note this is likely not where the mounted rootfs is
          to be found, use LXC_ROOTFS_MOUNT for that. --></para></listitem>
          <listitem><para> LXC_SRC_NAME: clone フックの場合、元のコンテナの名前 <!-- in the case of the clone hook, this is
          the original container's name. --></para></listitem>
        </itemizedlist>
      </para>
      <para>
        <!--
        Standard output from the hooks is logged at debug level.
        Standard error is not logged, but can be captured by the
        hook redirecting its standard error to standard output.
          -->
        スクリプトからの標準出力は debug レベルでロギングされます。
        標準エラー出力はロギングされません。
        しかし、フックの標準エラー出力を標準出力にリダイレクトすることにより保存することは可能です。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.version</option>
          </term>
          <listitem>
            <para>
              <!--
            To pass the arguments in new style via environment variables set to
            1 otherwise set to 0 to pass them as arguments.
            This setting affects all hooks arguments that were traditionally
            passed as arguments to the script. Specifically, it affects the
            container name, section (e.g. 'lxc', 'net') and hook type (e.g.
            'clone', 'mount', 'pre-mount') arguments. If new-style hooks are
            used then the arguments will be available as environment variables.
            The container name will be set in LXC_NAME. (This is set
            independently of the value used for this config item.) The section
            will be set in LXC_HOOK_SECTION and the hook type will be set in
            LXC_HOOK_TYPE.
            It also affects how the paths to file descriptors referring to the
            container's namespaces are passed. If set to 1 then for each
            namespace a separate environment variable LXC_[NAMESPACE
            IDENTIFIER]_NS will be set. If set to 0 then the paths will be
            passed as arguments to the stop hook.
                -->
              環境変数経由の新しいスタイルで引数を渡すには 1 に設定します。そうでなく、引数として渡すには 0 に設定します。この設定は、古い方法でスクリプトに引数として渡されているすべてのフック引数に影響します。特に、コンテナ名のセクション (例: 'lxc', 'net') とフックタイプ (例: 'clone', 'mount', 'pre-mount') 引数に影響します。新しいスタイルのフックが使われる場合、引数は環境変数として利用できます。
              コンテナ名は LXC_NAME に設定されます(これはこの設定項目に設定されている値とは関係なく設定されます)。セクションは LXC_HOOK_SECTION に設定されます。そしてフックタイプは LXC_HOOK_TYPE に設定されます。
              この設定は、コンテナの名前空間を参照するファイルディスクリプタのパスをどのように渡すかにも影響します。1 に設定した場合、名前空間ごとに別の環境変数 LXC_[NAMESPACE IDENTIFIER]_NS に設定されます。0 に設定すると、パスは stop フックの引数として渡されます。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.pre-start</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run in the host's namespace before the
              container ttys, consoles, or mounts are up.
              -->
              コンテナの tty、コンソールの作成、マウントが実行される前に、ホストの名前空間内で実行するフック。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.pre-mount</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run in the container's fs namespace but before
              the rootfs has been set up.  This allows for manipulation
              of the rootfs, i.e. to mount an encrypted filesystem.  Mounts
              done in this hook will not be reflected on the host (apart from
              mounts propagation), so they will be automatically cleaned up
              when the container shuts down.
              -->
              コンテナのファイルシステムの名前空間で実行されますが、rootfs が設定される前に実行するフック。
              これにより rootfs の操作が可能になります。
              例えば、暗号化されたファイルシステムのマウントなどです。
              このフック内でなされるマウントはホストには影響しません (mounts propagation を除いて)。
              なので、それらはコンテナがシャットダウンする時に自動的にクリーンアップされます。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.mount</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run in the container's namespace after
              mounting has been done, but before the pivot_root.
              -->
              マウントが完了した後ですが、pivot_root の前にコンテナの名前空間で実行されるフック。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.autodev</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run in the container's namespace after
              mounting has been done and after any mount hooks have
              run, but before the pivot_root, if
              <option>lxc.autodev</option> == 1.
              The purpose of this hook is to assist in populating the
              /dev directory of the container when using the autodev
              option for systemd based containers.  The container's /dev
              directory is relative to the
              ${<option>LXC_ROOTFS_MOUNT</option>} environment
              variable available when the hook is run.
              -->
              <option>lxc.autodev</option> == 1 が設定されている場合で、マウントが完了し、マウント時のフックも実行された後ですが、pivot_root の前にコンテナの名前空間で実行するフック。
              このフックの目的は、systemd ベースのコンテナ向けの autodev オプションが設定されている時に、コンテナの /dev ディレクトリを設定するのを支援することです。コンテナの /dev ディレクトリは、このフックが実行される時有効な ${<option>LXC_ROOTFS_MOUNT</option>} 環境変数からの相対パスとなります。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.start-host</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run in the host's namespace after the
              container has been setup, and immediately before starting
              the container init.
              -->
              コンテナのセットアップが済んだあと、コンテナの init を実行する直前に、ホストの名前空間で実行するためのフックです。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.start</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run in the container's namespace immediately
              before executing the container's init.  This requires the
              program to be available in the container.
              -->
              コンテナの init が実行される直前にコンテナの名前空間で実行されるフック。
              コンテナ内で利用可能なプログラムである必要があります。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.stop</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run in the host's namespace with references
              to the container's namespaces after the container has been shut
              down. For each namespace an extra argument is passed to the hook
              containing the namespace's type and a filename that can be used to
              obtain a file descriptor to the corresponding namespace, separated
              by a colon. The type is the name as it would appear in the
              <filename>/proc/PID/ns</filename> directory.
              For instance for the mount namespace the argument usually looks
              like <filename>mnt:/proc/PID/fd/12</filename>.
              -->
              コンテナのシャットダウン後、コンテナの名前空間への参照とともに、ホストの名前空間で実行されるフックです。
              それぞれの名前空間に対応する追加の引数がフックに渡されます。その引数にはコロンで区切られた名前空間のタイプ名とファイル名が含まれており、ファイル名は名前空間に対するファイルディスクリプタを取得するのに使えます。
              タイプ名は <filename>/proc/PID/ns</filename> ディレクトリ内のファイル名です。
              例えば、マウント名前空間に対応する引数は通常は <filename>mnt:/proc/PID/fd/12</filename> のようになります。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.post-stop</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run in the host's namespace after the
              container has been shut down.
              -->
              コンテナがシャットダウンされた後にホストの名前空間で実行するフック。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.clone</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run when the container is cloned to a new one.
              See <citerefentry><refentrytitle><command>lxc-clone</command></refentrytitle>
              <manvolnum>1</manvolnum></citerefentry> for more information.
              -->
              コンテナが新しいコンテナにクローンされる際に実行されるフック。詳しくは
              <citerefentry><refentrytitle><command>lxc-clone</command></refentrytitle>
              <manvolnum>1</manvolnum></citerefentry>
              を参照してください。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.hook.destroy</option>
          </term>
          <listitem>
            <para>
              <!--
              A hook to be run when the container is destroyed.
                -->
              コンテナを破壊する際に実行されるフックです。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
      <title><!-- Container hooks Environment Variables -->コンテナのフックで使える環境変数</title>
      <para>
        <!--
        A number of environment variables are made available to the startup
        hooks to provide configuration information and assist in the
        functioning of the hooks.  Not all variables are valid in all
        contexts.  In particular, all paths are relative to the host system
        and, as such, not valid during the <option>lxc.hook.start</option> hook.
        -->
        起動時のフックに設定情報を提供し、フックの機能を助けるための環境変数がいくつか利用可能です。
        全ての変数が全てのコンテキストで利用可能なわけではありません。
        具体的には、全てのパスはホストシステム上のパスであり、そのため、<option>lxc.hook.start</option> フックの時点では使用できません。
      </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_NAME</option>
          </term>
          <listitem>
            <para>
              <!--
              The LXC name of the container.  Useful for logging messages
              in common log environments.  [<option>-n</option>]
              -->
              LXC コンテナの名前。共通のログ環境内でのログメッセージに使うときに便利です。[<option>-n</option>]
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_CONFIG_FILE</option>
          </term>
          <listitem>
            <para>
              <!--
              Host relative path to the container configuration file.  This
              gives the container to reference the original, top level,
              configuration file for the container in order to locate any
              additional configuration information not otherwise made
              available.  [<option>-f</option>]
              -->
              コンテナの設定ファイルのホスト上でのパス。
              これは、他の方法では得られない追加の設定情報を見つけるために、コンテナに、元の、トップレベルの設定ファイルの位置を与えるものです。 [<option>-f</option>]
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_CONSOLE</option>
          </term>
          <listitem>
            <para>
              <!--
              The path to the console output of the container if not NULL.
              [<option>-c</option>] [<option>lxc.console.path</option>]
              -->
              設定されている場合のコンテナのコンソール出力のパス。
              [<option>-c</option>] [<option>lxc.console.path</option>]
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_CONSOLE_LOGPATH</option>
          </term>
          <listitem>
            <para>
              <!--
              The path to the console log output of the container if not NULL.
              [<option>-L</option>]
              -->
              設定されている場合のコンテナのコンソールログ出力のパス。
              [<option>-L</option>]
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_ROOTFS_MOUNT</option>
          </term>
          <listitem>
            <para>
              <!--
              The mount location to which the container is initially bound.
              This will be the host relative path to the container rootfs
              for the container instance being started and is where changes
              should be made for that instance.
              [<option>lxc.rootfs.mount</option>]
              -->
              初期にコンテナがマウントされる場所。
              これは、コンテナインスタンスが起動するためのコンテナの rootfs へのホスト上のパスであり、インスタンスのための移行が行われる場所です。
              [<option>lxc.rootfs.mount</option>]
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_ROOTFS_PATH</option>
          </term>
          <listitem>
            <para>
              <!--
              The host relative path to the container root which has been
              mounted to the rootfs.mount location.
              [<option>lxc.rootfs.path</option>]
              -->
              rootfs.mount へマウントされるコンテナのルートへのホスト上のパスです。
              [<option>lxc.rootfs.path</option>]
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_SRC_NAME</option>
          </term>
          <listitem>
            <para>
              <!--
              Only for the clone hook. Is set to the original container name.
              -->
              clone フックの場合のみ使われます。クローン元のコンテナ名が設定されます。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_TARGET</option>
          </term>
          <listitem>
            <para>
              <!--
              Only for the stop hook. Is set to "stop" for a container
              shutdown or "reboot" for a container reboot.
              -->
              stop フックの場合のみ使われます。コンテナのシャットダウンの場合は "stop"、リブートの場合は "reboot" が設定されます。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
      <variablelist>
        <varlistentry>
          <term>
            <option>LXC_CGNS_AWARE</option>
          </term>
          <listitem>
            <para>
         <!--
         If unset, then this version of lxc is not aware of cgroup
         namespaces.  If set, it will be set to 1, and lxc is aware
         of cgroup namespaces.  Note this does not guarantee that
         cgroup namespaces are enabled in the kernel.  This is used
         by the lxcfs mount hook.
         -->
              この変数が設定されていない場合、お使いのバージョンの LXC は cgroup 名前空間を扱えません。設定されている場合、この値は 1 に設定されています。そして、cgroup 名前空間を扱えます。
              この変数はカーネルで cgroup 名前空間が有効であることは保証しません。この変数は lxcfs のマウントフックが使います。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
    <title><!-- Logging -->ロギング</title>
    <para>
      <!--
      Logging can be configured on a per-container basis.  By default,
      depending upon how the lxc package was compiled, container startup
      is logged only at the ERROR level, and logged to a file named after
      the container (with '.log' appended) either under the container path,
      or under @LOGPATH@.
      -->
      ロギングはコンテナごとに設定することが可能です。
      デフォルトでは、lxc パッケージのコンパイル条件に依存し、コンテナのスタートアップは ERROR レベルでのみロギングされ、コンテナのパス以下か、@LOGPATH@ 以下のどちらかにコンテナ名 (の後に '.log' が付与される) をもとにした名前でロギングされます。
    </para>
    <para>
      <!--
      Both the default log level and the log file can be specified in the
      container configuration file, overriding the default behavior.  Note
      that the configuration file entries can in turn be overridden by the
      command line options to <command>lxc-start</command>.
      -->
      デフォルトのログレベルとログファイルは両方とも、コンテナの設定ファイル内で指定され、デフォルトの値を上書きします。
      同様に、設定ファイルのエントリは <command>lxc-start</command> のコマンドラインオプションで上書きすることも可能です。
    </para>
      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.log.level</option>
          </term>
          <listitem>
            <para>
              <!--
            The level at which to log.  The log level is an integer in
            the range of 0..8 inclusive, where a lower number means more
            verbose debugging.  In particular 0 = trace, 1 = debug, 2 =
            info, 3 = notice, 4 = warn, 5 = error, 6 = critical, 7 =
            alert, and 8 = fatal.  If unspecified, the level defaults
            to 5 (error), so that only errors and above are logged.
            -->
              ログを取得するレベル。
              ログレベルは 0..8 の範囲の整数です。
              数字が小さいほど冗長なデバッグを意味します。
              具体的には、0 = trace, 1 = debug, 2 = info, 3 = notice, 4 = warn, 5 = error, 6 = critical, 7 = alert, and 8 = fatal です。
              指定されない場合、レベルのデフォルトは 5 (error) で、それ以上のエラーがロギングされます。
            </para>
            <para>
              <!--
            Note that when a script (such as either a hook script or a
            network interface up or down script) is called, the script's
            standard output is logged at level 1, debug.
            -->
              (フックスクリプトやネットワークインターフェースの起動、停止時のスクリプトのような) スクリプトが呼ばれた時、スクリプトの標準出力は level 1 の debug でロギングされます。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.log.file</option>
          </term>
          <listitem>
            <para>
              <!--
            The file to which logging info should be written.
            -->
              ログ情報を書き込むファイル。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.log.syslog</option>
          </term>
          <listitem>
            <para>
              <!--
            Send logging info to syslog. It respects the log level defined in
            <command>lxc.log.level</command>. The argument should be the syslog
            facility to use, valid ones are: daemon, local0, local1, local2,
            local3, local4, local5, local5, local7.
            -->
              ログ情報を syslog に送ります。ログレベルとして <command>lxc.log.level</command> の値を使用します。指定する値は使用する syslog の facility です。有効な値は daemon, local0, local1, local2, local3, local4, local5, local5, local6, local7 のいずれかです。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
    <title><!-- Autostart -->自動起動</title>
    <para>
      <!--
        The autostart options support marking which containers should be
        auto-started and in what order. These options may be used by LXC tools
        directly or by external tooling provided by the distributions.
        -->
      自動起動オプションでは、自動起動させるコンテナと順番の設定が可能です。
      このオプションは LXC ツールが直接使用するか、ディストリビューションが提供する外部ツールが使用するかもしれません。
    </para>

    <variablelist>
        <varlistentry>
          <term>
            <option>lxc.start.auto</option>
          </term>
          <listitem>
            <para>
              <!--
              Whether the container should be auto-started.
              Valid values are 0 (off) and 1 (on).
              -->
              コンテナを自動起動させるかどうかを設定します。
              有効な値は 0(オフ) か 1(オン) です。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.start.delay</option>
          </term>
          <listitem>
            <para>
              <!--
              How long to wait (in seconds) after the container is
              started before starting the next one.
              -->
              コンテナを起動させた後、次のコンテナを起動させるまでにどれくらい (秒) 待つかを設定します。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.start.order</option>
          </term>
          <listitem>
            <para>
              <!--
              An integer used to sort the containers when auto-starting
              a series of containers at once. A lower value means an
              earlier start.
              -->
              自動起動させるコンテナが多数ある場合のコンテナの起動順を決めるのに使う整数を指定します。
              小さい値ほど早く起動します。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.monitor.unshare</option>
          </term>
          <listitem>
            <para>
              <!--
              If not zero the mount namespace will be unshared from the host
              before initializing the container (before running any pre-start
              hooks). This requires the CAP_SYS_ADMIN capability at startup.
              Default is 0.
                -->
              この値が 0 でない場合、コンテナが初期化される前 (pre-start フックが実行される前) にマウント名前空間がホストから unshare されます。この機能を使う場合、スタート時に CAP_SYS_ADMIN ケーパビリティが必要です。デフォルト値は 0 です。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.monitor.signal.pdeath</option>
          </term>
          <listitem>
            <para>
	      <!--
              Set the signal to be sent to the container's init when the lxc
              monitor exits. By default it is set to SIGKILL which will cause
              all container processes to be killed when the lxc monitor process
              dies.
              To ensure that containers stay alive even if lxc monitor dies set
              this to 0.
		-->
	      lxc のモニタプロセスが終了した際に、コンテナの init プロセスに送出するシグナルを指定します。デフォルトでは、lxc のモニタプロセスが終了した場合には、すべてのコンテナ内のプロセスが停止するように SIGKILL が設定されています。
	      lxc のモニタプロセスが終了しても、コンテナがすべて確実に動作しつづけるようにするには、この値を 0 に設定します。
            </para>
          </listitem>
        </varlistentry>
        <varlistentry>
          <term>
            <option>lxc.group</option>
          </term>
          <listitem>
            <para>
              <!--
              A multi-value key (can be used multiple times) to put the
              container in a container group. Those groups can then be
              used (amongst other things) to start a series of related
              containers.
              -->
              コンテナを追加したいコンテナグループ名を指定します。
              複数の値を設定でき、複数回指定することもできます。
              設定されたグループは、関連する一連のコンテナを起動させるために使われます。
            </para>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

    <refsect2>
    <title><!-- Autostart and System Boot -->自動起動とシステムブート</title>
    <para>
      <!--
          Each container can be part of any number of groups or no group at all.
          Two groups are special. One is the NULL group, i.e. the container does
          not belong to any group. The other group is the "onboot" group.
        -->
          コンテナはいくつでもグループに属することができ、全く属さないことも可能です。特別なグループが 2 つ存在します。1 つは NULL グループです。これはどのグループにも属さないコンテナです。もう 1 つは "onboot" グループです。
    </para>

    <para>
      <!--
          When the system boots with the LXC service enabled, it will first
          attempt to boot any containers with lxc.start.auto == 1 that is a member
          of the "onboot" group. The startup will be in order of lxc.start.order.
          If an lxc.start.delay has been specified, that delay will be honored
          before attempting to start the next container to give the current
          container time to begin initialization and reduce overloading the host
          system. After starting the members of the "onboot" group, the LXC system
          will proceed to boot containers with lxc.start.auto == 1 which are not
          members of any group (the NULL group) and proceed as with the onboot
          group.
          -->
          LXC サービスが有効になった状態でシステムがブートすると、最初に "onboot" グループのメンバーである lxc.start.auto == 1 が設定されたコンテナを起動しようとします。起動は lxc.start.order の順に起動します。
          lxc.start.delay が指定されている場合、現在対象となっているコンテナに初期化の時間を与え、ホストシステムの負荷を低減するために、次のコンテナを開始させるまでに遅延時間を与えます。
          "onboot" グループのメンバーが開始した後、LXC システムは lxc.start.auto == 1 が設定された、どのグループのメンバーでもない (NULL グループの) コンテナのブートを onboot グループのコンテナと同様に開始します。
    </para>

    </refsect2>

    <refsect2>
      <title><!-- Container Environment -->コンテナの環境変数</title>
      <para>
        <!--
        If you want to pass environment variables into the container (that
        is, environment variables which will be available to init and all of
        its descendents), you can use <command>lxc.environment</command>
        parameters to do so.  Be careful that you do not pass in anything
        sensitive; any process in the container which doesn't have its
        environment scrubbed will have these variables available to it, and
        environment variables are always available via
        <command>/proc/PID/environ</command>.
        -->
        コンテナに環境変数を渡したい場合 (環境変数はコンテナの init とその子孫全てで利用可能です)、<command>lxc.environment</command> パラメータがその用途に使えます。
        機微 (センシティブ) な情報を渡さないように注意が必要です。そのような情報を持たないコンテナ内のプロセスでこれらの環境変数が利用可能になってしまいます。環境変数は常に <command>/proc/PID/environ</command> 経由で利用可能になります。
      </para>

      <para>
        <!--
        This configuration parameter can be specified multiple times; once
        for each environment variable you wish to configure.
        -->
        この設定項目は、設定したい環境変数ごとに 1 度ずつ、何度でも指定できます。
      </para>

      <variablelist>
        <varlistentry>
          <term>
            <option>lxc.environment</option>
          </term>
          <listitem>
            <para>
              <!--
              Specify an environment variable to pass into the container.
              Example:
              -->
              コンテナに渡したい環境変数を指定します。
              例:
            </para>
            <programlisting>
              lxc.environment = APP_ENV=production
              lxc.environment = SYSLOG_SERVER=**********
            </programlisting>
          </listitem>
        </varlistentry>
      </variablelist>
    </refsect2>

  </refsect1>

  <refsect1>
    <title><!-- Examples -->例</title>
      <para>
        <!--
        In addition to the few examples given below, you will find
        some other examples of configuration file in @DOCDIR@/examples
        -->
        以下に紹介するいくつかの例に加えて、他の設定例が @DOCDIR@/examples にあります。
      </para>
    <refsect2>
      <title><!-- Network -->ネットワーク</title>
      <para>
        <!--
        This configuration sets up a container to use a veth pair
        device with one side plugged to a bridge br0 (which has been
        configured before on the system by the administrator). The
        virtual network device visible in the container is renamed to
        eth0.
        -->
        この設定は、片方をブリッジである br0 と接続される veth ペアデバイスを使うコンテナを設定します (ブリッジは管理者によりあらかじめシステム上に設定済みである必要があります)。
        仮想ネットワークデバイスは、コンテナ内では eth0 とリネームされます。
      </para>
      <programlisting>
        lxc.uts.name = myhostname
        lxc.net.0.type = veth
        lxc.net.0.flags = up
        lxc.net.0.link = br0
        lxc.net.0.name = eth0
        lxc.net.0.hwaddr = 4a:49:43:49:79:bf
        lxc.net.0.ipv4.address = *******/24 *********
        lxc.net.0.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3597
      </programlisting>
    </refsect2>

    <refsect2>
      <title><!-- UID/GID mapping -->UID/GID のマッピング</title>
      <para><!-- This configuration will map both user and group ids in the
        range 0-9999 in the container to the ids 100000-109999 on the host. -->
        この設定は、コンテナ内のユーザとグループ両方の id 0-9999 の範囲を、ホスト上の 100000-109999 へマッピングします。
      </para>
      <programlisting>
        lxc.idmap = u 0 100000 10000
        lxc.idmap = g 0 100000 10000
      </programlisting>
    </refsect2>

    <refsect2>
      <title>Control group</title>
      <para>
        <!-- This configuration will setup several control groups for
      the application, cpuset.cpus restricts usage of the defined cpu,
      cpus.share prioritize the control group, devices.allow makes
      usable the specified devices.-->
        この設定は、アプリケーションのための control group をいくつか設定します。
        cpuset.cpus は定義された cpu のみ使用できるように制限します。
        cpus.share は、control group の (cpu) 優先度を指定します。
        devices.allow は、特定のデバイスを使用可能にします。
      </para>
      <programlisting>
        lxc.cgroup.cpuset.cpus = 0,1
        lxc.cgroup.cpu.shares = 1234
        lxc.cgroup.devices.deny = a
        lxc.cgroup.devices.allow = c 1:3 rw
        lxc.cgroup.devices.allow = b 8:0 rw
      </programlisting>
    </refsect2>

    <refsect2>
      <title><!-- Complex configuration -->複雑な設定</title>
      <para>
        <!-- This example show a complex configuration making a complex
      network stack, using the control groups, setting a new hostname,
      mounting some locations and a changing root file system. -->
        この例は、control group を使って、複雑なネットワークスタックを作成し、新しいホスト名を指定し、いくつかの場所をマウントし、ルートファイルシステムを変更するような複雑な設定を示します。
      </para>
      <programlisting>
        lxc.uts.name = complex
        lxc.net.0.type = veth
        lxc.net.0.flags = up
        lxc.net.0.link = br0
        lxc.net.0.hwaddr = 4a:49:43:49:79:bf
        lxc.net.0.ipv4.address = ********/24 **********
        lxc.net.0.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3597
        lxc.net.0.ipv6.address = 2003:db8:1:0:214:5432:feab:3588
        lxc.net.1.type = macvlan
        lxc.net.1.flags = up
        lxc.net.1.link = eth0
        lxc.net.1.hwaddr = 4a:49:43:49:79:bd
        lxc.net.1.ipv4.address = ********/24
        lxc.net.1.ipv4.address = **************/24
        lxc.net.1.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3596
        lxc.net.2.type = phys
        lxc.net.2.flags = up
        lxc.net.2.link = random0
        lxc.net.2.hwaddr = 4a:49:43:49:79:ff
        lxc.net.2.ipv4.address = ********/24
        lxc.net.2.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3297
        lxc.cgroup.cpuset.cpus = 0,1
        lxc.cgroup.cpu.shares = 1234
        lxc.cgroup.devices.deny = a
        lxc.cgroup.devices.allow = c 1:3 rw
        lxc.cgroup.devices.allow = b 8:0 rw
        lxc.mount.fstab = /etc/fstab.complex
        lxc.mount.entry = /lib /root/myrootfs/lib none ro,bind 0 0
        lxc.rootfs.path = dir:/mnt/rootfs.complex
        lxc.rootfs.options = idmap=container
        lxc.cap.drop = sys_module mknod setuid net_raw
        lxc.cap.drop = mac_override
      </programlisting>
    </refsect2>

  </refsect1>

  <refsect1>
    <title>See Also</title>
    <simpara>
      <citerefentry>
        <refentrytitle><command>chroot</command></refentrytitle>
        <manvolnum>1</manvolnum>
      </citerefentry>,

      <citerefentry>
        <refentrytitle><command>pivot_root</command></refentrytitle>
        <manvolnum>8</manvolnum>
      </citerefentry>,

      <citerefentry>
        <refentrytitle><filename>fstab</filename></refentrytitle>
        <manvolnum>5</manvolnum>
      </citerefentry>

      <citerefentry>
        <refentrytitle><filename>capabilities</filename></refentrytitle>
        <manvolnum>7</manvolnum>
      </citerefentry>

    </simpara>
  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
