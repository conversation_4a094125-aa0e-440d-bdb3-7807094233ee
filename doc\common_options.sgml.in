<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<refsect1>
  <title>Common Options</title>

  <para>
    These options are common to most of lxc commands.
  </para>

  <variablelist>
    <varlistentry>
      <term><option>-?, -h, --help</option></term>
      <listitem>
	<para>
	  Print a longer usage message than normal.
	</para>
      </listitem>
    </varlistentry>
    <varlistentry>
      <term><option>--usage</option></term>
      <listitem>
	<para>
	  Give the usage message
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-q, --quiet</option></term>
      <listitem>
	<para>
	  mute on
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-P, --lxcpath=<replaceable>PATH</replaceable></option></term>
      <listitem>
	<para>
	  Use an alternate container path.  The default is @LXCPATH@.
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-o, --logfile=<replaceable>FILE</replaceable></option></term>
      <listitem>
	<para>
	  Output to an alternate log
	  <replaceable>FILE</replaceable>. The default is no log.
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-l, --logpriority=<replaceable>LEVEL</replaceable></option></term>
      <listitem>
	<para>
	  Set log priority to
	  <replaceable>LEVEL</replaceable>. The default log
	  priority is <literal>ERROR</literal>. Possible values are :
	  <literal>FATAL</literal>, <literal>ALERT</literal>,
	  <literal>CRIT</literal>,
	  <literal>WARN</literal>, <literal>ERROR</literal>,
	  <literal>NOTICE</literal>, <literal>INFO</literal>,
	  <literal>DEBUG</literal>, <literal>TRACE</literal>.
	</para>
	<para>
	Note that this option is setting the priority of the events
	log in the alternate log file. It do not have effect on the
	ERROR events log on stderr.
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>-n, --name=<replaceable>NAME</replaceable></option></term>
      <listitem>
	<para>
	  Use container identifier <replaceable>NAME</replaceable>.
	  The container identifier format is an alphanumeric string.
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>--rcfile=<replaceable>FILE</replaceable></option></term>
      <listitem>
	<para>
	  Specify the configuration file to configure the virtualization
	  and isolation functionalities for the container.
	</para>
	<para>
	 This configuration file if present will be used even if there is
	 already a configuration file present in the previously created
	 container (via lxc-create).
	</para>
      </listitem>
    </varlistentry>

    <varlistentry>
      <term><option>--version</option></term>
      <listitem>
        <para>
          Show the version number.
        </para>
      </listitem>
    </varlistentry>
  </variablelist>

</refsect1>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
