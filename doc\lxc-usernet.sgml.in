<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-usernet</refentrytitle>
    <manvolnum>5</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-usernet</refname>

    <refpurpose>
      unprivileged user network administration file.
    </refpurpose>
  </refnamediv>

  <refsect1>
    <title>Description</title>

    <para>
      <filename>@LXC_USERNIC_CONF@</filename> controls the limits which the
      program <command>lxc-user-nic</command> places on network interfaces
      which an unprivileged user may create.
    </para>

    <refsect2>
      <title>Configuration</title>
      <para>
      This file consists of multiple entries, one per line, of the form:
      </para>

      <para>
      <command>user</command> <command>type</command> <command>bridge</command> <command>number</command>
      </para>
      <para>or</para>
      <para>
      <command>@group</command> <command>type</command> <command>bridge</command> <command>number</command>
      </para>
      <para>
      Where
      </para>

      <variablelist>

	<varlistentry>
	  <term>
	    <option>user</option>
	  </term>
	  <listitem>
	    <para>
	      is the username to whom this entry applies.
	     </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>@group</option>
	  </term>
	  <listitem>
	    <para>
	      is the groupname to which this entry applies.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>type</option>
	  </term>
	  <listitem>
	    <para>
	      is the type of network interface being allowed.  Only veth
	      is currently supported.
	     </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>bridge</option>
	  </term>
	  <listitem>
	    <para>
	      is the bridge to which the network interfaces may be attached, for
	      instance <filename>lxcbr0</filename>.
	     </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>number</option>
	  </term>
	  <listitem>
	    <para>
	      is the number or quota of network interfaces of the given type which the
	      given user or group may attach to the given bridge, for instance <filename>2</filename>.
	     </para>
	  </listitem>
	</varlistentry>
      </variablelist>

      <para>
        Since a user can be specified both by username as well as one or
        more usergroups, it is possible that several configuration lines
        enable that user to create network interfaces. In such cases, any
        interfaces create are counted towards the quotas of the user or group
        in the order in which they appear in the file. If the quota of one
        line is full, the rest will be parsed until one is found or the end of
        the file.
      </para>
    </refsect2>

  </refsect1>

  <refsect1>
    <title>See Also</title>
    <simpara>
      <citerefentry>
	<refentrytitle><command>lxc</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle><command>lxc-user-nic</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>
    </simpara>
  </refsect1>

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
