name: Fuzzing
on:
  push:
    branches:

permissions:
  contents: read

jobs:
  fuzzing:
    name: OSS-Fuzz
    runs-on: ubuntu-24.04
    if: github.repository == 'lxc/lxc'
    strategy:
      fail-fast: false
      matrix:
        sanitizer:
          - address
          - undefined
          - memory
    steps:
      - name: Build Fuzzers (${{ matrix.sanitizer }})
        id: build
        uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@master
        with:
          oss-fuzz-project-name: 'lxc'
          dry-run: false
          allowed-broken-targets-percentage: 0
          sanitizer: ${{ matrix.sanitizer }}

      - name: Run Fuzzers (${{ matrix.sanitizer }})
        uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@master
        with:
          oss-fuzz-project-name: 'lxc'
          fuzz-seconds: 360
          dry-run: false
          sanitizer: ${{ matrix.sanitizer }}

      - name: Upload Crash
        uses: actions/upload-artifact@v4
        if: failure() && steps.build.outcome == 'success'
        with:
          name: ${{ matrix.sanitizer }}-artifacts
          path: ./out/artifacts
