<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-snapshot</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-snapshot</refname>

    <refpurpose>
      <!--
      Snapshot an existing container.
      -->
      存在するコンテナのスナップショットの取得
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-snapshot</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="opt">-c, --comment <replaceable>file</replaceable></arg>
    </cmdsynopsis>
    <cmdsynopsis>
      <command>lxc-snapshot</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="req">-d, -destroy <replaceable>snapshot-name</replaceable></arg>
    </cmdsynopsis>
    <cmdsynopsis>
      <command>lxc-snapshot</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="req">-L, --list </arg>
      <arg choice="opt">-C, --showcomments </arg>
    </cmdsynopsis>
    <cmdsynopsis>
      <command>lxc-snapshot</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="req">-r, -restore <replaceable>snapshot-name</replaceable></arg>
      <arg choice="opt">-N, --newname <replaceable> newname</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      <command>lxc-snapshot</command> creates, lists, and restores
      container snapshots.
      -->
      <command>lxc-snapshot</command> はコンテナのスナップショットの作製、スナップショットのリスト表示、スナップショットからのリストアを行います。
    </para>
    <para>
      <!--
      Snapshots are stored as snapshotted containers under the container's configuration path. For instance, if the container's configuration path is <filename>/var/lib/lxc</filename> and the container is <filename>c1</filename>, then the first snapshot will be stored as container <filename>snap0</filename> under the path <filename>/var/lib/lxc/c1/snaps</filename>.
      If <filename>/var/lib/lxcsnaps</filename>, as used by LXC 1.0, already exists, then it will continue to be used.
        -->
      スナップショットはコンテナパス以下にスナップショット化されたコンテナとして保存されます。
      例えば、もしコンテナパスが <filename>/var/lib/lxc</filename> で、コンテナが <filename>c1</filename> である場合、最初に取得するスナップショットは、パス <filename>/var/lib/lxc/c1/snaps</filename> の下の <filename>snap0</filename> として保存されます。
      LXC 1.0 で使われていた <filename>/var/lib/lxcsnaps</filename> が存在する場合には、このディレクトリが引き続き使われます。
    </para>
  </refsect1>

  <refsect1>

    <title><!-- Options -->オプション</title>

    <variablelist>

	  <varlistentry>
	    <term> <option>-c,--comment <replaceable>comment_file</replaceable></option> </term>
	   <listitem>
	    <para>
              <!--
              Associate the comment in <replaceable>comment_file</replaceable> with the newly created snapshot.
              -->
              新しく作製するスナップショットに <replaceable>comment_file</replaceable> ファイル内のコメントを関連付ける。
            </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-d,--destroy snapshot-name</option> </term>
	   <listitem>
             <!--
	    <para> Destroy the named snapshot.  If the named snapshot is ALL, then all snapshots
	    will be destroyed.</para>
            -->
             指定した名前のスナップショットを破壊します。ALL という名前が指定された場合、すべてのスナップショットが破壊されます。
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-L,--list </option> </term>
	   <listitem>
	    <para>
              <!--
              List existing snapshots.
              -->
              存在するスナップショットをリスト表示します。
            </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-C,--showcomments </option> </term>
	   <listitem>
	    <para>
              <!--
              Show snapshot comments in the snapshots listings.
              -->
              スナップショットのリスト表示でスナップショットに対するコメントを表示します。
            </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-r,--restore snapshot-name</option> </term>
	   <listitem>
	    <para>
              <!--
              Restore the named snapshot, meaning a full new container is created which is a copy of the snapshot.
              -->
              指定のスナップショットをリストアします。
              これはスナップショットのコピーである完全に新しいコンテナが作製されるということです。
            </para>
	   </listitem>
	  </varlistentry>

	  <varlistentry>
	    <term> <option>-N, --newname</option> </term>
	   <listitem>
            <para>
	      <!-- When restoring a snapshot, the last optional argument if not given explicitly Via <command>\-\-newname</command> is the name to use for the restored container.  If the newname is identical to the original name of the container, then the original container will be destroyed and the restored container will take its place. Note that deleting the original snapshot is not possible in the case of overlayfs or zfs backed snapshots. -->
	      スナップショットをリストアする際、<command>--newname</command> の値として明示的に指定しない場合でも、最後のオプション引数はリストアするコンテナの名前として使用します。
	      もし newname が元のコンテナの名前と同じ場合、元のコンテナが削除され、リストアされるコンテナに置き換えられます。
	      スナップショット元を削除することは、overlayfs, zfs がバックエンドのスナップショットではできないことに注意が必要です。
	    </para>
	   </listitem>
	  </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
