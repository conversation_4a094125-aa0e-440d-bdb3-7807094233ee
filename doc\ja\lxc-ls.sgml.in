<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-ls</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-ls</refname>

    <refpurpose>
      <!--
      list the containers existing on the system
      -->
      システム上に存在するコンテナのリスト表示
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-ls</command>
      <arg choice="opt">-1</arg>
      <arg choice="opt">--active</arg>
      <arg choice="opt">--frozen</arg>
      <arg choice="opt">--running</arg>
      <arg choice="opt">--stopped</arg>
      <arg choice="opt">--defined</arg>
      <arg choice="opt">-f</arg>
      <arg choice="opt">-F <replaceable>format</replaceable></arg>
      <arg choice="opt">-g <replaceable>groups</replaceable></arg>
      <arg choice="opt">--nesting=<replaceable>NUM</replaceable></arg>
      <arg choice="opt">--filter=<replaceable>regex</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>
    <para>
      <!--
      <command>lxc-ls</command> list the containers existing on the
      system.
      -->
      <command>lxc-ls</command> はシステム上に存在するコンテナをリスト表示します。
    </para>
  </refsect1>

  <refsect1>
    <title><!-- Options -->オプション</title>
    <variablelist>
      <varlistentry>
        <term>
          <option>-1</option>
        </term>
        <listitem>
          <para>
            <!--
            Show one entry per line. (default when /dev/stdout isn't a tty)
            -->
            1 行に 1 エントリ表示します。(/dev/stdout が tty でない場合のデフォルト)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--active</option>
	</term>
	<listitem>
	  <para>
            <!--
            List only active containers (same as &#045;&#045;frozen &#045;&#045;running).
            -->
            アクティブなコンテナのみリスト表示します。(--frozen --running と同じです)
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--frozen</option>
        </term>
        <listitem>
          <para>
            <!--
            List only frozen containers.
            -->
            凍結 (frozen) 状態のコンテナのみをリスト表示します。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--running</option>
        </term>
        <listitem>
          <para>
            <!--
            List only running containers.
            -->
            実行 (running) 状態のコンテナのみをリスト表示します。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--stopped</option>
        </term>
        <listitem>
          <para>
            <!--
            List only stopped containers.
            -->
            停止状態のコンテナのみをリスト表示します。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--defined</option>
        </term>
        <listitem>
          <para>
            <!--
            List only defined containers.
              -->
            定義済みのコンテナ (設定ファイルが存在するコンテナ) のみを表示します。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-f,--fancy</option>
        </term>
        <listitem>
          <para>
            <!--
            Use a fancy, column-based output.
            -->
            装飾付きのカラムベースの出力を使用します。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-F,--fancy-format <replaceable>format</replaceable></option>
        </term>
        <listitem>
          <para>
            <!--
            Comma separated list of columns to show in the fancy output.
            The list of accepted and default fields is listed in \-\-help.
            -->
            装飾付き出力で表示するカラムのコンマ区切りのリスト。デフォルトで表示される項目と指定可能な項目名は --help オプションで確認してください。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-g,--groups <replaceable>groups</replaceable></option>
        </term>
        <listitem>
          <para>
            <!--
            Comma separated list of groups the container must have to be displayed.
            The parameter may be passed multiple times.
            -->
            表示させるコンテナグループのコンマ区切りのリスト。このオプションは複数回指定することもできます。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--nesting=<replaceable>NUM</replaceable></option>
        </term>
        <listitem>
          <para>
            <!--
            Show nested containers. The number of nesting levels to be shown can
            be specified by passing a number as argument.
            -->
            ネストしたコンテナを表示します。引数として数字を指定することで、表示するネストのレベルを指定できます。
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--filter=<replaceable>regex</replaceable></option>
        </term>
        <listitem>
          <para>
	    <!--
            The regular expression passed to <command>lxc-ls</command> will be
            applied to the container name. The format is a POSIX extended
            regular expression. It can also be given as additional argument
            without explicitly using <option>&#045;&#045;filter</option>.
	    -->
	    <command>lxc-ls</command> に与える、コンテナ名に対して適用する正規表現です。フォーマットは POSIX 拡張正規表現です。<option>--filter</option> を明示的に使わずに、追加の引数として与えることもできます。
          </para>
        </listitem>
      </varlistentry>
    </variablelist>

  </refsect1>

  <refsect1>
    <title><!-- Examples -->例</title>
    <variablelist>
      <varlistentry>
        <term>lxc-ls --fancy</term>
        <listitem>
        <para>
          <!--
          list all the containers, listing one per line along with its
          name, state, ipv4 and ipv6 addresses.
          -->
          全てのコンテナをリスト表示します。
          一行にはコンテナの名前、状態、IPv4 アドレス、IPv6 アドレスが表示されます。
        </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>lxc-ls --active -1</term>
	<listitem>
	<para>
          <!--
	  list active containers and display the list in one column.
          -->
          稼働中のコンテナを一列にリスト表示します。
	</para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  &commonoptions;

  &seealso;

  <refsect1>
    <title><!-- History -->履歴</title>
    <!--
    Written originally as a shell script by Daniel Lezcano and Serge Hallyn.
    Later reimplemented and extended in Python by Stéphane Graber and then
    reimplemented and extended in C by Christian Brauner.
      -->
    元は Daniel Lezcano と Serge Hallyn によりシェルスクリプトとして書かれていました。のちに、Stéphane Graber が Python で再実装し、拡張しました。その後、Christian Brauner が C で再実装し、拡張しました。
  </refsect1>

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
