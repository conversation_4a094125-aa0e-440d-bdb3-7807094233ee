#!/bin/sh

# SPDX-License-Identifier: LGPL-2.1+

# lxc: linux Container library

DONE=0
cleanup() {
	lxc-destroy -n $CONTAINER_NAME >/dev/null 2>&1 || true

	if [ $DONE -eq 0 ]; then
		echo "FAIL"
		exit 1
	fi
	echo "PASS"
}

trap cleanup EXIT HUP INT TERM
set -eu

# Create a container
CONTAINER_NAME=lxc-test-auto

lxc-create -t busybox -n $CONTAINER_NAME -B dir
CONTAINER_PATH=$(dirname $(lxc-info -n $CONTAINER_NAME -c lxc.rootfs.path -H) | sed -e 's/dir://')
cp $CONTAINER_PATH/config $CONTAINER_PATH/config.bak

# Ensure it's not in lxc-autostart
lxc-autostart -L | grep -q $CONTAINER_NAME && \
    (echo "Container shouldn't be auto-started" && exit 1)

# Mark it as auto-started and re-check
echo "lxc.start.auto = 1" >> $CONTAINER_PATH/config
lxc-autostart -L | grep -q $CONTAINER_NAME || \
    (echo "Container should be auto-started" && exit 1)

# Put it in a test group and set a delay
echo "lxc.group = lxc-auto-test" >> $CONTAINER_PATH/config
echo "lxc.start.delay = 5" >> $CONTAINER_PATH/config

# Check that everything looks good
if [ "$(lxc-autostart -L -g lxc-auto-test | grep $CONTAINER_NAME)" != "$CONTAINER_NAME 5" ]; then
    echo "Invalid autostart setting" && exit 1
fi

# Start it
lxc-autostart -g lxc-auto-test
lxc-wait -n $CONTAINER_NAME -t 5 -s RUNNING || (echo "Container didn't start" && exit 1)
sleep 5

# Restart it
lxc-autostart -g lxc-auto-test -r
lxc-wait -n $CONTAINER_NAME -t 5 -s RUNNING || (echo "Container didn't restart" && exit 1)
sleep 5

# When shutting down, the container calls sync. If the machine running
# the test has a massive backlog, it can take minutes for the sync call to
# finish, causing a test failure.
# So try to reduce that by flushing everything to disk before we attempt
# a container shutdown.
sync

# Shut it down
lxc-autostart -g lxc-auto-test -s -t 120
lxc-wait -n $CONTAINER_NAME -t 120 -s STOPPED || (echo "Container didn't stop" && exit 1)

# Kill it
lxc-autostart -g lxc-auto-test -k
lxc-wait -n $CONTAINER_NAME -t 60 -s STOPPED || (echo "Container didn't die" && exit 1)

DONE=1
