<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-user-nic</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-user-nic</refname>

    <refpurpose>
      Manage nics in another network namespace
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-user-nic</command>
      <command>create</command>
      <arg choice="req"><replaceable>lxcpath</replaceable></arg>
      <arg choice="req"><replaceable>name</replaceable></arg>
      <arg choice="req"><replaceable>pid</replaceable></arg>
      <arg choice="req"><replaceable>type</replaceable></arg>
      <arg choice="req"><replaceable>bridge</replaceable></arg>
      <arg choice="req"><replaceable>container nicname</replaceable></arg>
    </cmdsynopsis>

    <cmdsynopsis>
      <command>lxc-user-nic</command>
      <command>delete</command>
      <arg choice="req"><replaceable>lxcpath</replaceable></arg>
      <arg choice="req"><replaceable>name</replaceable></arg>
      <arg choice="req"><replaceable>path to network namespace</replaceable></arg>
      <arg choice="req"><replaceable>type</replaceable></arg>
      <arg choice="req"><replaceable>bridge</replaceable></arg>
      <arg choice="req"><replaceable>container nicname</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-user-nic</command> is a setuid-root program with which
      unprivileged users may manage network interfaces for use by a
      lxc container.
    </para>
    <para>
      It will consult the configuration file <filename>@LXC_USERNIC_CONF@</filename>
      to determine the number of interfaces which the calling user is allowed to
      create, and which bridge they may attach them to.  It tracks the
      number of interfaces each user has created using the file
      <filename>@LXC_USERNIC_DB@</filename>.  It ensures that the calling
      user is privileged over the network namespace to which the interface
      will be attached.
      <command>lxc-user-nic</command> also allows one to delete network devices.
      Currently only ovs ports can be deleted.
    </para>

  </refsect1>

  <refsect1>

    <title>Options</title>

    <variablelist>
      <varlistentry>
	<term>
	  <option><replaceable>lxcpath</replaceable></option>
	</term>
	<listitem>
	  <para>
	  The path of the container. This is currently not used.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>name</replaceable></option>
	</term>
	<listitem>
	  <para>
	  The name of the container. This is currently not used.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>pid</replaceable></option>
	</term>
	<listitem>
	  <para>
	  The process id for the task to whose network namespace the interface
	  should be attached.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>type</replaceable></option>
	</term>
	<listitem>
	  <para>
	  The network interface type to attach. Currently only veth is
	  supported.  With this type, two interfaces representing each
	  tunnel endpoint are created.  One endpoint will be attached
	  to the specified bridge, while the other will be passed into
	  the container.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>bridge</replaceable></option>
	</term>
	<listitem>
	  <para>
	  The bridge to which to attach the network interface, for
	  instance <filename>lxcbr0</filename>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>container nicname</replaceable></option>
	</term>
	<listitem>
	  <para>
	  The desired interface name in the container. This will be
	  <filename>eth0</filename> if unspecified.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><replaceable>path to network namespace</replaceable></option>
	</term>
	<listitem>
	  <para>
	  A path to open to get a file descriptor for the target
	  network namespace.
	  This is only relevant when an veth device is deleted.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  <refsect1>
    <title>See Also</title>

    <simpara>
      <citerefentry>
	<refentrytitle><command>lxc</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>,

      <citerefentry>
	<refentrytitle><command>lxc-start</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>,

      <citerefentry>
	<refentrytitle><command>lxc-usernet</command></refentrytitle>
	<manvolnum>5</manvolnum>
      </citerefentry>
    </simpara>
   </refsect1>

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
