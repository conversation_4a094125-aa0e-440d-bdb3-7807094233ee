<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-usernet</refentrytitle>
    <manvolnum>5</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-usernet</refname>

    <refpurpose>
      <!--
      unprivileged user network administration file.
      -->
      비특권 사용자의 네트워크 관리용 설정파일
    </refpurpose>
  </refnamediv>

  <refsect1>
    <title><!-- Description -->설명</title>

    <para>
      <!--
      <filename>@LXC_USERNIC_CONF@</filename> controls the limits which the
      program <command>lxc-user-nic</command> places on network interfaces
      which an unprivileged user may create.
      -->
     <filename>@LXC_USERNIC_CONF@</filename>로 비특권 사용자가 <command>lxc-user-nic</command> 명령어로 네트워크 인터페이스를 만들 때, 제한을 걸 수 있다.
    </para>

    <refsect2>
      <title><!-- Configuration -->설정</title>
      <para>
        <!--
      This file consists of multiple entries, one per line, of the form:
          -->
        이 파일은 아래와 같은 형식의 한 줄로 이루어진 여러 항목들로 구성되어 있다.
      </para>

      <para>
      <command>user</command> <command>type</command> <command>bridge</command> <command>number</command>
      </para>
      <para>또는 아래의 형식을 사용할 수 있다.</para>
      <para>
      <command>@group</command> <command>type</command> <command>bridge</command> <command>number</command>
      </para>
      <para>
        <!--
      Where
          -->
        여기서 각 항목들은 다음과 같은 의미를 가진다.
      </para>

      <variablelist>

	<varlistentry>
	  <term>
	    <option>user</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      is the username to whom this entry applies.
              -->
              이 항목이 적용될 사용자 이름을 가리킨다.
	     </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
            <option>@group</option>
          </term>
          <listitem>
            <para>
              <!--
              is the groupname to which this entry applies.
                -->
              이 항목이 적용될 그룹 이름을 가리킨다.
            </para>
          </listitem>
        </varlistentry>

        <varlistentry>
          <term>
	    <option>type</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      is the type of network interface being allowed.  Only veth
	      is currently supported.
              -->
              허용되는 네트워크 인터페이스 형태를 가리킨다. veth만 지원된다.
	     </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>bridge</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      is the bridge to which the network interfaces may be attached, for
	      instance <filename>lxcbr0</filename>.
              -->
              네트워크 인터페이스들을 붙일 수 있는 브리지를 가리킨다.
              예를 들어 <filename>lxcbr0</filename>로 지정 가능하다.
	     </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <option>number</option>
	  </term>
	  <listitem>
	    <para>
              <!--
	      is the number or quota of network interfaces of the given type which the
	      given user or group may attach to the given bridge, for instance <filename>2</filename>.
              -->
              지정한 사용자 또는 그룹이 지정된 브리지에 붙일 수 있는 지정된 형태의 네트워크 인터페이스 개수를 가리킨다.
              예를 들어 <filename>2</filename>로 지정 가능하다.
	     </para>
	  </listitem>
	</varlistentry>
      </variablelist>

      <para>
        <!--
        Since a user can be specified both by username as well as one or
        more usergroups, it is possible that several configuration lines
        enable that user to create network interfaces. In such cases, any
        interfaces create are counted towards the quotas of the user or group
        in the order in which they appear in the file. If the quota of one
        line is full, the rest will be parsed until one is found or the end of
        the file.
          -->
        사용자는 사용자 이름이나 하나 이상의 사용자 그룹을 통해 지정될 수 있으므로, 여러 줄의 설정을 통해 사용자가 네트워크 인터페이스들을 생성할 수 있도록 하는 것이 가능하다.
        이러한 경우, 인터페이스 생성은 파일 상의 순서대로 사용자 또는 그룹의 사용량에 집계된다.
        만약 해당 줄에서 할당한 개수가 가득차면, 또다른 설정이 발견되거나 파일의 끝에 도달할 때까지 행을 계속 읽어들인다.
      </para>
    </refsect2>

  </refsect1>

  <refsect1>
    <title><!-- See Also -->참조</title>
    <simpara>
      <citerefentry>
	<refentrytitle><command>lxc</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle><command>lxc-user-nic</command></refentrytitle>
	<manvolnum>1</manvolnum>
      </citerefentry>
    </simpara>
  </refsect1>

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
