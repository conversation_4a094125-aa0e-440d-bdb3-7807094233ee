#!/bin/sh
#
# SPDX-License-Identifier: LGPL-2.1+
#
# This hook can be used to mount an ecryptfs filesystem as a container's
# rootfs.
# To use this hook, assuming your container is called q1,
#  1. add 'lxc.hook.pre-mount = /usr/share/lxc/hooks/mountecryptfsroot' to
#     the container's configuration file
#  2. Create /var/lib/lxc/q1/ecryptfs-root
#     a. mkdir /var/lib/lxc/q1/ecryptfs-root
#  3. convert your container's root filesystem to be ecryptfs-backed.  Assuming
#     your container is called 'q1', do
#     a. c=q1
#     b. mv /var/lib/lxc/$c/rootfs /var/lib/lxc/$c/rootfs.plain
#     c. mkdir /var/lib/lxc/$c/rootfs{,.crypt}
#     d. sig=$(echo none | ecryptfs-add-passphrase | grep -v Passphrase | cut -d[ -f 2 | cut -d] -f 1)
#     e. echo $sig > /var/lib/lxc/$c/sig
#     f. mount -t ecryptfs -o ecryptfs_cipher=aes,ecryptfs_key_bytes=16,ecryptfs_passthrough=n,ecryptfs_enable_filename_crypto=n,ecryptfs_sig=${sig},sig=${sig},verbosity=0 /var/lib/lxc/$c/rootfs.crypt /var/lib/lxc/$c/rootfs
#     g. rsync -va /var/lib/lxc/$c/rootfs.plain/ /var/lib/lxc/$c/rootfs/
#     h. umount /var/lib/lxc/$c/rootfs
#     i. rm -rf /var/lib/lxc/$c/rootfs.plain
#  4. Now you can start your container by adding the passphrase to your
#     in-kernel keyring using 'ecryptfs-add-passphrase', then starting your
#     container as normal.
#     a. echo none | ecryptfs-add-passphrase
#     b. lxc-start -n q1
#  Note that you may well want to use a wrapped passphrase (see the ecryptfs-wrap-passphrase(1) manual page).

set -e
ecryptfs_crypt=$(echo $LXC_ROOTFS_PATH | sed 's/rootfs$/rootfs.crypt/')
sigfile=$(echo $LXC_CONFIG_FILE | sed 's/config$/sig/')

sig=$(cat $sigfile)
mount -n -t ecryptfs -o ecryptfs_cipher=aes,ecryptfs_key_bytes=16,ecryptfs_passthrough=n,ecryptfs_enable_filename_crypto=n,ecryptfs_sig=${sig},sig=${sig},verbosity=0 $ecryptfs_crypt $LXC_ROOTFS_PATH
exit 0
