<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-cgroup</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-cgroup</refname>

    <refpurpose>
      manage the control group associated with a container
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-cgroup</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="req"><replaceable>state-object</replaceable></arg>
      <arg choice="opt">value</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-cgroup</command> gets or sets the value of a
      <replaceable>state-object</replaceable> (e.g., 'cpuset.cpus')
      in the container's cgroup for the corresponding subsystem (e.g.,
      'cpuset'). If no <optional>value</optional> is specified, the
      current value of the <replaceable>state-object</replaceable> is
      displayed; otherwise it is set.
    </para>

    <para>
      Note that <command>lxc-cgroup</command> does not check that the
      <replaceable>state-object</replaceable> is valid for the running
      kernel, or that the corresponding subsystem is contained in any
      mounted cgroup hierarchy.
    </para>
  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>

      <varlistentry>
	<term>
	  <option><replaceable>state-object</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify the state object name.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option><optional>value</optional></option>
	</term>
	<listitem>
	  <para>
	    Specify the value to assign to the state object.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Examples</title>
    <variablelist>
      <varlistentry>
	<term>lxc-cgroup -n foo devices.list</term>
	<listitem>
	<para>
	  display the allowed devices to be used.
	</para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>lxc-cgroup -n foo cpuset.cpus "0,3"</term>
	<listitem>
	<para>
	  assign the processors 0 and 3 to the container.
	</para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  <refsect1>
    <title>Diagnostic</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
	    The container is not running.
          </para>
        </listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
