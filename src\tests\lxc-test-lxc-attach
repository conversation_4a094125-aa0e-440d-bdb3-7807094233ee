#!/bin/sh

# SPDX-License-Identifier: LGPL-2.1+

# lxc: linux Container library

set -e

# NOTE:
# lxc-attach allocates a pty on the host and attaches any standard file
# descriptors that refer to a pty to it. Standard file descriptors which do not
# refer to a pty are not attached. In order to determine whether lxc-attach
# works correctly we test various methods of redirection on the host. E.g.:
#
#       lxc-attach -n busy -- hostname < /dev/null
#
# This is done to check whether the file descriptor that gets redirected to
# /dev/null is (a) left alone and (b) that lxc-attach does not fail. When
# lxc-attach fails we know that it's behavior has been altered, e.g. by trying
# to attach a standard file descriptor that does not refer to a pty.
# The small table preceeding each test case show which standard file descriptors
# we expect to be attached to a pty and which we expect to be redirected. E.g.
#
#       stdin  --> attached to pty
#       stdout --> attached to pty
#       stderr --> attached to pty

allocate_pty="nopty"

ATTACH_LOG=$(mktemp --dry-run)

FAIL() {
	echo -n "Failed " >&2
	echo "$*" >&2
	cat "${ATTACH_LOG}"
	rm -f "${ATTACH_LOG}" || true
        lxc-destroy -n busy -f
	exit 1
}

# Create a container, start it and wait for it to be in running state.
lxc-create -t busybox -n busy -l trace -o "${ATTACH_LOG}" || FAIL "creating busybox container"
lxc-start -n busy -d -l trace -o "${ATTACH_LOG}" || FAIL "starting busybox container"
lxc-wait -n busy -s RUNNING -l trace -o "${ATTACH_LOG}" || FAIL "waiting for busybox container to run"

if [ -t 0 ] && [ -t 1 ] && [ -t 2 ]; then
	allocate_pty="pty"
	echo "All standard file descriptors refer to a pty."
	echo "Tests for lxc-attach pty allocation and I/O redirection"
	echo "will be performed correctly."
fi

# stdin  --> attached to pty
# stdout --> attached to pty
# stderr --> attached to pty
for i in $(seq 1 100); do
	attach=$(lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- hostname || FAIL "to allocate or setup pty")
	if [ "$attach" != "busy" ]; then
		FAIL "lxc-attach -n busy -- hostname"
	fi
done

# stdin  --> /dev/null
# stdout --> attached to pty
# stderr --> attached to pty
attach=$(lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- hostname < /dev/null || FAIL "to allocate or setup pty")
if [ "$attach" != "busy" ]; then
        FAIL "lxc-attach -n busy -- hostname < /dev/null"
fi

# stdin  --> attached to pty
# stdout --> /dev/null
# stderr --> attached to pty
attach=$(lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- hostname > /dev/null || FAIL "to allocate or setup pty")
if [ -n "$attach" ]; then
        FAIL "lxc-attach -n busy -- hostname > /dev/null"
fi

# stdin  --> attached to pty
# stdout --> attached to pty
# stderr --> /dev/null
attach=$(lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- hostname 2> /dev/null || FAIL "to allocate or setup pty")
if [ "$attach" != "busy" ]; then
        FAIL "lxc-attach -n busy -- hostname 2> /dev/null < /dev/null"
fi

# stdin  --> /dev/null
# stdout --> attached to pty
# stderr --> /dev/null
attach=$(lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- hostname 2> /dev/null < /dev/null || FAIL "to allocate or setup pty")
if [ "$attach" != "busy" ]; then
        FAIL "lxc-attach -n busy -- hostname 2> /dev/null < /dev/null"
fi

# Use a synthetic reproducer in container to produce output on stderr. stdout on
# the host gets redirect to /dev/null. We should still be able to receive
# containers output on stderr on the host. (The command is run in a subshell.
# This allows us to redirect stderr to stdout for the subshell and capture the
# output in the attach variable.)
# stdin  --> attached to pty
# stdout --> /dev/null
# stderr --> attached to pty
attach=$( ( lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- sh -c 'hostname >&2' > /dev/null ) 2>&1 || FAIL "to allocate or setup pty")
if [ "$attach" != "busy" ]; then
        FAIL "lxc-attach -n busy -- sh -c 'hostname >&2' > /dev/null"
fi

# Use a synthetic reproducer in container to produce output on stderr. stderr on
# the host gets redirect to /dev/null. We should not receive output on stderr on
# the host. (The command is run in a subshell. This allows us to redirect stderr
# to stdout for the subshell and capture the output in the attach variable.)
# stdin  --> attached to pty
# stdout --> attach to pty
# stderr --> /dev/null
attach=$( ( lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- sh -c 'hostname >&2' 2> /dev/null ) 2>&1 || FAIL "to allocate or setup pty")
if [ -n "$attach" ]; then
        FAIL "lxc-attach -n busy -- sh -c 'hostname >&2' 2> /dev/null"
fi


# stdin  --> attached to pty
# stdout --> /dev/null
# stderr --> attached to pty
# (As we expect the exit code of the command to be 1 we ignore it.)
attach=$(lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- sh -c 'rm 2>&1' > /dev/null || true)
if [ -n "$attach" ]; then
        FAIL "lxc-attach -n busy -- sh -c 'rm 2>&1' > /dev/null"
fi


# - stdin  --> attached to pty
# - stdout --> attached to pty
# - stderr --> /dev/null
# (As we expect the exit code of the command to be 1 we ignore it.)
attach=$(lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- sh -c 'rm 2>&1' 2> /dev/null || true)
if [ -z "$attach" ]; then
        FAIL "lxc-attach -n busy -- sh -c 'rm 2>&1' 2> /dev/null"
fi

# stdin  --> $in
# stdout --> attached to pty
# stderr --> attached to pty
attach=$(echo hostname | lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- || FAIL "to allocate or setup pty")
if [ "$attach" != "busy" ]; then
        FAIL "echo hostname | lxc-attach -n busy --"
fi

# stdin  --> attached to pty
# stdout --> $out
# stderr --> $err
out=$(mktemp /tmp/out_XXXX)
err=$(mktemp /tmp/err_XXXX)
trap "rm -f $out $err" EXIT INT QUIT PIPE
lxc-attach -n busy -l trace -o "${ATTACH_LOG}" -- sh -c 'echo OUT; echo ERR >&2' > $out 2> $err || FAIL "to allocate or setup pty"
outcontent=$(cat $out)
errcontent=$(cat $err)
if [ "$outcontent" != "OUT" ] || [ "$errcontent" != "ERR" ]; then
        FAIL "lxc-attach -n busy -- sh -c 'echo OUT; echo ERR >&2' > $out 2> $err"
fi

rm -f $out $err

# stdin  --> $in
# stdout --> $out
# stderr --> $err
# (As we expect the exit code of the command to be 1 we ignore it.)
out=$(mktemp /tmp/out_XXXX)
err=$(mktemp /tmp/err_XXXX)
trap "rm -f $out $err" EXIT INT QUIT PIPE
echo "hostname; rm" | lxc-attach -n busy -l trace -o "${ATTACH_LOG}" > $out 2> $err || true
outcontent=$(cat $out)
errcontent=$(cat $err)
if [ "$outcontent" != "busy" ] || [ -z "$errcontent" ]; then
        FAIL "echo 'hostname; rm' | lxc-attach -n busy > $out 2> $err"
fi

rm -f $out $err

lxc-destroy -n busy -f
rm -f "${ATTACH_LOG}" || true

exit 0
