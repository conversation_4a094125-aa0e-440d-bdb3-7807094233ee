#!/bin/sh
# SPDX-License-Identifier: LGPL-2.1+

# Make sure the usual locations are in PATH
export PATH=$PATH:/usr/sbin:/usr/bin:/sbin:/bin

set -e

usage()
{
cat <<EOF
$1 -h|--help [-c|--config]
config: the container configuration to update
EOF
    return 0
}

# Check whether any arguments are provided.
if [ $# -eq 0 ]; then
    usage "${0}"
    exit 0
fi

OPTIONS=$(getopt -o c:h --long config:,help -- "${@}")
eval set -- "${OPTIONS}"

while true; do
	case "${1}" in
		-h|--help)
			usage "${0}"
			exit 0
			;;
		-c|--config)
			CONFIGPATH="${2}"
			shift 2
			;;
		--)
			shift 1
			break
			;;
		*)
			break
			;;
	esac
done

cp "${CONFIGPATH}" "${CONFIGPATH}.backup"

# Deal with lxc.rootfs.backend lines
DRIVER=""
while read -r LINE; do
	DRIVER=$(echo $LINE | sed -n 's/\([[:blank:]]*\|#*\)\(lxc\.rootfs\.backend\)\([[:blank:]]*\|\)\(=[[:blank:]]*\|\)\([[:alnum:]]*\)\([[:space:]]*\)/\5/p')
done < "${CONFIGPATH}"

if [ -z "${DRIVER}" ]; then
	DRIVER="dir"
fi
sed -i 's/\([[:blank:]*]\|#*\)\(lxc\.rootfs\)\([[:blank:]*]\|\)\(=[[:blank:]]*\)\(.*\)/\1lxc\.rootfs\.path\3\4'"${DRIVER}"':\5/g' "${CONFIGPATH}"

sed -i \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.id_map\)\([[:blank:]*]\|=\)/\1lxc\.idmap\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.pts\)\([[:blank:]*]\|=\)/\1lxc\.pty\.max\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.tty\)\([[:blank:]*]\|=\)/\1lxc\.tty\.max\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.devttydir\)\([[:blank:]*]\|=\)/\1lxc\.tty\.dir\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.aa_profile\)\([[:blank:]*]\|=\)/\1lxc\.apparmor\.profile\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.aa_allow_incomplete\)\([[:blank:]*]\|=\)/\1lxc\.apparmor\.allow_incomplete\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.se_context\)\([[:blank:]*]\|=\)/\1lxc\.selinux\.context\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.mount\)\([[:blank:]*]\|=\)/\1lxc\.mount\.fstab\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.utsname\)\([[:blank:]*]\|=\)/\1lxc\.uts\.name\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.seccomp\)\([[:blank:]*]\|=\)/\1lxc\.seccomp\.profile\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.console\)\([[:blank:]*]\|=\)/\1lxc\.console\.path\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.haltsignal\)\([[:blank:]*]\|=\)/\1lxc\.signal\.halt\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.rebootsignal\)\([[:blank:]*]\|=\)/\1lxc\.signal\.reboot\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.stopsignal\)\([[:blank:]*]\|=\)/\1lxc\.signal\.stop\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.syslog\)\([[:blank:]*]\|=\)/\1lxc\.log\.syslog\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.loglevel\)\([[:blank:]*]\|=\)/\1lxc\.log\.level\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.logfile\)\([[:blank:]*]\|=\)/\1lxc\.log\.file\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.init_cmd\)\([[:blank:]*]\|=\)/\1lxc\.init\.cmd\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.init_uid\)\([[:blank:]*]\|=\)/\1lxc\.init\.uid\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.init_gid\)\([[:blank:]*]\|=\)/\1lxc\.init\.gid\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.limit\)\([[:blank:]*]\|=\)/\1lxc\.prlimit\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.network\)\(\.[[:digit:]*]\)\(\.ipv4\)/\1lxc\.net\3\4/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.network\)\(\.[[:digit:]*]\)/\1lxc\.net\3/g' \
-e 's/\([[:blank:]*]\|#*\)\(lxc\.network\)\([[:blank:]*]\|=\)/\1lxc\.net\3/g' \
-e '/\([[:blank:]*]\|#*\)\(lxc\.rootfs\.backend\)\([[:blank:]*]\|=\)/d' \
-e '/\([[:blank:]*]\|#*\)\(lxc\.pivotdir\)\([[:blank:]*]\|=\)/d' \
-e '/\([[:blank:]*]\|#*\)\(lxc\.kmsg\)\([[:blank:]*]\|=\)/d' \
	"${CONFIGPATH}"

# Finally, deal with network definitions of the following form:
#
# lxc.network.type = veth
# lxc.network.flags = up
# lxc.network.link = lxcbr0
# lxc.network.name= eth0
#
# lxc.network.type = veth
# lxc.network.flags = up
# lxc.network.link = lxcbr0
# lxc.network.name = eth1

set +e

TMPFILE=$(mktemp -p "${PWD}" XXXXXXXXXX)
cp "${CONFIGPATH}" "${TMPFILE}"

LINE_NUM=0
IDX=-1
while read -r LINE; do
	LINE_NUM=$((LINE_NUM+1))
	# A "lxc.network.type" key defines a new network. So everytime we see
	# one we bump IDX and replace any "lxc.network.<subkey>" keys we
	# encounter with "lxc.network.<IDX>.<subkey>".
	echo "${LINE}" | grep -q "lxc.network.type" && IDX=$((IDX+1))
	sed -i \
-e "${LINE_NUM} s/\([[:blank:]*]\|#*\)\(lxc\.network\)\(\.ipv[[:digit:]]\)\([[:blank:]]*\)=\(.*\)/\1lxc\.net\.${IDX}\3\.address\4=\5/g" \
-e "${LINE_NUM} s/\([[:blank:]*]\|#*\)\(lxc\.network\)\.\([^[:digit:]*]\)/\1lxc\.net\.${IDX}\.\3/g" \
	"${CONFIGPATH}"
done < "${TMPFILE}"

rm "${TMPFILE}"
