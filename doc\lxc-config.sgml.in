<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [
    <!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
    <!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>
    <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>
    <refmeta>
        <refentrytitle>lxc-config</refentrytitle>
        <manvolnum>1</manvolnum>
    </refmeta>

    <refnamediv>
        <refname>lxc-config</refname>

        <refpurpose>
            query LXC system configuration
        </refpurpose>
    </refnamediv>

    <refsynopsisdiv>
        <cmdsynopsis>
            <command>lxc-config</command>
            <arg choice="opt">-l</arg>
            <arg choice="opt"><replaceable>item</replaceable></arg>
        </cmdsynopsis>
    </refsynopsisdiv>

    <refsect1>
        <title>Description</title>

        <para>
            <command>lxc-config</command> queries the lxc system
            configuration and lets you list all valid keys or query
            individual keys for their value.
        </para>
    </refsect1>

    <refsect1>
        <title>Options</title>
        <variablelist>
            <varlistentry>
                <term>
                    <option>-l</option>
                </term>
                <listitem>
                    <para>
                        List all supported keys.
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>item</option>
                </term>
                <listitem>
                    <para>
                        Query the value of the specified key.
                    </para>
                </listitem>
            </varlistentry>
        </variablelist>
    </refsect1>

    &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
