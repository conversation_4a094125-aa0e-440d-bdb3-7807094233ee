  network,
  capability,
  file,
  umount,

  # dbus, signal, ptrace and unix are only supported by recent apparmor
  # versions. Comment them if the apparmor parser doesn't recognize them.

  # This also needs additional rules to reach outside of the container via
  # DBus, so just let all of DBus within the container.
  dbus,

  # Allow us to receive signals from anywhere. Note: if per-container profiles
  # are supported, for container isolation this should be changed to something
  # like:
  #   signal (receive) peer=unconfined,
  #   signal (receive) peer=/usr/bin/lxc-start,
  signal (receive),

  # Allow us to send signals to ourselves
  signal peer=@{profile_name},

  # Allow other processes to read our /proc entries, futexes, perf tracing and
  # kcmp for now (they will need 'read' in the first place). Administrators can
  # override with:
  #   deny ptrace (readby) ...
  ptrace (readby),

  # Allow other processes to trace us by default (they will need 'trace' in
  # the first place). Administrators can override with:
  #   deny ptrace (tracedby) ...
  ptrace (tracedby),

  # Allow us to ptrace ourselves
  ptrace peer=@{profile_name},

  # Allow receive via unix sockets from anywhere. Note: if per-container
  # profiles are supported, for container isolation this should be changed to
  # something like:
  #   unix (receive) peer=(label=unconfined),
  unix (receive),

  # Allow all unix in the container
  unix peer=(label=@{profile_name}),

  # ignore DENIED message on / remount
  deny mount options=(ro, remount) -> /,
  deny mount options=(ro, remount, silent) -> /,

  # allow tmpfs mounts everywhere
  mount fstype=tmpfs,

  # allow hugetlbfs mounts everywhere
  mount fstype=hugetlbfs,

  # allow mqueue mounts everywhere
  mount fstype=mqueue,

  # allow fuse mounts everywhere
  mount fstype=fuse,
  mount fstype=fuse.*,

  # deny access under /proc/bus to avoid e.g. messing with pci devices directly
  deny @{PROC}/bus/** wklx,

  # deny writes in /proc/sys/fs but allow binfmt_misc to be mounted
  mount fstype=binfmt_misc -> /proc/sys/fs/binfmt_misc/,
  deny @{PROC}/sys/fs/** wklx,

  # allow efivars to be mounted, writing to it will be blocked though
  mount fstype=efivarfs -> /sys/firmware/efi/efivars/,

  # block some other dangerous paths
  deny @{PROC}/kcore rwklx,
  deny @{PROC}/sysrq-trigger rwklx,
  deny @{PROC}/acpi/** rwklx,

  # deny writes in /sys except for /sys/fs/cgroup, also allow
  # fusectl, securityfs and debugfs to be mounted there (read-only)
  mount fstype=fusectl -> /sys/fs/fuse/connections/,
  mount fstype=securityfs -> /sys/kernel/security/,
  mount fstype=debugfs -> /sys/kernel/debug/,
  deny mount fstype=debugfs -> /var/lib/ureadahead/debugfs/,
  mount fstype=proc -> /proc/,
  mount fstype=sysfs -> /sys/,
  mount options=(rw, nosuid, nodev, noexec, remount) -> /sys/,
  deny /sys/firmware/efi/efivars/** rwklx,
  deny /sys/kernel/security/** rwklx,
  mount options=(ro, nosuid, nodev, noexec, remount, strictatime) -> /sys/fs/cgroup/,

  # deny reads from debugfs
  deny /sys/kernel/debug/{,**} rwklx,

  # allow paths to be made slave, shared, private or unbindable
  mount options=(rw,make-slave) -> /{,**},
  mount options=(rw,make-rslave) -> /{,**},
  mount options=(rw,make-shared) -> /{,**},
  mount options=(rw,make-rshared) -> /{,**},
  mount options=(rw,make-private) -> /{,**},
  mount options=(rw,make-rprivate) -> /{,**},
  mount options=(rw,make-unbindable) -> /{,**},
  mount options=(rw,make-runbindable) -> /{,**},

  # allow bind-mounts of anything except /proc, /sys and /dev
  mount options=(rw,bind) /[^spd]*{,/**},
  mount options=(rw,bind) /d[^e]*{,/**},
  mount options=(rw,bind) /de[^v]*{,/**},
  mount options=(rw,bind) /dev/.[^l]*{,/**},
  mount options=(rw,bind) /dev/.l[^x]*{,/**},
  mount options=(rw,bind) /dev/.lx[^c]*{,/**},
  mount options=(rw,bind) /dev/.lxc?*{,/**},
  mount options=(rw,bind) /dev/[^.]*{,/**},
  mount options=(rw,bind) /dev?*{,/**},
  mount options=(rw,bind) /p[^r]*{,/**},
  mount options=(rw,bind) /pr[^o]*{,/**},
  mount options=(rw,bind) /pro[^c]*{,/**},
  mount options=(rw,bind) /proc?*{,/**},
  mount options=(rw,bind) /s[^y]*{,/**},
  mount options=(rw,bind) /sy[^s]*{,/**},
  mount options=(rw,bind) /sys?*{,/**},

  # allow various ro-bind-*re*-mounts
  mount options=(ro,remount,bind),
  mount options=(ro,remount,bind,nosuid),
  mount options=(ro,remount,bind,noexec),
  mount options=(ro,remount,bind,nodev),
  mount options=(ro,remount,bind,nosuid,noexec),
  mount options=(ro,remount,bind,noexec,nodev),
  mount options=(ro,remount,bind,nodev,nosuid),
  mount options=(ro,remount,bind,nosuid,noexec,nodev),

  # allow moving mounts except for /proc, /sys and /dev
  mount options=(rw,move) /[^spd]*{,/**},
  mount options=(rw,move) /d[^e]*{,/**},
  mount options=(rw,move) /de[^v]*{,/**},
  mount options=(rw,move) /dev/.[^l]*{,/**},
  mount options=(rw,move) /dev/.l[^x]*{,/**},
  mount options=(rw,move) /dev/.lx[^c]*{,/**},
  mount options=(rw,move) /dev/.lxc?*{,/**},
  mount options=(rw,move) /dev/[^.]*{,/**},
  mount options=(rw,move) /dev?*{,/**},
  mount options=(rw,move) /p[^r]*{,/**},
  mount options=(rw,move) /pr[^o]*{,/**},
  mount options=(rw,move) /pro[^c]*{,/**},
  mount options=(rw,move) /proc?*{,/**},
  mount options=(rw,move) /s[^y]*{,/**},
  mount options=(rw,move) /sy[^s]*{,/**},
  mount options=(rw,move) /sys?*{,/**},
