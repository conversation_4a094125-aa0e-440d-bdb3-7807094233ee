<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-ls</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-ls</refname>

    <refpurpose>
      list the containers existing on the system
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-ls</command>
      <arg choice="opt">-1</arg>
      <arg choice="opt">--active</arg>
      <arg choice="opt">--frozen</arg>
      <arg choice="opt">--running</arg>
      <arg choice="opt">--stopped</arg>
      <arg choice="opt">--defined</arg>
      <arg choice="opt">-f</arg>
      <arg choice="opt">-F <replaceable>format</replaceable></arg>
      <arg choice="opt">-g <replaceable>groups</replaceable></arg>
      <arg choice="opt">--nesting=<replaceable>NUM</replaceable></arg>
      <arg choice="opt">--filter=<replaceable>regex</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>
    <para>
      <command>lxc-ls</command> list the containers existing on the
      system.
    </para>
  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>
      <varlistentry>
        <term>
          <option>-1</option>
        </term>
        <listitem>
          <para>
            Show one entry per line. (default when /dev/stdout isn't a tty)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--active</option>
        </term>
        <listitem>
          <para>
            List only active containers (same as --frozen --running).
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--frozen</option>
        </term>
        <listitem>
          <para>
            List only frozen containers.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--running</option>
        </term>
        <listitem>
          <para>
            List only running containers.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--stopped</option>
        </term>
        <listitem>
          <para>
            List only stopped containers.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--defined</option>
        </term>
        <listitem>
          <para>
            List only defined containers.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-f,--fancy</option>
        </term>
        <listitem>
          <para>
            Use a fancy, column-based output.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-F,--fancy-format <replaceable>format</replaceable></option>
        </term>
        <listitem>
          <para>
            Comma separated list of columns to show in the fancy output.
            The list of accepted and default fields is listed in --help.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-g,--groups <replaceable>groups</replaceable></option>
        </term>
        <listitem>
          <para>
            Comma separated list of groups the container must have to be displayed.
            The parameter may be passed multiple times.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--nesting=<replaceable>NUM</replaceable></option>
        </term>
        <listitem>
          <para>
            Show nested containers. The number of nesting levels to be shown can
            be specified by passing a number as argument.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>--filter=<replaceable>regex</replaceable></option>
        </term>
        <listitem>
          <para>
            The regular expression passed to <command>lxc-ls</command> will be
            applied to the container name. The format is a POSIX extended
            regular expression. It can also be given as additional argument
            without explicitly using <option>--filter</option>.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  <refsect1>
    <title>Examples</title>
    <variablelist>
      <varlistentry>
        <term>lxc-ls --fancy</term>
        <listitem>
        <para>
          list all the containers, listing one per line along with its
          name, state, ipv4 and ipv6 addresses.
        </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>lxc-ls --active -1</term>
        <listitem>
        <para>
          list active containers and display the list in one column.
        </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  &commonoptions;

  &seealso;

  <refsect1>
    <title>History</title>
    Written originally as a shell script by Daniel Lezcano and Serge Hallyn.
    Later reimplemented and extended in Python by Stéphane Graber and then
    reimplemented and extended in C by Christian Brauner.
  </refsect1>

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
