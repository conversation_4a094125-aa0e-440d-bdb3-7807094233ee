<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [
    <!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
    <!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>
    <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>
    <refmeta>
        <refentrytitle>lxc-config</refentrytitle>
        <manvolnum>1</manvolnum>
    </refmeta>

    <refnamediv>
        <refname>lxc-config</refname>

        <refpurpose>
          <!--
            query LXC system configuration
            -->
          LXC 시스템 설정 얻어오기
        </refpurpose>
    </refnamediv>

    <refsynopsisdiv>
        <cmdsynopsis>
            <command>lxc-config</command>
            <arg choice="opt">-l</arg>
            <arg choice="opt"><replaceable>item</replaceable></arg>
        </cmdsynopsis>
    </refsynopsisdiv>

    <refsect1>
        <title><!-- Description -->설명</title>

        <para>
          <!--
            <command>lxc-config</command> queries the lxc system
            configuration and lets you list all valid keys or query
            individual keys for their value.
            -->
          <command>lxc-config</command>는 lxc 시스템 설정을 보여준다. 가능한 모든 항목의 이름을 나열하기도 하고 각각의 항목들에 설정되어 잇는 값을 보여주기도 한다.
        </para>
    </refsect1>

    <refsect1>
        <title><!-- Options -->옵션</title>
        <variablelist>
            <varlistentry>
                <term>
                    <option>-l</option>
                </term>
                <listitem>
                    <para>
                      <!--
                        List all supported keys.
                        -->
                      지원되는 모든 항목의 이름을 나열한다.
                    </para>
                </listitem>
            </varlistentry>

            <varlistentry>
                <term>
                    <option>item</option>
                </term>
                <listitem>
                    <para>
                      <!--
                        Query the value of the specified key.
                        -->
                      지정한 항목에 설정되어 있는 값을 표시한다.
                    </para>
                </listitem>
            </varlistentry>
        </variablelist>
    </refsect1>

    &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
