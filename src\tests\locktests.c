/* liblxcapi
 *
 * SPDX-License-Identifier: GPL-2.0-only
 *
 */

#include "config.h"

#include "lxclock.h"
#include "config.h"
#include <unistd.h>
#include <signal.h>
#include <stdio.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <stdlib.h>

#define mycontainername "lxctest.sem"
#define TIMEOUT_SECS 3

static void test_two_locks(void)
{
	struct lxc_lock *l;
	pid_t pid;
	int ret, status;
	int p[2];
	char c;

	if (pipe(p) < 0)
		exit(EXIT_FAILURE);

	if ((pid = fork()) < 0)
		exit(EXIT_FAILURE);

	if (pid == 0) {
		if (read(p[0], &c, 1) < 0) {
			perror("read");
			exit(EXIT_FAILURE);
		}

		l = lxc_newlock("/tmp", "lxctest-sem");
		if (!l) {
			fprintf(stderr, "%d: child: failed to create lock\n", __LINE__);
			exit(EXIT_FAILURE);
		}

		if (lxclock(l, 0) < 0) {
			fprintf(stderr, "%d: child: failed to grab lock\n", __LINE__);
			exit(EXIT_FAILURE);
		}

		fprintf(stderr, "%d: child: grabbed lock\n", __LINE__);
		exit(EXIT_SUCCESS);
	}

	l = lxc_newlock("/tmp", "lxctest-sem");
	if (!l) {
		fprintf(stderr, "%d: failed to create lock\n", __LINE__);
		exit(EXIT_FAILURE);
	}

	if (lxclock(l, 0) < 0) {
		fprintf(stderr, "%d; failed to get lock\n", __LINE__);
		exit(EXIT_FAILURE);
	}

	if (write(p[1], "a", 1) < 0) {
		perror("write");
		exit(EXIT_FAILURE);
	}

	sleep(3);

	ret = waitpid(pid, &status, WNOHANG);
	if (ret == pid) { // task exited
		if (WIFEXITED(status)) {
			printf("%d exited normally with exit code %d\n", pid,
				WEXITSTATUS(status));
			if (WEXITSTATUS(status) != 0)
				exit(EXIT_FAILURE);
		} else
			printf("%d did not exit normally\n", pid);
		return;
	} else if (ret < 0) {
		perror("waitpid");
		exit(EXIT_FAILURE);
	}

	kill(pid, SIGKILL);
	wait(&status);
	close(p[1]);
	close(p[0]);
	lxcunlock(l);
	lxc_putlock(l);
}

int main(int argc, char *argv[])
{
	int ret;
	struct lxc_lock *lock;

	lock = lxc_newlock(NULL, NULL);
	if (!lock) {
		fprintf(stderr, "%d: failed to get unnamed lock\n", __LINE__);
		exit(EXIT_FAILURE);
	}

	ret = lxclock(lock, 0);
	if (ret) {
		fprintf(stderr, "%d: failed to take unnamed lock (%d)\n", __LINE__, ret);
		exit(EXIT_FAILURE);
	}

	ret = lxcunlock(lock);
	if (ret) {
		fprintf(stderr, "%d: failed to put unnamed lock (%d)\n", __LINE__, ret);
		exit(EXIT_FAILURE);
	}
	lxc_putlock(lock);

	lock = lxc_newlock("/var/lib/lxc", mycontainername);
	if (!lock) {
		fprintf(stderr, "%d: failed to get lock\n", __LINE__);
		exit(EXIT_FAILURE);
	}

	struct stat sb;
	char *pathname = RUNTIME_PATH "/lxc/lock/var/lib/lxc/";

	ret = stat(pathname, &sb);
	if (ret != 0) {
		fprintf(stderr, "%d: filename %s not created\n", __LINE__,
			pathname);
		exit(EXIT_FAILURE);
	}
	lxc_putlock(lock);

	test_two_locks();

	fprintf(stderr, "all tests passed\n");

	exit(ret);
}
