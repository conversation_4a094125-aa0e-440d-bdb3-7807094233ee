#!/bin/sh
# lxc-apparmor-load: Load AppArmor profiles, if supported by the system

set -eu

# don't load profiles if mount mediation is not supported
SYSF=/sys/kernel/security/apparmor/features/mount/mask
if [ -f $SYSF ]; then
	if [ -x /lib/apparmor/profile-load ]; then
		/lib/apparmor/profile-load usr.bin.lxc-copy
		/lib/apparmor/profile-load usr.bin.lxc-start
		/lib/apparmor/profile-load lxc-containers
	elif [ -x /lib/init/apparmor-profile-load ]; then
		/lib/init/apparmor-profile-load usr.bin.lxc-copy
		/lib/init/apparmor-profile-load usr.bin.lxc-start
		/lib/init/apparmor-profile-load lxc-containers
	fi
fi
