#!/bin/sh
#
# lxc Start/Stop LXC autoboot containers
#
# chkconfig: 345 99 01
# description: Starts/Stops all LXC containers configured for autostart.
#
### BEGIN INIT INFO
# Provides: lxc
# Required-Start: $syslog $remote_fs
# Required-Stop: $syslog $remote_fs
# Should-Start: cgroupfs-mount
# Should-Stop: cgroupfs-mount
# Default-Start: 2 3 4 5
# Default-Stop: 0 1 6
# Short-Description: Bring up/down LXC autostart containers
# Description: Bring up/down LXC autostart containers
### END INIT INFO

# To be replaced by LSB functions, if they can be found
# Defined here for distributions that don't have log_daemon_msg
log_daemon_msg () {
    echo $@
}

# Try to source LSB init functions to define LSB log_* functions.
test ! -r /lib/lsb/init-functions ||
        . /lib/lsb/init-functions

start() {
    # Setup host /dev for autodev containers.
    log_daemon_msg "Starting LXC autoboot containers: "
    @LIBEXECDIR@/lxc/lxc-containers start
}

stop() {
    log_daemon_msg "Stopping LXC containers: "
    @LIBEXECDIR@/lxc/lxc-containers stop
}

# See how we were called.
case "$1" in
    start)
        start
    ;;

    stop)
        stop
    ;;

    restart|reload|force-reload)
        $0 stop
        $0 start
    ;;

    *)
        echo "Usage: $0 {start|stop|restart|reload|force-reload}"
        exit 2
    ;;
esac

exit $?
