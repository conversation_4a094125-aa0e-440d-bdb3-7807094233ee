<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-destroy</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-destroy</refname>

    <refpurpose>
      <!--
      destroy a container.
      -->
      컨테이너 제거
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-destroy</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-f</arg>
      <arg choice="opt">-s</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!--Description-->설명</title>

    <para>
      <!--
      <command>lxc-destroy</command> destroys the system object
      previously created by the <command>lxc-create</command> command.
      -->
      <command>lxc-destroy</command>는 <command>lxc-create</command>로 이전에 생성했던 시스템 객체를 제거한다.
    </para>

  </refsect1>

  <refsect1>

    <title><!-- Options -->옵션</title>

    <variablelist>
      <varlistentry>
	<term>
	  <option>-f, --force</option>
	</term>
	<listitem>
	  <para>
            <!--
	    If a container is running, stop it first.  If this option is
	    not specified and the container is running, then
	    <command>lxc-destroy</command> will be aborted.
            -->
            만약 컨테이너가 실행중이라면, 컨테이너를 종료시킨다.
            이 옵션이 지정되지 않았을 때 컨테이너가 실행중이라면, <command>lxc-destroy</command>는 중지될 것이다.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
        <term><option>-s, --snapshots</option></term>
        <listitem>
	  <para>
            <!--
	    destroy the specified container including all its snapshots.
            -->
            해당 컨테이너의 모든 스냅샷까지 제거한다.
	  </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Diagnostic -->진단</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
            <!--
	    The specified container for destruction was not found. It
	    is probable it does not exists and was already
	    destroyed.You can use the <command>lxc-ls</command>
	    command to list the available containers on the system.
            -->
            제거하려는 컨테이너를 찾을 수 없는 경우이다. 아마도 존재하지 않았거나 이미 제거되었을 경우일 것이다. <command>lxc-ls</command> 명령어를 사용하여 시스템에 존재하는 컨테이너를 확인해볼 수 있다.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
