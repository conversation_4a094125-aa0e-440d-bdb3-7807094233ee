<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-attach</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-attach</refname>

    <refpurpose>
      <!--
      start a process inside a running container.
      -->
      実行中のコンテナ内でプロセスの開始
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-attach</command>
      <arg choice="req">-n, --name <replaceable>name</replaceable></arg>
      <arg choice="opt">-f, --rcfile <replaceable>config_file</replaceable></arg>
      <arg choice="opt">-a, --arch <replaceable>arch</replaceable></arg>
      <arg choice="opt">-e, --elevated-privileges <replaceable>privileges</replaceable></arg>
      <arg choice="opt">-s, --namespaces <replaceable>namespaces</replaceable></arg>
      <arg choice="opt">-R, --remount-sys-proc</arg>
      <arg choice="opt">--keep-env</arg>
      <arg choice="opt">--clear-env</arg>
      <arg choice="opt">-v, --set-var <replaceable>variable</replaceable></arg>
      <arg choice="opt">--keep-var <replaceable>variable</replaceable></arg>
      <arg choice="opt">-u, --uid <replaceable>uid</replaceable></arg>
      <arg choice="opt">-g, --gid <replaceable>gid</replaceable></arg>
      <arg choice="opt">-- <replaceable>command</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      <command>lxc-attach</command> runs the specified
      <replaceable>command</replaceable> inside the container
      specified by <replaceable>name</replaceable>. The container
      has to be running already.
      -->
      <command>lxc-attach</command> は <replaceable>name</replaceable> で指定したコンテナ内で指定した <replaceable>command</replaceable> を実行します。
      実行する時点でコンテナが実行中でなければなりません。
    </para>
    <para>
      <!--
      If no <replaceable>command</replaceable> is specified, the
      current default shell of the user running
      <command>lxc-attach</command> will be looked up inside the
      container and executed. This will fail if no such user exists
      inside the container or the container does not have a working
      nsswitch mechanism.
      -->
      もし <replaceable>command</replaceable> が指定されていない場合、<command>lxc-attach</command> コマンドを実行したユーザのデフォルトシェルをコンテナ内で調べて実行します。
      もしコンテナ内にユーザが存在しない場合や、コンテナで nsswitch 機構が働いていない場合はこの動作は失敗します。
    </para>
    <para>
      <!--
    Previous versions of <command>lxc-attach</command> simply attached to the
    specified namespaces of a container and ran a shell or the specified command
    without first allocating a pseudo terminal. This made them vulnerable to
    input faking via a TIOCSTI <command>ioctl</command> call after switching
    between userspace execution contexts with different privilege levels. Newer
    versions of <command>lxc-attach</command> will try to allocate a pseudo
    terminal file descriptor pair on the host and attach any standard file
    descriptors which refer to a terminal to the container side of the pseudo
    terminal before executing a shell or command. Note, that if none of the
    standard file descriptors refer to a terminal <command>lxc-attach</command>
    will not try to allocate a pseudo terminal. Instead it will simply attach
    to the containers namespaces and run a shell or the specified command.
    -->
      前のバージョンの <command>lxc-attach</command> は、単に指定したコンテナの名前空間にアタッチし、最初に擬似端末 (pseudo terminal) を割り当てないで、シェルもしくは指定したコマンドを実行しました。
      これは、異なる特権レベルを持つユーザ空間の実行コンテキストを切り替えた後に、TIOCSTI <command>ioctl</command> の呼び出し経由で擬似入力を行うことに対して脆弱となります。
      新しいバージョンの <command>lxc-attach</command> は、ホスト上の擬似端末のファイルディスクリプタのペアを割り当てようとします。そしてシェルやコマンドを実行する前に、擬似端末のコンテナ側に対して、ターミナルを参照する標準ファイルディスクリプタをアタッチします。
      ターミナルを参照する標準ファイルディスクリプタがない場合は、<command>lxc-attach</command> は擬似端末の割り当てを行わないことに注意してください。代わりに、単にコンテナの名前空間にアタッチし、シェルや指定したコマンドを実行します。
    </para>

  </refsect1>

  <refsect1>

    <title><!-- Options -->オプション</title>

    <variablelist>

      <varlistentry>
	<term>
	  <option>-f, --rcfile <replaceable>config_file</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the configuration file to configure the virtualization
	    and isolation functionalities for the container.
            -->
            コンテナの仮想化、隔離機能の設定のための設定ファイルを指定します。
	  </para>
	  <para>
            <!--
	   This configuration file if present will be used even if there is
	   already a configuration file present in the previously created
	   container (via lxc-create).
           -->
            (lxc-create 経由で) 前もってコンテナが作られた際の設定ファイルが既にあった場合でも、このオプションが指定された場合は、指定した設定ファイルが使用されます。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-a, --arch <replaceable>arch</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the architecture which the kernel should appear to be
	    running as to the command executed. This option will accept the
	    same settings as the <option>lxc.arch</option> option in
	    container configuration files, see
	    <citerefentry>
	      <refentrytitle><filename>lxc.conf</filename></refentrytitle>
	      <manvolnum>5</manvolnum>
	    </citerefentry>. By default, the current architecture of the
	    running container will be used.
            -->
            コマンドを実行するコンテナのアーキテクチャを指定します。
            このオプションは、コンテナの設定ファイルで指定する <option>lxc.arch</option> オプションと同じものが使用可能です。
            <citerefentry>
	      <refentrytitle><filename>lxc.conf</filename></refentrytitle>
	      <manvolnum>5</manvolnum>
	    </citerefentry> を参照してください。デフォルトでは、実行しているコンテナのアーキテクチャになります。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>
	    -e, --elevated-privileges <replaceable>privileges</replaceable>
	  </option>
	</term>
	<listitem>
	  <para>
            <!--
	    Do not drop privileges when running
	    <replaceable>command</replaceable> inside the container. If
	    this option is specified, the new process will
	    <emphasis>not</emphasis> be added to the container's cgroup(s)
	    and it will not drop its capabilities before executing.
            -->
            コンテナの内部で <replaceable>command</replaceable> を実行する時に特権を削除しません。
            もしこのオプションが指定された場合、新しいプロセスはコンテナの cgroup に追加 <emphasis>されず</emphasis>、実行する前にケーパビリティ (capability) も削除しません。
	  </para>
          <para>
	    <!--
	    You may specify privileges, in case you do not want to elevate all of
	    them, as a pipe-separated list, e.g.
	    <replaceable>CGROUP|LSM</replaceable>. Allowed values are
	    <replaceable>CGROUP</replaceable>, <replaceable>CAP</replaceable> and
	    <replaceable>LSM</replaceable> representing cgroup, capabilities and
	    restriction privileges respectively. (The pipe symbol needs to be escaped,
	    e.g. <replaceable>CGROUP\|LSM</replaceable> or quoted, e.g.
	    <replaceable>"CGROUP|LSM"</replaceable>.)
	    -->
            全ての特権の取得したくない場合は、パイプで連結したリストとして、例えば <replaceable>CGROUP|LSM</replaceable> のように、特権を指定することが可能です。
            指定できる値は、それぞれ cgroup、ケーパビリティ、特権の制限を表す <replaceable>CGROUP</replaceable>、<replaceable>CAP</replaceable>、<replaceable>LSM</replaceable> です。
	    (パイプ記号を <replaceable>CGROUP\|LSM</replaceable> のようにエスケープするか、<replaceable>"CGROUP|LSM"</replaceable> のように引用符号を付ける必要があります。)
          </para>
	  <para>
            <!--
	    <emphasis>Warning:</emphasis> This may leak privileges into the
	    container if the command starts subprocesses that remain active
	    after the main process that was attached is terminated. The
	    (re-)starting of daemons inside the container is problematic,
	    especially if the daemon starts a lot of subprocesses such as
	    <command>cron</command> or <command>sshd</command>.
	    <emphasis>Use with great care.</emphasis>
            -->
            <emphasis>警告：</emphasis>
            もし実行するコマンドが、アタッチするメインプロセスが終了した後も実行されたままのサブプロセスを開始するような場合、このオプションの指定はコンテナ内への特権のリークとなる可能性があります。
            コンテナ内でのデーモンの開始(もしくは再起動)は問題となります。
            デーモンが多数のサブプロセスを開始する <command>cron</command> や <command>sshd</command> のような場合は特に問題となります。
            <emphasis>充分な注意を払って使用してください。</emphasis>
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-s, --namespaces <replaceable>namespaces</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the namespaces to attach to, as a pipe-separated list,
	    e.g. <replaceable>NETWORK|IPC</replaceable>. Allowed values are
	    <replaceable>MOUNT</replaceable>, <replaceable>PID</replaceable>,
	    <replaceable>UTSNAME</replaceable>, <replaceable>IPC</replaceable>,
	    <replaceable>USER </replaceable> and
	    <replaceable>NETWORK</replaceable>. This allows one to change
	    the context of the process to e.g. the network namespace of the
	    container while retaining the other namespaces as those of the
            host. (The pipe symbol needs to be escaped, e.g.
            <replaceable>MOUNT\|PID</replaceable> or quoted, e.g.
            <replaceable>"MOUNT|PID"</replaceable>.)
            -->
            アタッチする名前空間をパイプで連結したリストで指定します。
            例えば <replaceable>NETWORK|IPC</replaceable> のようにです。
            ここで使用可能な値は <replaceable>MOUNT</replaceable>, <replaceable>PID</replaceable>, <replaceable>UTSNAME</replaceable>, <replaceable>IPC</replaceable>, <replaceable>USER </replaceable>, <replaceable>NETWORK</replaceable> です。
            これにより指定した名前空間にプロセスのコンテキストを変更できます。
            例えばコンテナのネットワーク名前空間に変更する一方で、他の名前空間はホストの名前空間のままにするというような事が可能です。
	    (パイプ記号を <replaceable>MOUNT\|PID</replaceable> のようにエスケープするか、<replaceable>"MOUNT|PID"</replaceable> のように引用符号を付ける必要があります。)
	  </para>
	  <para>
            <!--
	    <emphasis>Important:</emphasis> This option implies
	    <option>&#045;e</option>.
            -->
            <emphasis>重要:</emphasis> このオプションは <option>-e</option> オプションを指定しなくても指定している場合と同様の動作をします。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-R, --remount-sys-proc</option>
	</term>
	<listitem>
	  <para>
            <!--
	    When using <option>&#045;s</option> and the mount namespace is not
	    included, this flag will cause <command>lxc-attach</command>
	    to remount <replaceable>/proc</replaceable> and
	    <replaceable>/sys</replaceable> to reflect the current other
	    namespace contexts.
            -->
            <option>-s</option> を指定し、そこにマウント名前空間が含まれない時、このオプションにより <command>lxc-attach</command> は <replaceable>/proc</replaceable> と <replaceable>/sys</replaceable> をリマウントします。
            これは現在の他の名前空間のコンテキストを反映させるためです。
	  </para>
	  <para>
            <!--
	    Please see the <emphasis>Notes</emphasis> section for more
	    details.
            -->
            もっと詳細な説明は <emphasis>注意</emphasis> を参照してください。
	  </para>
	  <para>
            <!--
	    This option will be ignored if one tries to attach to the
	    mount namespace anyway.
            -->
            このオプションは、マウント名前空間へのアタッチが行われる場合は無視されます。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--keep-env</option>
	</term>
	<listitem>
	  <para>
            <!--
	    Keep the current environment for attached programs. This is
	    the current default behaviour (as of version 0.9), but is
	    is likely to change in the future, since this may leak
	    undesirable information into the container. If you rely on
	    the environment being available for the attached program,
	    please use this option to be future-proof. In addition to
	    current environment variables, container=lxc will be set.
            -->
            アタッチされるプログラムに対して現在の環境を保持したままにします。
            これは現在 (バージョン 0.9 時点) のデフォルトの動作ですが、将来は変更される予定です。
            この動作がコンテナ内への望ましくない情報の漏洩につながる可能性があるためです。
            アタッチするプログラムで環境変数が利用可能であることを期待している場合、将来的にもそれが保証されるようにこのオプションを使用するようにしてください。
            現在の環境変数に加えて、container=lxc が設定されます。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--clear-env</option>
	</term>
	<listitem>
	  <para>
            <!--
	    Clear the environment before attaching, so no undesired
	    environment variables leak into the container. The variable
	    container=lxc will be the only environment with which the
	    attached program starts.
            -->
            アタッチする前に環境変数をクリアします。
            これによりコンテナへの不要な環境変数の漏洩が起こらなくなります。
            変数 container=lxc のみがアタッチするプログラムの開始の時の環境変数となります。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-v, --set-var <replaceable>variable</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	    Set an additional environment variable that is seen by the
	    attached program in the container. It is specified in the
	    form of "VAR=VALUE", and can be specified multiple times.
	    -->
	    コンテナにアタッチしたプログラムから見える環境変数を追加します。このオプションは "VAR=VALUE" の形式で指定し、複数回指定できます。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--keep-var <replaceable>variable</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	    Keep a specified environment variable. It can only be
	    specified in conjunction
	    with <replaceable>\-\-clear-env</replaceable>, and can be
	    specified multiple times.
	    -->
	    <replaceable>--clear-env</replaceable> を指定した際に、クリアせずに保持したままにしたい環境変数を指定します。<replaceable>--clear-env</replaceable> と同時にしか使えません。複数回指定できます。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-u, --uid <replaceable>uid</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	    Executes the <replaceable>command</replaceable> with user ID (use numerical value)
	    <replaceable>uid</replaceable> inside the container.
	    -->
	    コンテナ内で、ユーザ ID <replaceable>uid</replaceable> で <replaceable>command</replaceable> を実行します（数値で指定）。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--g, --gid <replaceable>gid</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	    Executes the <replaceable>command</replaceable> with group ID (use numerical value)
	    <replaceable>gid</replaceable> inside the container.
	    -->
	    コンテナ内で、グループ ID <replaceable>gid</replaceable> で <replaceable>command</replaceable> を実行します（数値で指定）。
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Examples -->例</title>
      <para>
        <!--
        To spawn a new shell running inside an existing container, use
        <programlisting>
          lxc-attach -n container
        </programlisting>
        -->
        存在するコンテナ内で新しいシェルを生成するには、以下のようにします。
        <programlisting>
          lxc-attach -n container
        </programlisting>
      </para>
      <para>
        <!--
        To restart the cron service of a running Debian container, use
        <programlisting>
          lxc-attach -n container &#045;&#045; /etc/init.d/cron restart
        </programlisting>
        -->
        実行中の Debian コンテナの cron サービスを再起動するには、以下のように実行します。
        <programlisting>
          lxc-attach -n container -- /etc/init.d/cron restart
        </programlisting>
      </para>
      <para>
        <!--
        To deactivate the network link eth1 of a running container that
        does not have the NET_ADMIN capability, use either the
        <option>-e</option> option to use increased capabilities,
        assuming the <command>ip</command> tool is installed:
        <programlisting>
          lxc-attach -n container -e &#045;&#045; /sbin/ip link delete eth1
        </programlisting>
        -->
        NET_ADMIN ケーパビリティを持たない実行中のコンテナのネットワークインターフェース eth1 の動作を停止させるには、ケーパビリティを増加させるために <option>-e</option> オプションを指定し、<command>ip</command> ツールがインストールされていることを前提に、以下のように実行します。
        <programlisting>
          lxc-attach -n container -e -- /sbin/ip link delete eth1
        </programlisting>
      </para>
  </refsect1>

  <refsect1>
    <title><!-- Compatibility -->互換性</title>
    <para>
      <!--
      Attaching completely (including the pid and mount namespaces) to a
      container requires a kernel of version 3.8 or higher, or a
      patched kernel, please see the lxc website for
      details. <command>lxc-attach</command> will fail in that case if
      used with an unpatched kernel of version 3.7 and prior.
      -->
      (pid とマウント名前空間を含む) コンテナに対する完全なアタッチを行うには 3.8 以上、もしくはパッチを適用したカーネルが必要となります。
      詳しくは lxc のウェブサイトを参照してください。
      パッチが当たっていない 3.8 より小さなバージョンのカーネルを使った場合は、<command>lxc-attach</command> の実行は失敗するでしょう。
    </para>
    <para>
      <!--
      Nevertheless, it will succeed on an unpatched kernel of version 3.0
      or higher if the <option>-s</option> option is used to restrict the
      namespaces that the process is to be attached to to one or more of
      <replaceable>NETWORK</replaceable>, <replaceable>IPC</replaceable>
      and <replaceable>UTSNAME</replaceable>.
      -->
      しかし、もし <option>-s</option> を使用して、アタッチするものを <replaceable>NETWORK</replaceable>, <replaceable>IPC</replaceable>, <replaceable>UTSNAME</replaceable> の 1 つか複数の名前空間に限定して使用すれば、バージョン 3.0 以上のパッチを適用していないカーネルでもアタッチが成功するでしょう。
    </para>
    <para>
      <!--
      Attaching to user namespaces is supported by kernel 3.8 or higher
      with enabling user namespace.
      -->
      ユーザ名前空間へのアタッチは、ユーザ名前空間機能を有効にした 3.8 以上のカーネルでサポートされます。
    </para>
  </refsect1>

  <refsect1>
    <title><!-- Notes -->注意</title>
    <para>
      <!--
      The Linux <replaceable>/proc</replaceable> and
      <replaceable>/sys</replaceable> filesystems contain information
      about some quantities that are affected by namespaces, such as
      the directories named after process ids in
      <replaceable>/proc</replaceable> or the network interface information
      in <replaceable>/sys/class/net</replaceable>. The namespace of the
      process mounting the pseudo-filesystems determines what information
      is shown, <emphasis>not</emphasis> the namespace of the process
      accessing <replaceable>/proc</replaceable> or
      <replaceable>/sys</replaceable>.
      -->
      Linux の <replaceable>/proc</replaceable> と <replaceable>/sys</replaceable> ファイルシステムは名前空間によって影響を受けるある程度の情報を持っています。
      これは <replaceable>/proc</replaceable> 内のプロセス ID の名前のディレクトリや、<replaceable>/sys/class/net</replaceable> 内のネットワークインターフェース名のディレクトリなどです。
      擬似ファイルシステムをマウントしているプロセスの名前空間が、どのような情報を表示するかを決定します。
      <replaceable>/proc</replaceable> や <replaceable>/sys</replaceable> にアクセスしているプロセスの名前空間が決定するのではありません。
    </para>
    <para>
      <!--
      If one uses the <option>-s</option> option to only attach to
      the pid namespace of a container, but not its mount namespace
      (which will contain the <replaceable>/proc</replaceable> of the
      container and not the host), the contents of <option>/proc</option>
      will reflect that of the host and not the container. Analogously,
      the same issue occurs when reading the contents of
      <replaceable>/sys/class/net</replaceable> and attaching to just
      the network namespace.
      -->
      <option>-s</option> を使ってコンテナの pid 名前空間のみをアタッチし、マウント名前空間 (これはコンテナの <replaceable>/proc</replaceable> を含み、ホストのは含まないでしょう) はアタッチしない場合、<option>/proc</option> のコンテンツはコンテナのものではなく、ホストのものとなります。
      似たような事例として、ネットワーク名前空間のみをアタッチして、<replaceable>/sys/class/net</replaceable> のコンテンツを読んだ場合も同じような事が起こるでしょう。
    </para>
    <para>
      <!--
      To work around this problem, the <option>-R</option> flag provides
      the option to remount <replaceable>/proc</replaceable> and
      <replaceable>/sys</replaceable> in order for them to reflect the
      network/pid namespace context of the attached process. In order
      not to interfere with the host's actual filesystem, the mount
      namespace will be unshared (like <command>lxc-unshare</command>
      does) before this is done, essentially giving the process a new
      mount namespace, which is identical to the hosts's mount namespace
      except for the <replaceable>/proc</replaceable> and
      <replaceable>/sys</replaceable> filesystems.
      -->
      この問題への対処のために、<option>-R</option> オプションが <replaceable>/proc</replaceable> と <replaceable>/sys</replaceable> が提供されています。
      これにより、アタッチするプロセスのネットワーク/pid 名前空間のコンテキストを反映させることができます。ホストの実際のファイルシステムに影響を与えないために、実行前にはマウント名前空間は unshare されます (<command>lxc-unshare</command> のように)。
      これは、<replaceable>/proc</replaceable> と <replaceable>/sys</replaceable> ファイルシステム以外はホストのマウント名前空間と同じである、新しいマウント名前空間がプロセスに与えられるということです。
    </para>
    <para>
      <!--
      Previous versions of <command>lxc-attach</command> suffered a bug whereby
      a user could attach to a containers namespace without being placed in a
      writeable cgroup for some critical subsystems. Newer versions of
      <command>lxc-attach</command> will check whether a user is in a writeable
      cgroup for those critical subsystems. <command>lxc-attach</command> might
      thus fail unexpectedly for some users (E.g. on systems where an
      unprivileged user is not placed in a writeable cgroup in critical
      subsystems on login.). However, this behavior is correct and more secure.
      -->
      以前のバージョンの <command>lxc-attach</command> は、いくつかの重要なサブシステムに対して、書き込み可能な cgroup 内に配置することなしに、ユーザがコンテナの名前空間にアタッチできたバグがありました。
      新しいバージョンの <command>lxc-attach</command> は、このような重要なサブシステムに対して、ユーザが書き込み可能な cgroup 内にいるかどうかをチェックします。
      したがって、ユーザによっては <command>lxc-attach</command> は不意に失敗するかもしれません (例えば、非特権ユーザが、ログイン時に重要であるサブシステムの書き込み可能な cgroup に配置されていないようなシステムで)。しかし、この振る舞いは正しく、よりセキュアです。
    </para>
  </refsect1>

  <refsect1>
    <title><!-- Security -->セキュリティ</title>
    <para>
      <!--
      The <option>-e</option> and <option>-s</option> options should
      be used with care, as it may break the isolation of the containers
      if used improperly.
      -->
      <option>-e</option> と <option>-s</option> オプションの使用には注意を払うべきです。
      不適切に使用した場合、コンテナの隔離を破壊してしまう可能性があります。
    </para>
  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
