<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-usernsexec</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-usernsexec</refname>

    <refpurpose>
      <!--
      Run a task as root in a new user namespace.
      -->
      新しいユーザ名前空間内で root としてタスクを実行する
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-usernsexec</command>
      <arg choice="opt">-m <replaceable>uid-map</replaceable></arg>
      <arg choice="req">-- command</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      <command>lxc-usernsexec</command> can be used to run a task as root
      in a new user namespace.
      -->
      <command>lxc-usernsexec</command> は、新しいユーザ名前空間内で root としてタスクを実行するのに使います。
    </para>

  </refsect1>

  <refsect1>

    <title><!-- Options -->オプション</title>

    <variablelist>

      <varlistentry>
	<term>
	  <option>-m <replaceable>uid-map</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	  The uid map to use in the user namespace.  Each map consists of
	  four colon-separate values.  First a character 'u', 'g' or 'b' to
	  specify whether this map pertains to user ids, group ids, or
	  both; next the first userid in the user namespace;  next the
	  first userid as seen on the host;  and finally the number of
	  ids to be mapped.
              -->
            ユーザ名前空間内で使うための uid のマッピング。マッピングは、コロンで分けられた 4 つの値から構成されます。
            最初の文字は 'u', 'g', 'b' のどれかで、マッピングが UID, GID, UID と GID の両方のうちのどれに関するものなのかを指定します。
            次はユーザ名前空間内の最初の ID を指定します。その次はホスト上での最初の ID を指定します。最後はマッピングされる ID の数を指定します。
	  </para>
	  <para>
            <!--
	  More than one map can be specified.  If no map is
	  specified, then by default the full uid and gid ranges granted
	  by /etc/subuid and /etc/subgid will be mapped to the
	  uids and gids starting at 0 in the container.
              -->
            複数回のマッピングを指定することも可能です。もしマッピングを指定しない場合、デフォルトでは /etc/subuid, /etc/subgid で許可された全ての範囲の UID, GID が、コンテナ内の 0 から始まる UID, GID にマッピングされます。
	  </para>
	  <para>
            <!--
	  Note that <replaceable>lxc-usernsexec</replaceable> always tries
	  to setuid and setgid to 0 in the namespace.  Therefore uid 0 in
	  the namespace must be mapped.
              -->
            <replaceable>lxc-usernsexec</replaceable> は、常に名前空間内の 0 に setuid, setgid しようとしますので、名前空間内の UID 0 は必ずマッピングしなければいけないことに注意してください。
	  </para>
	</listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  <refsect1>
    <title><!-- Examples -->例</title>
      <para>
        <!--
        To spawn a shell with the full allotted subuids mapped into
	the container, use
        <programlisting>
	  lxc-usernsexec
        </programlisting>
	To run a different shell than <replaceable>/bin/sh</replaceable>, use
        <programlisting>
	  lxc-usernsexec &#045;&#045; /bin/bash
        </programlisting>
        -->
        割り当てられた subuid の全てをコンテナ内にマッピングしてシェルを起動するには、
        <programlisting>
	  lxc-usernsexec
        </programlisting>
        のようにしてください。<replaceable>/bin/sh</replaceable> とは違うシェルを起動する場合、
        <programlisting>
	  lxc-usernsexec -- /bin/bash
        </programlisting>
        のようにしてください。
      </para>
      <para>
        <!--
	If your user id is 1000, root in a container is mapped to 190000, and
	you wish to chown a file you own to root in the container, you can use:
        <programlisting>
	  lxc-usernsexec -m b:0:1000:1 -m b:1:190000:1 &#045;&#045; /bin/chown 1:1 $file
        </programlisting>
	This maps your userid to root in the user namespace, and 190000 to uid 1.
	Since root in the user namespace is privileged over all userids mapped
	into the namespace, you are allowed to change the file ownership, which
	you could not do on the host using a simple chown.
        -->
        あなたの UID が 1000 で、コンテナ内の root を 190000 にマッピングする場合で、あなたの所有するファイルをコンテナ内の root に chown したい場合は、以下のように実行します。
        <programlisting>
	  lxc-usernsexec -m b:0:1000:1 -m b:1:190000:1 -- /bin/chown 1:1 $file
        </programlisting>
        これはあなたの UID をユーザ名前空間の root に、190000 を uid 1 にマッピングしています。
        ユーザ名前空間内の root は、名前空間内の全ての ID に対して特権があるため、ホスト上で単純に chown を使えない場合でも、あなたはファイルのオーナーを変更する事が可能です。
      </para>
  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
