#!/bin/bash

# SPDX-License-Identifier: LGPL-2.1+

# lxc: linux Container library

set -ex

cleanup() {
	set +e
	lxc-destroy -n lxc-test-rootfs -f
	sed -i '/^root:910000:10000/d' /etc/subuid /etc/subgid
	if [ $PHASE != "done" ]; then
		echo "rootfs test failed at $PHASE"
		exit 1
	fi
	echo "rootfs test passed"
	exit 0
}

PHASE=setup
trap cleanup EXIT

lxc-destroy -n lxc-test-rootfs -f || true
lxc-create -t busybox -n lxc-test-rootfs

PHASE=ro_rootfs
echo "Starting phase $PHASE"
config=/var/lib/lxc/lxc-test-rootfs/config
sed -i '/lxc.rootfs.options/d' $config
echo "lxc.rootfs.options = ro" >> $config

lxc-start -n lxc-test-rootfs
pid=$(lxc-info -n lxc-test-rootfs -p -H)
ro=0
mkdir /proc/$pid/root/rotest || ro=1
[ $ro -ne 0 ]

lxc-stop -n lxc-test-rootfs -k
PHASE=rw_rootfs
echo "Starting phase $PHASE"
sed -i '/lxc.rootfs.options/d' $config
echo "lxc.rootfs.options = rw" >> $config
lxc-start -n lxc-test-rootfs
pid=$(lxc-info -n lxc-test-rootfs -p -H)
ro=0
mkdir /proc/$pid/root/rwtest || ro=1
[ $ro -ne 1 ]
rmdir /proc/$pid/root/rwtest
ro=0

lxc-stop -n lxc-test-rootfs -k
PHASE=idmapped_rootfs
echo "Starting phase $PHASE"
usermod -v 910000-919999 -w 910000-919999 root
sed -i '/lxc.rootfs.options/d' $config
echo "lxc.idmap = u 0 910000 9999" >> $config
echo "lxc.idmap = g 0 910000 9999" >> $config
echo "lxc.rootfs.options = idmap=container" >> $config
lxc-start -n lxc-test-rootfs
pid=$(lxc-info -n lxc-test-rootfs -p -H)
ro=0
lxc-attach -n lxc-test-rootfs -- mkdir /rwtest || ro=1
[ $ro -ne 1 ]
rmdir /proc/$pid/root/rwtest
ro=0

PHASE=done
