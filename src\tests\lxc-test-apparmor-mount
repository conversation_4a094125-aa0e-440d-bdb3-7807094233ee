#!/bin/sh

# SPDX-License-Identifier: LGPL-2.1+

# apparmor_mount: test proper handling of apparmor in kernels
# without mount features

# These require the ubuntu lxc package to be installed.

set -e

# Only run on a normally configured ubuntu lxc system
if [ ! -d /sys/class/net/lxcbr0 ]; then
	echo "lxcbr0 is not configured."
	exit 1
fi
if [ "$(id -u)" != "0" ]; then
	echo "ERROR: Must run as root."
	exit 1
fi

if [ -f /proc/self/ns/cgroup ]; then
	default_profile="lxc-container-default-cgns (enforce)"
else
	default_profile="lxc-container-default (enforce)"
fi

FAIL() {
	echo -n "Failed " >&2
	echo "$*" >&2
	exit 1
}

run_cmd() {
	sudo -i -u $TUSER \
	    env http_proxy=${http_proxy:-} https_proxy=${https_proxy:-} \
	        XDG_RUNTIME_DIR=/run/user/$(id -u $TUSER) ASAN_OPTIONS=${ASAN_OPTIONS:-} \
		UBSAN_OPTIONS=${UBSAN_OPTIONS:-} $*
}

DONE=0
MOUNTSR=/sys/kernel/security/apparmor/features/mount
dnam=$(mktemp -d)
logfile=$(mktemp)
cname=$(basename $dnam)
cleanup() {
	run_cmd lxc-destroy -f -n $cname || true
	umount -l $MOUNTSR || true
	rmdir $dnam || true
	pkill -u $(id -u $TUSER) -9 || true
	sed -i '/lxcunpriv/d' /run/lxc/nics /etc/lxc/lxc-usernet
	sed -i '/^lxcunpriv:/d' /etc/subuid /etc/subgid
	rm -Rf $HDIR /run/user/$(id -u $TUSER) || true
	deluser $TUSER
	if [ $DONE -eq 0 ]; then
		echo 'Failed container log:' >&2
		cat "$logfile" >&2
		echo 'End log' >&2
		rm -f "$logfile"
		echo "FAIL"
		exit 1
	fi
	rm -f "$logfile"
	echo "PASS"
}

clear_log() {
	truncate -s0 "$logfile"
}

trap cleanup exit

chmod 0666 "$logfile"

# This would be much simpler if we could run it as
# root.  However, in order to not have the bind mount
# of an empty directory over the securityfs 'mount' directory
# be removed, we need to do this as non-root.

command -v newuidmap >/dev/null 2>&1 || { echo "'newuidmap' command is missing" >&2; exit 1; }
# create a test user
TUSER=lxcunpriv
HDIR=/home/<USER>

deluser $TUSER && rm -Rf $HDIR || true
useradd $TUSER

mkdir -p $HDIR
echo "$TUSER veth lxcbr0 2" >> /etc/lxc/lxc-usernet
sed -i '/^lxcunpriv:/d' /etc/subuid /etc/subgid

usermod -v 910000-919999 -w 910000-919999 $TUSER

mkdir -p $HDIR/.config/lxc/
cat > $HDIR/.config/lxc/default.conf << EOF
lxc.net.0.type = veth
lxc.net.0.link = lxcbr0
lxc.idmap = u 0 910000 9999
lxc.idmap = g 0 910000 9999
EOF
chown -R $TUSER: $HDIR

mkdir -p /run/user/$(id -u $TUSER)
chown -R $TUSER: /run/user/$(id -u $TUSER)

cd $HDIR

run_cmd lxc-create -t busybox -n $cname

echo "test default confined container"
run_cmd lxc-start -n $cname -d -lDEBUG -o "$logfile"
run_cmd lxc-wait -n $cname -s RUNNING
pid=$(run_cmd lxc-info -p -H -n $cname)
profile=$(cat /proc/$pid/attr/current)
if [ "x$profile" != "x${default_profile}" ]; then
	echo "FAIL: confined container was in profile $profile"
	exit 1
fi
run_cmd lxc-stop -n $cname -k
clear_log

echo "test regular unconfined container"
echo "lxc.apparmor.profile = unconfined" >> $HDIR/.local/share/lxc/$cname/config
run_cmd lxc-start -n $cname -d -lDEBUG -o "$logfile"
run_cmd lxc-wait -n $cname -s RUNNING
pid=$(run_cmd lxc-info -p -H -n $cname)
profile=$(cat /proc/$pid/attr/current)
if [ "x$profile" != "xunconfined" ]; then
	echo "FAIL: unconfined container was in profile $profile"
	exit 1
fi
run_cmd lxc-stop -n $cname -k
clear_log

echo "masking $MOUNTSR"
mount --bind $dnam $MOUNTSR

echo "test default confined container"
sed -i '/apparmor.profile/d' $HDIR/.local/share/lxc/$cname/config
run_cmd lxc-start -n $cname -d || true
sleep 3
pid=$(run_cmd lxc-info -p -H -n $cname) || true
if [ -n "$pid" -a "$pid" != "-1" ]; then
	echo "FAIL: confined container started without mount restrictions"
	echo "pid was $pid"
	exit 1
fi

echo "test regular unconfined container"
echo "lxc.apparmor.profile = unconfined" >> $HDIR/.local/share/lxc/$cname/config
run_cmd lxc-start -n $cname -d -lDEBUG -o "$logfile"
run_cmd lxc-wait -n $cname -s RUNNING
pid=$(run_cmd lxc-info -p -H -n $cname)
if [ "$pid" = "-1" ]; then
	echo "FAIL: unconfined container failed to start without mount restrictions"
	exit 1
fi
profile=$(cat /proc/$pid/attr/current)
if [ "x$profile" != "xunconfined" ]; then
	echo "FAIL: confined container was in profile $profile"
	exit 1
fi
run_cmd lxc-stop -n $cname -k
clear_log

echo "testing override"
sed -i '/apparmor.profile/d' $HDIR/.local/share/lxc/$cname/config
echo "lxc.apparmor.allow_incomplete = 1" >> $HDIR/.local/share/lxc/$cname/config
run_cmd lxc-start -n $cname -d -lDEBUG -o "$logfile"
run_cmd lxc-wait -n $cname -s RUNNING
pid=$(run_cmd lxc-info -p -H -n $cname)
if [ "$pid" = "-1" ]; then
	echo "FAIL: excepted container failed to start without mount restrictions"
	exit 1
fi
profile=$(cat /proc/$pid/attr/current)
if [ "x$profile" != "x${default_profile}" ]; then
	echo "FAIL: confined container was in profile $profile"
	exit 1
fi
run_cmd lxc-stop -n $cname -k
clear_log

DONE=1
