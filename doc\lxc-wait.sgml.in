<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-wait</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-wait</refname>

    <refpurpose>
      wait for a specific container state
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-wait</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="req">-s <replaceable>states</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>
    <para>
      <command>lxc-wait</command> waits for a specific container state
      before exiting, this is useful for scripting.
    </para>
  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>

      <varlistentry>
	<term>
	  <option>-s, --state <replaceable>states</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify the container state(s) to wait for. The container
	    states can be ORed to specify several states.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-t, --timeout <replaceable>timeout</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Wait timeout seconds for desired state to be reached.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Examples</title>
    <variablelist>
      <varlistentry>
	<term>lxc-wait -n foo -s RUNNING</term>
	<listitem>
	<para>
	  exits when 'RUNNING' is reached.
	</para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>lxc-wait -n foo -s 'RUNNING|STOPPED'</term>
	<listitem>
	<para>
	  exits when 'RUNNING' or 'STOPPED' state is reached.
	</para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  <refsect1>
    <title>Diagnostic</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
	    The specified container was not created before with
	    the <command>lxc-create</command> command.
          </para>
        </listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
