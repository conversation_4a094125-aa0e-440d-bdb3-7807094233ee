name: Coverity
on:
  push:
    branches:
      - main

permissions:
  contents: read

jobs:
  coverity:
    name: Build and upload
    runs-on: ubuntu-24.04
    if: github.repository == 'lxc/lxc'
    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - name: Download Coverity Build Tool
        run: |
          wget -q https://scan.coverity.com/download/cxx/linux64 --post-data "token=$TOKEN&project=lxc/lxc" -O cov-analysis-linux64.tar.gz
          mkdir cov-analysis-linux64
          tar xzf cov-analysis-linux64.tar.gz --strip 1 -C cov-analysis-linux64
        env:
          TOKEN: ${{ secrets.COVERITY_SCAN_TOKEN }}

      - name: Install dependencies
        run: |
          sudo apt-get update -qq
          sudo apt-get install -qq gcc clang meson
          sudo apt-get install -qq libapparmor-dev libcap-dev libseccomp-dev libselinux1-dev linux-libc-dev libpam0g-dev docbook2x libdbus-1-dev

      - name: Run coverity
        run: |
          # Configure
          export PATH="$(pwd)/cov-analysis-linux64/bin:${PATH}"
          export CFLAGS="-Wall -Werror"
          export LDFLAGS="-pthread -lpthread"

          BUILD="$(pwd)/build"
          meson setup -Dtests=true -Dpam-cgroup=true -Dcoverity-build=true build/

          # Build
          cov-build --dir cov-int ninja -C ${BUILD}
          tar czvf upload.tgz cov-int

          # Submit the results
          curl \
            --form project=lxc/lxc \
            --form token=${TOKEN} \
            --form email=<EMAIL> \
            --form file=@upload.tgz \
            --form version=main \
            --form description="${GITHUB_SHA}" \
            https://scan.coverity.com/builds?project=lxc/lxc
        env:
          TOKEN: ${{ secrets.COVERITY_SCAN_TOKEN }}
