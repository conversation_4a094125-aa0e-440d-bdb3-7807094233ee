<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-monitor</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-monitor</refname>

    <refpurpose>
      monitor the container state
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-monitor</command>
      <arg choice="opt">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-Q <replaceable>name</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-monitor</command> monitors the state of containers. The
      <replaceable>name</replaceable> argument may be used to specify
      which containers to monitor. It is a regular expression, conforming
      with posix2, so it is possible to monitor all the containers,
      several of them or just one. If not specified,
      <replaceable>name</replaceable> will default to '.*' which will
      monitor all containers in <command>lxcpath</command>.
    </para>

    <para>
      The <option>-P, --lxcpath</option>=PATH option may be specified multiple
      times to monitor more than one container path. Note however that
      containers with the same name in multiple paths will be
      indistinguishable in the output.
    </para>

  </refsect1>

  <refsect1>
    <title>Options</title>

    <variablelist>
      <varlistentry>
	<term>
	  <option>-Q, --quit</option>
	</term>
	<listitem>
	  <para>
	    Ask the lxc-monitord daemon on each given <command>lxcpath</command>
	    to quit. After receiving this command, lxc-monitord will exit
	    immediately as soon as it has no clients instead of waiting the
	    normal 30 seconds for new clients. This is useful if you need to
	    unmount the filesystem <command>lxcpath</command> is on.
	  </para>
	</listitem>
      </varlistentry>
     </variablelist>
  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Examples</title>
    <variablelist>
      <varlistentry>
	<term>lxc-monitor -n foo</term>
	<listitem>
	<para>
	  will monitor the different states for container foo.
	</para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>lxc-monitor -n 'foo|bar'</term>
	<listitem>
	<para>
	  will monitor the different states for container foo and bar.
	</para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>lxc-monitor -n '[fb].*'</term>
	<listitem>
	<para>
	  will monitor the different states for container with the
	  name beginning with letter 'f' or 'b'.
	</para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>lxc-monitor -n '.*'</term>
	<listitem>
	<para>
	  will monitor the different states for all containers.
	</para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  <refsect1>
    <title>Diagnostic</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
	    The specified container was not created before with
	    the <command>lxc-create</command> command.
          </para>
        </listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  <refsect1>
    <title>See Also</title>

    <simpara>
      <citerefentry>
	<refentrytitle>regex</refentrytitle>
	<manvolnum>7</manvolnum>
      </citerefentry>,
    </simpara>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
