/* SPDX-License-Identifier: LGPL-2.1+ */

#include "config.h"

#include <alloca.h>
#include <stdio.h>
#include <sched.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <string.h>
#include <sys/reboot.h>
#include <sys/types.h>
#include <sys/wait.h>

#include "namespace.h"

#include <linux/reboot.h>

/*
 * glibc clone(2) wrapper function prototypes as defined in that manpage. Most
 * architectures use clone(...), but ia64 uses __clone2(...).
 */
int clone(int (*fn)(void *), void *child_stack, int flags, void *arg, ...);
int __clone2(int (*fn)(void *), void *stack_base, size_t stack_size, \
		int flags, void *arg, ...);

static int do_reboot(void *arg)
{
	int *cmd = arg;

	if (reboot(*cmd))
		printf("failed to reboot(%d): %s\n", *cmd, strerror(errno));

	return 0;
}

static int test_reboot(int cmd, int sig)
{
	long stack_size = 4096;
	void *stack = alloca(stack_size) + stack_size;
	int status;
	pid_t ret;

#if defined(__ia64__)
	ret = __clone2(do_reboot, stack, stack_size, \
			CLONE_NEWPID | SIGCHLD, &cmd);
#else
	ret = clone(do_reboot, stack, CLONE_NEWPID | SIGCHLD, &cmd);
#endif
	if (ret < 0) {
		printf("failed to clone: %s\n", strerror(errno));
		return -1;
	}

	if (wait(&status) < 0) {
		printf("unexpected wait error: %s\n", strerror(errno));
		return -1;
	}

	if (!WIFSIGNALED(status)) {
		if (sig != -1)
			printf("child process exited but was not signaled\n");

		return -1;
	}

	if (WTERMSIG(status) != sig) {
		printf("signal termination is not the one expected\n");
		return -1;
	}

	return 0;
}

static int have_reboot_patch(void)
{
	FILE *f = fopen("/proc/sys/kernel/ctrl-alt-del", "r");
	int ret;
	int v;

	if (!f)
		return 0;

	ret = fscanf(f, "%d", &v);
	fclose(f);
	if (ret != 1)
		return 0;

	ret = reboot(v ? LINUX_REBOOT_CMD_CAD_ON : LINUX_REBOOT_CMD_CAD_OFF);
	if (ret != -1)
		return 0;

	return 1;
}

int main(int argc, char *argv[])
{
	int status;

	if (getuid() != 0) {
		printf("Must run as root.\n");
		return 1;
	}

	status = have_reboot_patch();
	if (status != 0) {
		printf("Your kernel does not have the container reboot patch\n");
		return 1;
	}

	status = test_reboot(LINUX_REBOOT_CMD_CAD_ON, -1);
	if (status >= 0) {
		printf("reboot(LINUX_REBOOT_CMD_CAD_ON) should have failed\n");
		return 1;
	}
	printf("reboot(LINUX_REBOOT_CMD_CAD_ON) has failed as expected\n");

	status = test_reboot(LINUX_REBOOT_CMD_RESTART, SIGHUP);
	if (status < 0)
		return 1;
	printf("reboot(LINUX_REBOOT_CMD_RESTART) succeed\n");

	status = test_reboot(LINUX_REBOOT_CMD_RESTART2, SIGHUP);
	if (status < 0)
		return 1;
	printf("reboot(LINUX_REBOOT_CMD_RESTART2) succeed\n");

	status = test_reboot(LINUX_REBOOT_CMD_HALT, SIGINT);
	if (status < 0)
		return 1;
	printf("reboot(LINUX_REBOOT_CMD_HALT) succeed\n");

	status = test_reboot(LINUX_REBOOT_CMD_POWER_OFF, SIGINT);
	if (status < 0)
		return 1;
	printf("reboot(LINUX_REBOOT_CMD_POWERR_OFF) succeed\n");

	printf("All tests passed\n");
	return 0;
}
