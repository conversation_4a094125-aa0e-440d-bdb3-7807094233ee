<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-unshare</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-unshare</refname>

    <refpurpose>
      Run a task in a new set of namespaces.
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-unshare</command>
      <arg choice="req">-s, --namespaces <replaceable>namespaces</replaceable></arg>
      <arg choice="opt">-u, --user <replaceable>user</replaceable></arg>
      <arg choice="opt">-H, --hostname <replaceable>hostname</replaceable></arg>
      <arg choice="opt">-i, --ifname <replaceable>ifname</replaceable></arg>
      <arg choice="opt">-d, --daemon</arg>
      <arg choice="opt">-M, --remount</arg>
      <arg choice="req">command</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-unshare</command> can be used to run a task in a cloned set
      of namespaces.  This command is mainly provided for testing purposes.
      Despite its name, it always uses clone rather than unshare to create
      the new task with fresh namespaces.  Apart from testing kernel
      regressions this should make no difference.
    </para>

  </refsect1>

  <refsect1>

    <title>Options</title>

    <variablelist>

      <varlistentry>
	<term>
	  <option>-s, --namespaces <replaceable>namespaces</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify the namespaces to attach to, as a pipe-separated list,
	    e.g. <replaceable>NETWORK|IPC</replaceable>. Allowed values are
	    <replaceable>MOUNT</replaceable>, <replaceable>PID</replaceable>,
	    <replaceable>UTSNAME</replaceable>, <replaceable>IPC</replaceable>,
	    <replaceable>USER </replaceable> and
	    <replaceable>NETWORK</replaceable>. This allows one to change
	    the context of the process to e.g. the network namespace of the
	    container while retaining the other namespaces as those of the
	    host. (The pipe symbol needs to be escaped, e.g.
            <replaceable>MOUNT\|PID</replaceable> or quoted, e.g.
            <replaceable>"MOUNT|PID"</replaceable>.)
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-u, --user <replaceable>user</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify a userid which the new task should become.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-H, --hostname <replaceable>hostname</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Set the hostname in the new container.  Only allowed if
	    the UTSNAME namespace is set.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-i, --ifname <replaceable>interfacename</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Move the named interface into the container.  Only allowed
	    if the NETWORK namespace is set.  You may specify this
	    argument multiple times to move multiple interfaces into
	    container.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-d, --daemon</option>
	</term>
	<listitem>
	  <para>
	    Daemonize (do not wait for the container to exit before exiting)
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-M, --remount</option>
	</term>
	<listitem>
	  <para>
	    Mount default filesystems (/proc /dev/shm and /dev/mqueue)
	    in the container.  Only allowed if MOUNT namespace is set.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  <refsect1>
    <title>Examples</title>
      <para>
        To spawn a new shell with its own UTS (hostname) namespace,
        <programlisting>
          lxc-unshare -s UTSNAME /bin/bash
        </programlisting>
	If the hostname is changed in that shell, the change will not be
	reflected on the host.
      </para>
      <para>
        To spawn a shell in a new network, pid, and mount namespace,
        <programlisting>
          lxc-unshare -s "NETWORK|PID|MOUNT" /bin/bash
        </programlisting>
	The resulting shell will have pid 1 and will see no network interfaces.
	After re-mounting /proc in that shell,
        <programlisting>
          mount -t proc proc /proc
        </programlisting>
	ps output will show there are no other processes in the namespace.
      </para>
      <para>
        To spawn a shell in a new network, pid, mount, and hostname
        namespace.
        <programlisting>
          lxc-unshare -s "NETWORK|PID|MOUNT|UTSNAME" -M -H myhostname -i veth1 /bin/bash
        </programlisting>

	The resulting shell will have pid 1 and will see two network
	interfaces (lo and veth1).  The hostname will be "myhostname" and
	/proc will have been remounted.  ps output will show there are
	no other processes in the namespace.
      </para>
  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
