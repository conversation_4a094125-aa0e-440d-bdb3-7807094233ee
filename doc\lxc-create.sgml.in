<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-create</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-create</refname>

    <refpurpose>
      creates a container
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-create</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-f <replaceable>config_file</replaceable></arg>
      <arg choice="req">-t <replaceable>template</replaceable></arg>
      <arg choice="opt">-B <replaceable>backingstore</replaceable></arg>
      <arg choice="opt">-- <replaceable>template-options</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      <command>lxc-create</command> creates a system object where is
      stored the configuration information and where can be stored
      user information. The identifier <replaceable>name</replaceable>
      is used to specify the container to be used with the different
      lxc commands.
    </para>
    <para>
      The object is a directory created in <filename>@LXCPATH@</filename>
      and identified by its name.
    </para>

    <para>
      The object is the definition of the different resources an
      application can use or can see. The more the configuration file
      contains information, the more the container is isolated and
      the more the application is jailed.
    </para>

    <para>
      If the configuration file <replaceable>config_file</replaceable>
      is not specified, the container will be created with the default
      isolation: processes, sysv ipc and mount points.
    </para>
  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>

      <varlistentry>
	<term>
	  <option>-f, --config <replaceable>config_file</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify the configuration file to configure the virtualization
	    and isolation functionalities for the container.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-t, --template <replaceable>template</replaceable></option>
	</term>
	<listitem>
	  <para>
	    'template' is the short name of an existing 'lxc-template'
	    script that is called by lxc-create,
	    eg. busybox, debian, fedora, ubuntu or sshd.
	    Refer to the examples in <filename>@LXCTEMPLATEDIR@</filename>
	    for details of the expected script structure.
	    Alternatively, the full path to an executable template script
	    can also be passed as a parameter.
	    "none" can be used to force lxc-create to skip rootfs creation.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-B, --bdev <replaceable>backingstore</replaceable></option>
	</term>
	<listitem>
	  <para>
	    'backingstore' is one of 'dir', 'lvm', 'loop', 'btrfs', 'zfs', 'rbd', or 'best'.  The
	    default is 'dir', meaning that the container root filesystem
	    will be a directory under <filename>@LXCPATH@/container/rootfs</filename>.
	    This backing store type allows the optional
	    <replaceable>--dir ROOTFS</replaceable> to be specified, meaning
	    that the container rootfs should be placed under the specified path,
	    rather than the default.  (The 'none' backingstore type is an alias for
	    'dir'.)  If 'btrfs' is specified, then the
	    target filesystem must be btrfs, and the container rootfs will be
	    created as a new subvolume.  This allows snapshotted clones to be
	    created, but also causes rsync --one-filesystem to treat it as a
	    separate filesystem.
	    If backingstore is 'lvm', then an lvm block device will be
	    used and the following further options are available:
	    <replaceable>--lvname lvname1</replaceable> will create an LV
	    named <filename>lvname1</filename> rather than the default, which
	    is the container name.  <replaceable>--vgname vgname1</replaceable>
	    will create the LV in volume group <filename>vgname1</filename>
	    rather than the default, <filename>lxc</filename>.
	    <replaceable>--thinpool thinpool1</replaceable> will create the
	    LV as a thin-provisioned volume in the pool named
	    <filename>thinpool1</filename> rather than the
	    default, <filename>lxc</filename>.
	    <replaceable>--fstype FSTYPE</replaceable> will create an FSTYPE
	    filesystem on the LV, rather than the default, which is ext4.
	    <replaceable>--fssize SIZE</replaceable> will create a LV (and
	    filesystem) of size SIZE rather than the default, which is 1G.
	  </para>
	  <para>
	    If backingstore is 'loop', you can use <replaceable>--fstype FSTYPE</replaceable> and <replaceable>--fssize SIZE</replaceable> as 'lvm'. The default values for these options are the same as 'lvm'.
	  </para>
	  <para>
	    If backingstore is 'rbd', then you will need to have a valid configuration in <filename>ceph.conf</filename> and a <filename>ceph.client.admin.keyring</filename> defined.
	    You can specify the following options :
	    <replaceable>--rbdname RBDNAME</replaceable> will create a blockdevice named RBDNAME rather than the default, which is the container name.
	    <replaceable>--rbdpool POOL</replaceable> will create the blockdevice in the pool named POOL, rather than the default, which is 'lxc'.
	  </para>
	  <para>
	    If backingstore is 'best', then lxc will try, in order, btrfs,
	    zfs, lvm, and finally a directory backing store.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-- <replaceable>template-options</replaceable></option>
	</term>
	<listitem>
	  <para>
	    This will pass <replaceable>template-options</replaceable> to the
	    template as arguments.  To see the list of options supported by
	    the template, you can run
	    <command>lxc-create -t TEMPLATE -h</command>.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Diagnostic</title>

    <variablelist>

      <varlistentry>
        <term>The container already exists</term>
        <listitem>
          <para>
	    As the message mention it, you try to create a container
	    but there is a container with the same name. You can use
	    the <command>lxc-ls</command> command to list the
	    available containers on the system.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
