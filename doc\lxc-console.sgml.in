<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-console</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-console</refname>

    <refpurpose>
      Launch a console for the specified container
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-console</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-e <replaceable>escape character</replaceable></arg>
      <arg choice="opt">-t <replaceable>ttynum</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>

    <para>
      If the tty service has been configured and is available for the
      container specified as parameter, this command will launch a
      console allowing to log on the container.
    </para>

    <para>
      The available tty are free slots taken by this command. That
      means if the container has four ttys available and the command
      has been launched four times each taking a different tty, the
      fifth command will fail because no console will be available.
    </para>

    <para>
      The command will connect to a tty. If the connection is lost or
      broken, the command can be launched again and regain the tty at
      the state it was before the disconnection.
    </para>

    <para>
      A <replaceable>ttynum</replaceable> of 0 may be given to attach
      to the container's /dev/console instead of its
      dev/tty&lt;<replaceable>ttynum</replaceable>&gt;.
    </para>

    <para>
      A keyboard escape sequence may be used to disconnect from the tty
      and quit lxc-console. The default escape sequence is &lt;Ctrl+a q&gt;.
    </para>

  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>

      <varlistentry>
	<term>
	  <option>-e, --escape <replaceable>escape character</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify the escape sequence prefix to use instead of
            &lt;Ctrl a&gt;.
            This may be given as '^letter' or just 'letter'. For example
            to use &lt;Ctrl+b q&gt; as the escape sequence use -e '^b'.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term>
	  <option>-t, --tty <replaceable>ttynum</replaceable></option>
	</term>
	<listitem>
	  <para>
	    Specify the tty number to connect to or 0 for the console. If not
	    specified the next available tty number will be automatically
	    chosen by the container.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title>Diagnostic</title>

    <variablelist>

      <varlistentry>
        <term>tty service denied</term>
        <listitem>
          <para>
	    No tty is available or there is not enough privilege to
	    use the console. For example, the container belongs to
	    user "foo" and "bar" is trying to open a console to it.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
