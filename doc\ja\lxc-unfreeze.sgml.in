<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-unfreeze</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-unfreeze</refname>

    <refpurpose>
      <!--
      thaw all the container's processes
      -->
      全てのコンテナのプロセスの解凍
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-unfreeze</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      <command>lxc-unfreeze</command> will thaw all the processes
      previously frozen by the <command>lxc-freeze</command> command.
      -->
      <command>lxc-unfreeze</command> は、先に <command>lxc-freeze</command> を使って凍結した全てのプロセスを解凍します。
    </para>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Diagnostic -->診断</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
            <!--
	    The specified container was not created before with
	    the <command>lxc-create</command> command.
            -->
            指定したコンテナが <command>lxc-create</command> で作成されておらず存在しません。
          </para>
        </listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
