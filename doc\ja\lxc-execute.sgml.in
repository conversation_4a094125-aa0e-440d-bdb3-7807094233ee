<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-execute</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-execute</refname>

    <refpurpose>
      <!--
      run an application inside a container.
      -->
      コンテナ内でのアプリケーションの実行
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-execute</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="opt">-d</arg>
      <arg choice="opt">-f <replaceable>config_file</replaceable></arg>
      <arg choice="opt">-s KEY=VAL</arg>
      <arg choice="opt">-u, --uid <replaceable>uid</replaceable></arg>
      <arg choice="opt">-g, --gid <replaceable>gid</replaceable></arg>
      <arg choice="opt">-- <replaceable>command</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->説明</title>

    <para>
      <!--
      <command>lxc-execute</command> runs the specified
      <replaceable>command</replaceable> inside the container
      specified by <replaceable>name</replaceable>.
      -->
      <command>lxc-execute</command> は指定した <replaceable>command</replaceable> を、<replaceable>name</replaceable> で指定したコンテナ内で実行します。
    </para>
    <para>
      <!--
      It will setup the container
      according to the configuration previously defined with the
      lxc-create command or with the configuration file parameter.
      If no configuration is
      defined, the default isolation is used.
      -->
      このコマンドは、lxc-create コマンドであらかじめ定義した設定、もしくはパラメータとして与えた設定ファイルを元にコンテナをセットアップします。
      もし設定が定義されていない場合、デフォルトの隔離を使用します。
    </para>
    <para>
      <!--
      This command is mainly used when you want to quickly launch an
      application in an isolated environment.
      -->
      このコマンドは主に、素早く単一のアプリケーションを隔離された環境で動作させたい時に使います。
    </para>
    <para>
      <!--
      <command>lxc-execute</command> command will run the
      specified command into the container via an intermediate
      process, <command>lxc-init</command>.
      This lxc-init after launching  the specified command,
      will wait for its end and all other reparented processes.
      (to support daemons in the container).
      In other words, in the
      container, <command>lxc-init</command> has the pid 1 and the
      first process of the application has the pid 2.
      -->
      <command>lxc-execute</command> は、<command>lxc-init</command> を間にはさんで、コンテナ内で特定のコマンドを実行します。
      lxc-init は、指定されたコマンドが実行された後は、そのコマンドの終了と、そのコマンドから生成された全てのプロセスの終了を待ちます (これにより、コンテナ内でデーモンのサポートが可能になります)。
      言いかえると、コンテナ内では <command>lxc-init</command> が pid 1 となり、アプリケーションの最初のプロセスの pid が 2 となります。
     </para>
     <para>
       <!--
      The above <command>lxc-init</command> is designed to forward received
      signals to the started command.
       -->
       前述の <command>lxc-init</command> は、受け取ったシグナルを開始したコマンドに送るように設計されています。
     </para>
  </refsect1>

  <refsect1>
    <title><!-- Options -->オプション</title>
    <variablelist>

      <varlistentry>
	<term>
	  <option>-f, --rcfile <replaceable>config_file</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Specify the configuration file to configure the virtualization
	    and isolation functionalities for the container.
            -->
            コンテナに設定したい仮想化および隔離機能の設定を行う設定ファイルを指定します。
	  </para>
	  <para>
            <!--
	   This configuration file if present will be used even if there is
	   already a configuration file present in the previously created
	   container (via lxc-create).
            -->
            もしコンテナ作成前に (lxc-create によって) あらかじめ設定ファイルが指定されている場合であっても、指定した設定ファイルが使われます。
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term>
	  <option>-s, --define <replaceable>KEY=VAL</replaceable></option>
	</term>
	<listitem>
	  <para>
            <!--
	    Assign value <replaceable>VAL</replaceable> to configuration
	    variable <replaceable>KEY</replaceable>. This overrides any
	    assignment done in <replaceable>config_file</replaceable>.
            -->
            設定変数 <replaceable>KEY</replaceable> の値を <replaceable>VAL</replaceable> に設定します。この設定は <replaceable>config_file</replaceable> で設定された値を上書きします。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-d, --daemon</option>
	</term>
	<listitem>
	  <para>
	    <!--
	    Run the container as a daemon. As the container has no
	    more tty, if an error occurs nothing will be displayed,
	    the log file can be used to check the error.
	      -->
            コンテナをデーモンとして実行します。コンテナには TTY がありませんので、エラーが発生した場合は何も表示されません。エラーのチェックにはログファイルを使用すると良いでしょう。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>-u, --uid <replaceable>uid</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	    Executes the <replaceable>command</replaceable> with user ID (use numerical value)
	    <replaceable>uid</replaceable> inside the container.
	    -->
	    コンテナ内で、ユーザ ID <replaceable>uid</replaceable> で <replaceable>command</replaceable> を実行します（数値で指定）。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>
	  <option>--g, --gid <replaceable>gid</replaceable></option>
	</term>
	<listitem>
	  <para>
	    <!--
	    Executes the <replaceable>command</replaceable> with group ID (use numerical value)
	    <replaceable>gid</replaceable> inside the container.
	    -->
	    コンテナ内で、グループ ID <replaceable>gid</replaceable> で <replaceable>command</replaceable> を実行します（数値で指定）。
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term><option>--</option></term>
	<listitem>
	  <para>
            <!--
	    Signal the end of options and disables further option
	    processing. Any arguments after the &#045;&#045; are treated as
	    arguments to <replaceable>command</replaceable>.
            -->
            オプション指定の最後の印で、それ以上のオプションの処理を止めます。
            -- の後の引数は実行する <replaceable>command</replaceable> の引数として扱われます。
	  </para>
	  <para>
            <!--
	    This option is useful when you want specify options
	    to <replaceable>command</replaceable> and don't want
	    <command>lxc-execute</command> to interpret them.
            -->
            このオプションは、<replaceable>command</replaceable> にオプションを指定したいときに、<command>lxc-execute</command> がそのオプションを読み取ってほしくないときに役に立ちます。
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Diagnostic -->診断</title>

    <variablelist>

      <varlistentry>
        <term>The container is busy</term>
        <listitem>
          <para>
            <!--
	    The specified container is already running an
	    application. You should stop it before reuse this
	    container or create a new one.
            -->
            指定したコンテナが既にアプリケーションを実行中の場合。コンテナを再使用したり、新しく作成する前にコンテナを止める必要があります。
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
