
Troubleshooting:
===============


Error:
------

error while loading shared libraries reported after sudo make install
and when trying to run lxc-execute.

"lxc-execute -n foo -f /usr/local/etc/lxc/lxc-macvlan.conf /bin/bash"

/usr/local/bin/lxc-execute: error while loading shared libraries:
  liblxc-0.5.0.so: cannot open shared object file: No such file or
  directory

Answer:
-------
update the ld cache by running ldconfig.



Error:
------

error when starting a container.

"lxc-start Invalid argument"

"lxc-execute -n foo -f /usr/local/etc/lxc/lxc-macvlan.conf /bin/bash"
"[syserr] lxc_start:96: Invalid argument - failed to fork into a new
namespace"

Answer:
-------

read the lxc man page about kernel version prereq :) most probably
your kernel is not configured to support the container options you
want to use.
