  network,
  capability,
  file,

  # The following 3 entries are only supported by recent apparmor versions.
  # Comment them if the apparmor parser doesn't recognize them.
  dbus,
  signal,
  ptrace,

  # currently blocked by apparmor bug
  mount -> /usr/lib*/*/lxc/{**,},
  mount -> /usr/lib*/lxc/{**,},
  mount -> @LXCROOTFSMOUNT@/{,**},
  mount fstype=devpts -> /dev/pts/,
  mount options=bind /dev/pts/ptmx/ -> /dev/ptmx/,
  mount options=bind /dev/pts/** -> /dev/**,
  mount options=(rw, make-slave) -> /{,**},
  mount options=(rw, make-rslave) -> /{,**},
  mount options=(rw, make-shared) -> /{,**},
  mount options=(rw, make-rshared) -> /{,**},
  mount fstype=debugfs,
  mount fstype=fuse.*,
  # allow pre-mount hooks to stage mounts under /var/lib/lxc/<container>/
  mount -> /var/lib/lxc/{**,},

  mount /dev/.lxc-boot-id -> /proc/sys/kernel/random/boot_id,
  mount options=(ro, nosuid, nodev, noexec, remount, bind) -> /proc/sys/kernel/random/boot_id,

  # required for some pre-mount hooks
  mount fstype=overlayfs,
  mount fstype=aufs,
  mount fstype=ecryptfs,

  # all umounts are under the original root's /mnt, but right now we
  # can't allow those umounts after pivot_root.  So allow all umounts
  # right now.  They'll be restricted for the container at least.
  umount,
  #umount /mnt/{**,},

  # This may look a bit redundant, however it appears we need all of
  # them if we want things to work properly on all combinations of kernel
  # and userspace parser...
  pivot_root /usr/lib*/lxc/,
  pivot_root /usr/lib*/*/lxc/,
  pivot_root /usr/lib*/lxc/**,
  pivot_root /usr/lib*/*/lxc/**,
  pivot_root @LXCROOTFSMOUNT@/{,**},

  change_profile -> lxc-*,
  change_profile -> lxc-**,
  change_profile -> unconfined,
  change_profile -> :lxc-*:unconfined,
