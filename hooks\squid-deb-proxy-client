#!/bin/sh
#
# SPDX-License-Identifier: GPL-2.0-only
#
# Make the cloned container aware of squid-deb-proxy settings at start.

# When starting a container, inject the squid-deb-proxy-client proxy
# address in the container's apt configuration.
# This script should be added to templates as a pre-start script, such as:
#lxc.hook.pre-start=/usr/share/lxc/hooks/squid-deb-proxy-client

discovery_script=/usr/share/squid-deb-proxy-client/apt-avahi-discover
proxy_file=/etc/apt/apt.conf.d/50squid-deb-proxy
container_proxy_file=$LXC_ROOTFS_PATH$proxy_file

if [ -f $proxy_file ]; then
    # The host has a proxy file - let's propagate the config to the guest.
    cat $proxy_file > $container_proxy_file
    exit 0
fi

if [ ! -f $discovery_script ]; then
    # There is no squid-deb-proxy-client package installed. Do nothing.
    exit 0
fi

proxy_line=$($discovery_script)  #XXX: Could be multiline?

if [ -n "$proxy_line" ]; then
    doc="# Detected at container start from the host's squid-deb-proxy-client"
    echo "Acquire::http::proxy \"$proxy_line\"; $doc" > $container_proxy_file
else
    if [ -f $proxy_file ]; then
        rm $proxy_file
    fi
fi
exit 0
