<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-checkpoint</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-checkpoint</refname>

    <refpurpose>
      <!--
      checkpoint a container
      -->
      컨테이너의 체크포인트 생성 및 복원
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-checkpoint</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
      <arg choice="req">-D <replaceable>PATH</replaceable></arg>
      <arg choice="opt">-r</arg>
      <arg choice="opt">-s</arg>
      <arg choice="opt">-v</arg>
      <arg choice="opt">-d</arg>
      <arg choice="opt">-F</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->설명</title>
    <para>
      <!--
      <command>lxc-checkpoint</command> checkpoints and restores containers.
      -->
      <command>lxc-checkpoint</command> 는 컨테이너의 체크포인트를 생성 및 복원을 수행한다.
      (역주 : 이 명령어를 사용하기 위해서는 CRIU(Checkpoint/Restore In Userspace)라는 툴이 반드시 필요하다, 컨테이너의 실행상태를 대상으로 한다는 점에서  <command>lxc-snapshot</command>와는 다르다)
    </para>
  </refsect1>

  <refsect1>
    <title><!-- Options -->옵션</title>
    <variablelist>

      <varlistentry>
        <term>
          <option>-r, --restore</option>
        </term>
        <listitem>
          <para>
            <!--
            Restore the checkpoint for the container, instead of dumping it.
            This option is incompatible with <option>-s</option>.
            -->
            컨테이너의 상태를 저장하는 것 대신에 체크포인트로 복원을 수행한다.
            이 옵션은 <option>-s</option>과 같이 사용될 수 없다.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-D <replaceable>PATH</replaceable>, --checkpoint-dir=<replaceable>PATH</replaceable></option>
        </term>
        <listitem>
          <para>
            <!--
            The directory to dump the checkpoint metadata.
            -->
            체크포인트 메타데이터를 저장할 디렉토리를 지정한다.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-s, --stop</option>
        </term>
        <listitem>
          <para>
            <!--
            Optionally stop the container after dumping. This option is
            incompatible with <option>-r</option>.
            -->
            컨테이너의 상태를 저장한 후 컨테이너를 중지한다. 이 옵션은 <option>-r</option>과 같이 사용될 수 없다.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-v, --verbose</option>
        </term>
        <listitem>
          <para>
            <!--
            Enable verbose criu logging.
            -->
            CRIU 로그 기록을 자세하게 한다.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-d, --daemon</option>
        </term>
        <listitem>
          <para>
            <!--
            Restore the container in the background (this is the default).
            Only available when providing <option>-r</option>.
            -->
            컨테이너 복원을 백그라운드에서 수행한다. (이것이 기본으로 되어있다)
            <option>-r</option> 옵션이랑만 사용가능하다.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>
          <option>-F, --foreground</option>
        </term>
        <listitem>
          <para>
            <!--
            Restore the container in the foreground. Only available when
            providing <option>-r</option>.
            -->
            컨테이너 복원을 포그라운드에서 수행한다. <option>-r</option> 옵션이랑만 사용가능하다.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Examples -->예제</title>
    <variablelist>

      <varlistentry>
        <term>lxc-checkpoint -n foo -D /tmp/checkpoint</term>
        <listitem>
          <para>
            <!--
            Checkpoint the container foo into the directory /tmp/checkpoint.
            -->
            foo 컨테이너의 체크포인트를 /tmp/checkpoint 디렉토리에 생성한다.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>lxc-checkpoint -r -n foo -D /tmp/checkpoint</term>
        <listitem>
          <para>
            <!--
            Restore the checkpoint from the directory /tmp/checkpoint.
            -->
            foo 컨테이너를 /tmp/checkpoint 디렉토리에 있는 체크포인트로 복원한다.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsect1>

  &seealso;
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
