<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-top</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-top</refname>

    <refpurpose>
      monitor container statistics
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-top</command>
      <arg choice="opt">--help</arg>
      <arg choice="opt">--delay <replaceable>delay</replaceable></arg>
      <arg choice="opt">--sort <replaceable>sortby</replaceable></arg>
      <arg choice="opt">--reverse</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>Description</title>
    <para>
      <command>lxc-top</command> displays container statistics. The output
      is updated every <replaceable>delay</replaceable> seconds, and is
      ordered according to the <replaceable>sortby</replaceable> value
      given. <command>lxc-top</command> will display as many containers as
      can fit in your terminal. Press 'q' to quit. Press one of the sort
      key letters to sort by that statistic. Pressing a sort key letter a
      second time reverses the sort order.
    </para>
  </refsect1>

  <refsect1>
    <title>Options</title>
    <variablelist>

      <varlistentry>
        <term>
          <option>-d, --delay <replaceable>delay</replaceable></option>
        </term>
        <listitem>
          <para>
            Amount of time in seconds to delay between screen updates.
            The default is 3 seconds.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>-s, --sort <replaceable>sortby</replaceable></option>
        </term>
        <listitem>
          <para>
            Sort the containers by name, cpu use, or memory use. The
            <replaceable>sortby</replaceable> argument should be one of
            the letters n,c,b,m,k to sort by name, cpu use, block I/O, memory,
            or kernel memory use respectively. The default is 'n'.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>-r, --reverse</option>
        </term>
        <listitem>
          <para>
            Reverse the default sort order. By default, names sort in
            ascending alphabetical order and values sort in descending
            amounts (ie. largest value first).
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  <refsect1>
    <title>Example</title>
    <variablelist>
      <varlistentry>
        <term>lxc-top --delay 1 --sort m</term>
        <listitem>
        <para>
          Display containers, updating every second, sorted by memory use.
        </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  <refsect1>
    <title>Notes</title>
    <para>
      For performance reasons the kernel does not account kernel memory use
      unless a kernel memory limit is set. If a limit is not set, <command>
      lxc-top</command> will display kernel memory use as 0. If no containers
      are being accounted, the KMem column will not be displayed. A limit can
      be set by specifying
      <programlisting>
      lxc.cgroup.memory.kmem.limit_in_bytes = <replaceable>number</replaceable>
      </programlisting>
      in your container configuration file, see
      <citerefentry>
        <refentrytitle>lxc.conf</refentrytitle>
        <manvolnum>5</manvolnum>
      </citerefentry>.
    </para>
  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
