<!-- SPDX-License-Identifier: LGPL-2.1+ -->

<!DOCTYPE refentry PUBLIC @docdtd@ [

<!ENTITY commonoptions SYSTEM "@builddir@/common_options.sgml">
<!ENTITY seealso SYSTEM "@builddir@/see_also.sgml">
]>

<refentry>

  <docinfo><date>@LXC_GENERATE_DATE@</date></docinfo>

  <refmeta>
    <refentrytitle>lxc-freeze</refentrytitle>
    <manvolnum>1</manvolnum>
  </refmeta>

  <refnamediv>
    <refname>lxc-freeze</refname>

    <refpurpose>
      <!--
      freeze all the container's processes
      -->
      컨테이너의 모든 프로세스를 동결
    </refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>lxc-freeze</command>
      <arg choice="req">-n <replaceable>name</replaceable></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title><!-- Description -->설명</title>

    <para>
      <!--
      <command>lxc-freeze</command> freezes all the processes running
      inside the container. The processes will be blocked until they
      are explicitly thawed by the <command>lxc-unfreeze</command>
      command. This command is useful for batch managers to schedule a
      group of processes.
      -->
      <command>lxc-freeze</command>는 컨테이너 내부에서 실행되는 모든 프로세스를 동결시킨다.
      프로세스는 <command>lxc-unfreeze</command> 명령어를 이용하여 명시적으로 동결 해제시킬 때까지 블로킹 된다.
      이 명령어는 프로세스 그룹들을 스케줄링하여 일괄처리하는 데 유용하다.
    </para>

  </refsect1>

  &commonoptions;

  <refsect1>
    <title><!-- Diagnostic -->진단</title>

    <variablelist>

      <varlistentry>
        <term>The container was not found</term>
        <listitem>
          <para>
            <!--
	    The specified container was not created before with
	    the <command>lxc-create</command> command.
            -->
            지정한 컨테이너가  <command>lxc-create</command>로 생성된 적이 없다.
            컨테이너가 존재하지 않는다.
          </para>
        </listitem>
      </varlistentry>


    </variablelist>

  </refsect1>

  &seealso;

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
