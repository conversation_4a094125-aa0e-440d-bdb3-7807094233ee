# the fstab mount file
lxc.mount.fstab = ./fstab

# the hostname to be set into the container
lxc.utsname = virtnode

# The network has several of kind of configuration:
#
#  * veth    : the network will use the veth virtual device, the specified
#              link must be a bridge
#  * macvlan : the network will use the macvlan device, the specified link
#              should be an existing interface, usually it is eth0
#  * phys    : the network will use a physical network device, the specified
#              link should be an existing interface
lxc.net.0.type = macvlan

# specify the flags to be used for the network, actually only <up> is allowed
# which mean the network should be set up when created. If the network is set
# up, the loopback is automatically set up too.
lxc.net.0.flags = up

# specify the physical network device which will communicate with the
# outside world
lxc.net.0.link = eth0

# NIC ethernet mac address
lxc.net.0.hwaddr = 4a:49:43:49:79:bd

# specify the ipv4 address of the container. Several lines are allowed and
# will mean several addresses will be assigned to the interface
lxc.net.0.ipv4.address = *******/24

# specify the ipv6 address of the container. Several lines are allowed and
# will mean several addresses will be assigned to the interface
lxc.net.0.ipv6.address = 2003:db8:1:0:214:1234:fe0b:3596

