# SPDX-License-Identifier: LGPL-2.1+

template_scripts = configure_file(
    configuration: conf,
    input: 'lxc-busybox.in',
    output: 'lxc-busybox',
    install: true,
    install_dir: lxctemplatedir)

template_scripts = configure_file(
    configuration: conf,
    input: 'lxc-download.in',
    output: 'lxc-download',
    install: true,
    install_dir: lxctemplatedir)

template_scripts = configure_file(
    configuration: conf,
    input: 'lxc-local.in',
    output: 'lxc-local',
    install: true,
    install_dir: lxctemplatedir)

template_scripts = configure_file(
    configuration: conf,
    input: 'lxc-oci.in',
    output: 'lxc-oci',
    install: true,
    install_dir: lxctemplatedir)
