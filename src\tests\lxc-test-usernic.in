#!/bin/bash

# SPDX-License-Identifier: LGPL-2.1+

# lxc: linux Container library
#
# This is a test script for the lxc-user-nic program
#
# This test assumes an Ubuntu host

DONE=0
LXC_USER_NIC="@LIBEXECDIR@/lxc/lxc-user-nic"

cleanup() {
	set +e

	(
		lxc-stop -n usernic-c1 -k
		lxc-destroy -n usernic-c1

		sed -i '/usernic-user/d' /run/lxc/nics /etc/lxc/lxc-usernet
		ip link set usernic-br0 down
		ip link set usernic-br1 down
		ip link del usernic-br0
		ip link del usernic-br1

		run_cmd "lxc-stop -n b1 -k"
		pkill -u $(id -u usernic-user) -9

		rm -rf /tmp/usernic-test /home/<USER>/run/user/$(id -u usernic-user)

		deluser usernic-user
	) >/dev/null 2>&1

	if [ "$DONE" = "1" ]; then
		echo "PASS"
		exit 0
	fi

	echo "FAIL"
	exit 1
}

run_cmd() {
	sudo -i -u usernic-user \
	    env http_proxy=${http_proxy:-} https_proxy=${https_proxy:-} \
	        XDG_RUNTIME_DIR=/run/user/$(id -u usernic-user) ASAN_OPTIONS=${ASAN_OPTIONS:-} \
		UBSAN_OPTIONS=${UBSAN_OPTIONS:-} $*
}

set -eu
trap cleanup EXIT SIGHUP SIGINT SIGTERM

# create a test user
deluser usernic-user || true
useradd usernic-user
sudo mkdir -p /home/<USER>
sudo chown usernic-user: /home/<USER>
usermod -v 910000-919999 -w 910000-919999 usernic-user

mkdir -p /home/<USER>/.config/lxc/
cat > /home/<USER>/.config/lxc/default.conf << EOF
lxc.net.0.type = empty
lxc.idmap = u 0 910000 10000
lxc.idmap = g 0 910000 10000
EOF

mkdir -p /run/user/$(id -u usernic-user)
chown -R usernic-user: /run/user/$(id -u usernic-user) /home/<USER>

# Create two test bridges
ip link add name usernic-br0 type bridge
ip link add name usernic-br1 type bridge
ip link set usernic-br0 up
ip link set usernic-br1 up


# Create three containers
run_cmd "lxc-create -t busybox -n b1"
run_cmd "lxc-start -n b1 -d"
p1=$(run_cmd "lxc-info -n b1 -p -H")

lxcpath=/home/<USER>/.local/share/lxc
lxcname=b1

# Assign one veth, should fail as no allowed entries yet
if run_cmd "$LXC_USER_NIC create $lxcpath $lxcname $p1 veth usernic-br0 xx1"; then
	echo "FAIL: able to create nic with no entries"
	exit 1
fi

# Give him a quota of two
touch /etc/lxc/lxc-usernet
sed -i '/^usernic-user/d' /etc/lxc/lxc-usernet
echo "usernic-user veth usernic-br0 2" >> /etc/lxc/lxc-usernet

# Assign one veth to second bridge, should fail
if run_cmd "$LXC_USER_NIC create $lxcpath $lxcname $p1 veth usernic-br1 xx1"; then
	echo "FAIL: able to create nic with no entries"
	exit 1
fi

# Assign two veths, should succeed
if ! run_cmd "$LXC_USER_NIC create $lxcpath $lxcname $p1 veth usernic-br0 xx2"; then
	echo "FAIL: unable to create first nic"
	exit 1
fi

if ! run_cmd "$LXC_USER_NIC create $lxcpath $lxcname $p1 veth usernic-br0 xx3"; then
	echo "FAIL: unable to create second nic"
	exit 1
fi

# Assign one more veth, should fail.
if run_cmd "$LXC_USER_NIC create $lxcpath $lxcname $p1 veth usernic-br0 xx4"; then
	echo "FAIL: able to create third nic"
	exit 1
fi

# Shut down and restart the container, should be able to assign more nics
run_cmd "lxc-stop -n b1 -k"
run_cmd "lxc-start -n b1 -d"
p1=$(run_cmd "lxc-info -n b1 -p -H")

if ! run_cmd "$LXC_USER_NIC create $lxcpath $lxcname $p1 veth usernic-br0 xx5"; then
	echo "FAIL: unable to create nic after destroying the old"
	cleanup 1
fi

run_cmd "lxc-stop -n b1 -k"

# Create a root-owned ns
lxc-create -t busybox -n usernic-c1
lxc-start -n usernic-c1 -d
p2=$(lxc-info -n usernic-c1 -p -H)

# assign veth to it - should fail
if run_cmd "$LXC_USER_NIC create $lxcpath $lxcname $p2 veth usernic-br0 xx6"; then
	echo "FAIL: able to attach nic to root-owned container"
	cleanup 1
fi

echo "All tests passed"
DONE=1
